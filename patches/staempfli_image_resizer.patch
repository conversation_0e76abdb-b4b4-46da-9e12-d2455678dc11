--- Model/Resizer.php 2020-03-31 16:21:42.000000000 +0200
+++ Model/Resizer.php 2021-04-27 08:42:29.000000000 +0200
@@ -141,7 +141,7 @@
             $this->initSize($width, $height);
             $this->initResizeSettings($resizeSettings);
         } catch (\Exception $e) {
-            $this->logger->addError("Staempfli_ImageResizer: could not find image: \n" . $e->getMessage());
+            $this->logger->error("Staempfli_ImageResizer: could not find image: \n" . $e->getMessage());
         }
         try {
             // Check if resized image already exists in cache
@@ -155,7 +155,7 @@
                 $resultUrl = $resizedUrl;
             }
         } catch (\Exception $e) {
-            $this->logger->addError("Staempfli_ImageResizer: could not resize image: \n" . $e->getMessage());
+            $this->logger->error("Staempfli_ImageResizer: could not resize image: \n" . $e->getMessage());
         }

         return $resultUrl;
