Index: vendor/firebear/importexport/Model/Import/AdvancedPricing.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/vendor/firebear/importexport/Model/Import/AdvancedPricing.php b/vendor/firebear/importexport/Model/Import/AdvancedPricing.php
--- a/vendor/firebear/importexport/Model/Import/AdvancedPricing.php	(date 1639462433171)
+++ b/vendor/firebear/importexport/Model/Import/AdvancedPricing.php	(date 1639462433171)
@@ -278,10 +278,6 @@
                     $this->addRowError(RowValidatorInterface::ERROR_SKU_IS_EMPTY, $rowNum);
                     continue;
                 }
-                if ($this->getErrorAggregator()->hasToBeTerminated()) {
-                    $this->getErrorAggregator()->addRowToSkip($rowNum);
-                    continue;
-                }
                 $rowSku = $rowData[self::COL_SKU];
                 $listSku[] = $rowSku;
                 if (!empty($rowData[self::COL_TIER_PRICE_WEBSITE])) {
