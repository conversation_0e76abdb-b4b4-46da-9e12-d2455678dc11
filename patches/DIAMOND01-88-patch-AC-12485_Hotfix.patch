From c508951016a27747979a7e1aaafccba15e095a39 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 7 Aug 2024 14:41:54 +0200
Subject: [PATCH] DIAMOND01-88 patch AC-12485_Hotfix

---
 .../module-jwt-user-token/Model/SecretBasedJwksFactory.php       | 1 +
 1 file changed, 1 insertion(+)

diff --git a/src/vendor/magento/module-jwt-user-token/Model/SecretBasedJwksFactory.php b/src/vendor/magento/module-jwt-user-token/Model/SecretBasedJwksFactory.php
index 5032db9..38af271 100644
--- a/module-jwt-user-token/Model/SecretBasedJwksFactory.php
+++ b/module-jwt-user-token/Model/SecretBasedJwksFactory.php
@@ -35,6 +35,7 @@ class SecretBasedJwksFactory
     public function __construct(DeploymentConfig $deploymentConfig, JwkFactory $jwkFactory)
     {
         $this->keys = preg_split('/\s+/s', trim((string)$deploymentConfig->get('crypt/key')));
+        $this->keys = [end($this->keys)];
         //Making sure keys are large enough.
         foreach ($this->keys as &$key) {
             $key = str_pad($key, 2048, '&', STR_PAD_BOTH);
--
2.34.1
