From 7788b80053587fff9dcee61a70af9225dff7e4e6 Mon Sep 17 00:00:00 2001
Date: Mon, 19 Jul 2021 09:26:01 -0500

---
 .../Paypal/Controller/Express/AbstractExpress/PlaceOrder.php     | 1 +
 1 file changed, 1 insertion(+)

diff --git a/vendor/magento/module-paypal/Controller/Express/AbstractExpress/PlaceOrder.php b/vendor/magento/module-paypal/Controller/Express/AbstractExpress/PlaceOrder.php
index 055af4162d5f3..3f4fafc110ae2 100644
--- a/vendor/magento/module-paypal/Controller/Express/AbstractExpress/PlaceOrder.php
+++ b/vendor/magento/module-paypal/Controller/Express/AbstractExpress/PlaceOrder.php
@@ -99,6 +99,7 @@ public function execute()
 
             // prepare session to success or cancellation page
             $this->_getCheckoutSession()->clearHelperData();
+            $this->_getSession()->unsQuoteId();
 
             // "last successful quote"
             $quoteId = $this->_getQuote()->getId();

From 92228b6c5c4c2b0d38052599fa042b981aa471b7 Mon Sep 17 00:00:00 2001
Date: Mon, 19 Jul 2021 13:26:44 -0500

---
 .../Paypal/Controller/Express/AbstractExpress/PlaceOrder.php   | 3 ++-
 1 file changed, 2 insertions(+), 1 deletion(-)

diff --git a/vendor/magento/module-paypal/Controller/Express/AbstractExpress/PlaceOrder.php b/vendor/magento/module-paypal/Controller/Express/AbstractExpress/PlaceOrder.php
index 3f4fafc110ae2..149a36f168b23 100644
--- a/vendor/magento/module-paypal/Controller/Express/AbstractExpress/PlaceOrder.php
+++ b/vendor/magento/module-paypal/Controller/Express/AbstractExpress/PlaceOrder.php
@@ -11,7 +11,8 @@
 use Magento\Paypal\Model\Api\ProcessableException as ApiProcessableException;
 
 /**
- * Class PlaceOrder
+ * Finalizes the PayPal order and executes payment
+ *
  * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
  */
 class PlaceOrder extends \Magento\Paypal\Controller\Express\AbstractExpress
