From 26e67d333038298c490607ce6999151c5e3b12a6 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 20 Feb 2023 16:27:30 +0100
Subject: [PATCH] PETCOWART-1218 PageBuilder video iframe lazy loading

---
 .../web/template/content-type/video/default/master.html       | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

diff --git a/vendor/magento/module-page-builder/view/adminhtml/web/template/content-type/video/default/master.html b/vendor/magento/module-page-builder/view/adminhtml/web/template/content-type/video/default/master.html
index 66859cdd..8a21709f 100644
--- a/vendor/magento/module-page-builder/view/adminhtml/web/template/content-type/video/default/master.html
+++ b/vendor/magento/module-page-builder/view/adminhtml/web/template/content-type/video/default/master.html
@@ -9,8 +9,8 @@
     <div class="pagebuilder-video-inner" attr="data.inner.attributes" ko-style="data.inner.style" css="data.inner.css">
         <div class="pagebuilder-video-wrapper" attr="data.wrapper.attributes" ko-style="Object.assign(data.wrapper.style(), {backgroundColor: data.wrapper.style().borderColor})" css="data.wrapper.css">
             <div class="pagebuilder-video-container" if="data.video.attributes().src">
-                <iframe frameborder="0" allowfullscreen attr="data.video.attributes" if="isHosted(data.video.attributes().src)"></iframe>
-                <video frameborder="0" controls="" attr="data.video.attributes" ifnot="isHosted(data.video.attributes().src)"></video>
+                <iframe frameborder="0" allowfullscreen attr="data.video.attributes" if="isHosted(data.video.attributes().src)" loading="lazy"></iframe>
+                <video frameborder="0" controls="" attr="data.video.attributes" ifnot="isHosted(data.video.attributes().src)" preload="metadata"></video>
             </div>
         </div>
     </div>
--
2.34.0

