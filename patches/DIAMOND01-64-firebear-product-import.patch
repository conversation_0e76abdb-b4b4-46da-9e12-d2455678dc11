commit 6c57fadbfa10241d9483b630d4b7689dc3e17c7e
Author: <PERSON> <<EMAIL>>
Date:   Mon Feb 13 17:01:07 2023 +0100

    DIAMOND01-64 Fix stock and prices updates

diff --git a/vendor/firebear/importexport/Model/Import/Product.php b/vendor/firebear/importexport/Model/Import/Product.php
index 2363d02..19286c7 100644
--- a/vendor/firebear/importexport/Model/Import/Product.php
+++ b/vendor/firebear/importexport/Model/Import/Product.php
@@ -5126,10 +5126,10 @@ class Product extends MagentoProduct
                     if ($this->onlyUpdate || $this->onlyAdd) {
                         if (!isset($oldSkus[$productSku]) && $this->onlyUpdate) {
                             $source->next();
-                            continue;
+                            continue 2;
                         } elseif (isset($oldSkus[$productSku]) && $this->onlyAdd) {
                             $source->next();
-                            continue;
+                            continue 2;
                         }
                     }
 
