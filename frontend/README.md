# Frontend Workflow v2

Our setup for compiling Sass, Javascript Files and work with Browsersync. You have also Webpack to work in modern
JavaScript Syntax.

Project repository and Full readme: [Frontend](https://gitlab.limesoda.com/limesoda/frontend/workflow)

## Requirements

Node v8.12.0 + Yarn 1.10.1

## Install Node Modules

Change to the Frontend directory for your project:

-   for TYPO3 and Magento: `/src/`
-   for WordPress: the theme directory
-   for custom software: `/src/` or any other directory that has been decide on for this project

Install it:

```
yarn
```

### Create the Browsersync configuration

- Inside the `frontend` directory, copy the `browser-sync.json.sample` and rename it to `browser-sync.json`
- Adapt the values according to your project and working copy

```
{
  "proxy": {
    "target": "na.www-zgonc-at.madl.vm-na2.limesoda.com"
  },
  "host": "*************",
  "port": 3003,
  "open": false,
  "cors": true
}
```

-   `proxy` is your project domain - only needed if you work on your project in a VM.
-   `port` is the port you want to use. If you use NFS shares, you can use any port. If you use a VM, make sure you open
    the port before (see [VM setup](#vm-setup)).
-   `host` your host system network IP - the IP your computer has inside the network, and not the IP of your VM or Docker
    setup.

## Usage

Use can run these Frontend tasks:

### Main tasks

```
yarn images [--theme=THEMENAME]
```

Optimises the images.

```
yarn serve [--theme=THEMENAME]
```

Starts Browsersync and webpack, enables sourcemaps, initialises the file watchers (which trigger reloads and linting
on file changes).

```
yarn serve:reload [--theme=THEMENAME]
```

**For multi config projects use `yarn serve:reload` rather than `yarn serve`.**

Does everything `yarn server` does but without hot module replacement from webpack.

```
yarn watch [--theme=THEMENAME]
```

Works like `yarn serve` but without Browsersync and hot module replacement from webpack.

```
yarn build [--theme=THEMENAME]
```

Compiles files for production usage (files are compressed, sourcemaps are omitted).

```
yarn test [--theme=THEMENAME]
```

Runs the test suite.

```
yarn watch:server [--theme=THEMENAME]
```

Works like `yarn watch` but with a simple node server. Ideal for PWA test cause browsersync is not ideal for PWA test. You can add Options in package.json script tag.   Here the  [http-server Options](https://github.com/indexzero/http-server#available-options)



### Sub tasks

```
yarn sprites [--theme=THEMENAME]
```

Generates a sprite image and a SASS file for the contained images.

```
yarn sasslint [--theme=THEMENAME]
```

Lints SASS files.

```
yarn stylelint [--theme=THEMENAME]
```

Lints CSS files (only in development mode).

```
yarn dev:css [--theme=THEMENAME]
```

Compiles CSS using SASS in development mode with sourcemaps.

```
yarn build:css [--theme=THEMENAME]
```

Compiles CSS using SASS in production mode.

```
yarn dev:js [--theme=THEMENAME]
```

Compiles and bundles JS using webpack in development mode with sourcemaps.

```
yarn build:js [--theme=THEMENAME]
```

Compiles and bundles JS using webpack in production mode.

```
yarn handlebars [--theme=THEMENAME]
```

Generates the HTML (if you're using handlebars).

```
yarn build:dev [--theme=THEMENAME]
```

Compiles CSS and JS for development mode.

## Additional config files

You can add additional configuration files to the project root.
Use the name scheme `config.*.json`.
That way multiple input and output paths for several extensions are handled.
To compile files only using one configuration, use the `--theme=THEMENAME` parameter for the cli where `THEMENAME` equals the corresponding configuration file identifier.
For this to work, the client name inside each configuration file has to be unique.

#### Example

The project has 2 configuration files `config.json` and `config.specialConfiguration.json`.
To compile only the additional configuration file use the following:

```
yarn serve --theme=specialConfiguration
```

## Customizing the config file

Open the file `config.json` in your Frontend source directory (normally `/src/` or any other directory you selected in `Custom`
projects) to further customize the config.

All paths defined in the file are relative to the Frontend source directory.

**Note**: Feel free to delete any file not related to your cms.

### Client

Self explaining.

```
{
   client: "limesoda"
}
```

### src section

Only change the settings explained here or prepare for trouble. ;-)

-   `sassFolder/lessfolder` Folder containint the .scss files.
-   `sass/less` as default you need only the main style.scss file. If you have many files, you can also use glob or add an
    array of files
-   `includePaths` if you want to include other libraries
-   `js` Javascript src root folder
-   `chunks -> main` as default you need 'main.js' where you import your modules
-   `chunks -> vendor` dynamically generated if you use packages from node modules.
-   `img` source for images to be optimised (Optional)
-   `externalLibrary, externals` if you must use an external library which is already included, you can import them with
    another alias, eg. use `$` for jquery (Optional)
-   `providePlugin, providePlugins` if you want to use plugins outside of webpack bundles, you can export them using these
    aliases
-   `sprites` path in the images folder to the pngs
-   `handlebars` Handlebar config only for html
-   `handlebars -> src` pages
-   `handlebars -> options -> batch` partials like header and footer

```
"src": {
   "root": "./resources/assets",
   "sassFolder/lessFolder": "./resources/assets/Scss/",
   "sass/less": [
       "./resources/assets/Scss/styles.scss"
   ],
   "includePaths": [
       "./node_modules/compass-mixins/lib",
       "./htdocs/skin/frontend/rwd/default/scss"
   ],
   "js": "./resources/assets/JavaScript/",
   "chunks": {
       "main": "./resources/assets/JavaScript/main.js"
   },
   "img": "./resources/assets/Images/",
   "externalLibrary": false,
   "externals": {
       "window": "window"
   },
   "providePlugin": false,
   "providePlugins": {
       "$": "jquery",
       "jQuery": "jquery",
       "window.jQuery": "jquery"
   },
   "sprite": [
       {
           "name": "sprites",
           "input": "sprites",
           "outputImg": "sprite",
           "outputScss": "/modules"
       }
   ],
   "handlebars": {
       "src": "./resources/assets/Pages/*.hbs",
       "data": {},
       "options": {
           "batch": [
               "./resources/assets/Pages/Partials"
           ]
       },
       "rename": {
           "extname": ".html"
       }
   }
},
```

### dist section

-   `root` destination folder only for handlebars
-   `css` css destination folder
-   `js` js destination folder
-   `img` images destination folder
-   `sprite` sprite destination folder

```
"dist": {
   "root": "./public",
   "css": "./public/Css",
   "js": "./public/JavaScript",
   "img": "./public/Images",
   "sprite": [
       {
           "output": "sprite"
       }
   ]
},
```

### clean section

-   `css` css - clean folder
-   `js` js - clean folder
-   `img` images - clean folder

**Note**: a `!` at the beginning of a line negates the expresion (= don't delete files matching this pattern)

```
"clean": {
    "css": [
        "./public/Css/**/*.css",
        "!./public/Css"
    ],
    "js": [
        "**/*",
        "!.gitignore",
        "!jquery-ui.min.js",
        "!jquery.homePageSlider.js",
        "!jquery.mCustomScrollbar.concat.min.js",
        "!jquery.rwdImageMaps.min.js",
        "!jquery.tinyscrollbar.js",
        "!jquery.tinyscrollbar.min.js",
        "!productview.js",
        "!remodal.min.js"],
    "img": [
        "./public/Images/**",
        "!./public/Images",
        "!./public/Images/.gitignore"
    ]
},
```

### linters section

Paths that the linters are checking.

```
"linter": {
   "sass": [
       "./resources/assets/Scss/**/*.scss"
   ],
   "css": [
       "./public/**/*.css"
   ]
},
```

### watch section

Watch for Files.

If one of them is enabled, then these paths will be watched for changes. If watchers don't work as expected, look here
first.

-   `singleSass` only this main sass file will be compiled by default | options: false | string

```
"watch": {
   "sass": "./resources/assets/Scss/**/*.scss",
   "js": "./resources/assets/JavaScript/**/*.js",
   "html": "./public/**/*.html",
   "hbspages": "./resources/assets/Pages/**/*.hbs",
   "php": "./resources/assets/**/*.php",
   "singleSass": "styles.scss",
   "css": [
       "./public/Css/main.css"
   ]
},
```

### jest section

jest is used for testing.

In these folders, jest uses files ending with `*.test.js`.

```
"jest": [
       "./resources/assets",
       "./frontend/test"
   ],
```

### purge section

Optimises CSS by deleting all code that is not necessary/used.

-   `purgeList`: folders containing the code (can also be JS)
    -   In the `frontend/purgeHtml/` folder, you can save HTML templates which wouldn't be covered otherwise (e.g. make
        sure that CSS from dynamically generated HTML is not deleted)
-   `purgeWhiteList`: don't delete these CSS classes
-   `purgePattern`: don't delete CSS classes beginning with these strings

```
"purge": {
   "purgeList": [
       "./frontend/purgeHtml/*.html",
       "./public/**/*.html",
       "./resources/assets/**/*.js"
   ],
   "purgeWhiteList": [
       "random"
   ],
   "purgePattern": [
       "js--",
       "ls--",
       "is--"
   ]
},
```

### Additional config section

-   `usepolling` use true if you work n VM
-   `publicJavascript` public Javascript folder for webpack
-   `enableHandlebars` enable handlebar only for html templates
-   `csslint`: enable or disable CSS linting
-   `enablePurgecss`: enable or disable CSS purging
-   `enable`:
    -   enable either `vue` or `react`, not both!
    -   `watch` enable or disable file watching
    -   `clean` enable or disable cleaning of images and CSS

```
"usepolling": false,
"publicJavascript": "/JavaScript/",
"enableHandlebars": true,
"csslint": false,
"enablePurgecss": true,
"enable": {
    "sasshash": false,
    "vue": false,
    "react": true,
    "watch": {
        "css": true,
        "js": true,
        "html": true,
        "php": true
    },
    "clean": {
        "images": true,
        "css": true
    },
    "webpack": {
        "hmtl": false,
        "chunkhash": true,
        "cssExtract": true
    },
    "server": {
        "historyApi": true
    }
}
```

## Javascript testing with Jest

-   In the folder 'test' are example tests
-   you can create for each javascript file a test file - e.g main.js -> main.test.js. Jest will take every file with the pattern '.test.js'
-   for build and deployment - webpack will ignore the test files

Run the script:

```
yarn test
```


## Eslint Browsersupport Configuration

if you have a Polyfill or you want disable the error do the following steps

Search the feature on caniuse.com.
Get the link to the feature (click on the # icon)

Add the id to the feature in the url polyfills in your .eslintrc. For example, the url for the caniuse webassembly page is http://caniuse.com/#feat=serviceworkers. The id for this feature is wasm. Example:

```
// ...
settings: {
    polyfills: ['serviceworkers']
    }
}

```

## Current Browsersupport in this Project

Here our Browser-Support-Policy in KM https://km.limesoda.com/marktanteile/

Check which Browser are supported at now http://gs.statcounter.com/browser-version-market-share/all/austria

When the supported browser versions change, please update the main README.md of the project.

### For Developers: 

-> change the browserlist in package.json. See above `Supported Browsers:` which are supported in this Project
```
example

"browserslist": [
    "Chrome >= 72",
    "ChromeAndroid >= 72",
    "Firefox >= 65",
    "iOS >=12",
    "IE >= 11"
],


```


test your list here https://browserl.ist/
```
example

Chrome >= 72, ChromeAndroid >= 72, Firefox >= 65, iOS >=12, IE >= 11

```




### Additional Magento 2 Setup

- inside your VM/DOCKER run in `src` folder follwoing command
This will create the less Files in your pub/static/frontend~theme folder. You have to run this command again if you delete the pub/static/frontend folder


```
yarn deploy
```
- if you work locally you have to create a symlink from `/var/www/projects/` to your local projects folder
from your root local. create the folders if  doesnt exist. See example below

```
cd /var/
sudo mkdir www
cd www
sudo ln -s /Volumes/VMS/wwwvms/projects72 projects

```
- if you Work inside your VM/Docker
    * Node 12 and yarn has to be installed
    * set `usePolling: true` in your `src/config.json`
    * forward ports for Browsersync [VM Setup](#vm-setup)  
    * use the same ports in your  [Browsersync config](#create-the-browsersync-configuration) 

- there are example js/css files in your `app/frontend/{vendor}/{theme}` folder. 
    `Info` Everything what is custom is under `Resources` folder
    Overriding Parent Tmeme `Blank or luma` is under `web/css/source` folder. See Magento docs for further information https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-guide/css_quick_guide_approach.html

- See the main Task to work with [Main tasks](#main-tasks) 

### VM Setup

**Note**: This step is required once per VM. On Mac or Ubuntu with NFS share you can skip this step.

-   open your `config-custom.yaml`
-   after the line `private_network` add the lines below to open the ports between host and VM:

```
forwarded_port:
     browser_sync:
          host: '3000'
          guest: '3000'
          auto_correct: true
     browser_sync_ui:
          host: '3001'
          guest: '3001'
          auto_correct: true
```

**Attention**: Use only spaces not tabs.

-   Restart your VM: `vagrant reload`

More information can be found in the [complete browsersync VM documentation](http://vms.limesoda.com/docs/current/setup/configuration/browsersync).

