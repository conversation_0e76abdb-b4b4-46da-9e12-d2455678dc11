const functions = require('./functions').functions;
const reverseString = require('./functions').reverseString;
const chunkArray = require('./functions').chunkArray;
const isAnagram = require('./functions').isAnagram;
const toggle = require('./functions').toggle;

// Output has to be
test('Should be equal 4', () => {
    expect(functions.add(2, 2)).toBe(4);
});

// Output has not to be
test('Should be NOT equal 5', () => {
    expect(functions.add(2, 2)).not.toBe(5);
});

// if Output is null
test('Should be null', () => {
    expect(functions.isNull()).toBeNull();
});

// if Output is falsy
test('Should be falsy', () => {
    expect(functions.checkValue(undefined)).toBeFalsy();
});

// if Output is Truthy
test('Should be Truthy', () => {
    expect(functions.checkValue('elem')).toBeTruthy();
});

// if Output is toEqual
test('Object equal -> Should be Naci Akce  Object ', () => {
    expect(functions.createUser('Naci', 'Akce')).toEqual({
        firstName: 'Naci',
        LastName: 'Akce'
    });
});

// Less and greater than
test('Should be under 1400', () => {
    const load1 = 1000;
    const load2 = 300;
    expect(load1 + load2).toBeLessThan(1400);
});

test('Should be under or equal 1400', () => {
    const load1 = 1000;
    const load2 = 400;
    expect(load1 + load2).toBeLessThanOrEqual(1400);
});

// Regex
test('Regex match -> There is no "N" in Team', () => {
    expect('team').not.toMatch(/N/);
});

// Arrays
test('Array contains -> Naci should be in usernames', () => {
    const usernames = ['Naci', 'Karen', 'Udo'];
    expect(usernames).toContain('Naci');
});

//  Promise async data
// test('Promise -> User fetched name should be Leanne Graham', () => {
//     expect.assertions(1);
//     return functions.fetchUser().then(data => {
//         expect(data.name).toEqual('Leanne Graham');
//     });
// });

//  async await
// test('Async Await -> User fetched name should be Leanne Graham', async () => {
//     expect.assertions(1);
//     const data = await functions.fetchUser();
//     expect(data.name).toEqual('Leanne Graham');
// });

describe('Grouped reverseString Tests', () => {
    // function exist
    test('reverseString function exist', () => {
        expect(reverseString).toBeDefined();
    });

    // string reverse
    test('string reverse', () => {
        expect(reverseString('Hello')).toEqual('olleH');
    });
});

// examples
describe('Grouped chunkArray Tests', () => {
    test('function exist ', () => {
        expect(typeof chunkArray).toEqual('function');
    });
    test('Chunk an array of 10 values with length of 2', () => {
        const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        const length = 2;
        const chunked = chunkArray(numbers, length);

        expect(chunked).toEqual([[1, 2], [3, 4], [5, 6], [7, 8], [9, 10]]);
    });
});
// examples
// function exist
describe('Grouped Anagram Tests', () => {
    test('function exist ', () => {
        expect(typeof isAnagram).toEqual('function');
    });

    test('cinema is an anagram of iceman', () => {
        expect(isAnagram('cinema', 'iceman')).toBeTruthy();
    });

    test('cinema is not an anagram of naci', () => {
        expect(isAnagram('cinema', 'naci')).toBeFalsy();
    });
});

// test DomManipulation
/**
 * Simulate a click event.
 * @public
 * @param {Element} elem  the element to simulate a click on
 */

const simulateClick = function(elem) {
    // Create our event (with options)
    const evt = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
    });
    // If cancelled, don't dispatch our event
    const canceled = !elem.dispatchEvent(evt);
};

describe('Grouped Dom Manipulation', () => {
    it('Should Element exist', function() {
        document.body.innerHTML =
            '<div>' +
            '  <span class="container" />' +
            '  <button id="button" />' +
            '</div>';
        expect(document.querySelectorAll('.container').length).toBe(1);
    });

    it('if function exist', function() {
        expect(typeof toggle).toEqual('function');
    });

    it('return if one of  selectors not exist', function() {
        expect(toggle('.containers', '.buttons')).not.toBeTruthy();
    });

    it('add active class and remove active class', function() {
        document.body.innerHTML =
            '<div>' +
            '  <span class="container" />' +
            '  <button class="button" />' +
            '</div>';

        const elem = document.querySelector('.container');
        const button = document.querySelector('.button');

        toggle('.container', '.button');
        simulateClick(button);
        expect(elem.classList.contains('active')).toBeTruthy();
        simulateClick(button);
        expect(elem.classList.contains('active')).not.toBeTruthy();
    });
});
