import 'core-js/stable';
import 'regenerator-runtime/runtime';

const axios = require('axios');

const functions = {
    add: (x, y) => x + y,
    isNull: () => null,
    checkValue: x => x,
    createUser: (first, last) => {
        const user = {
            firstName: first,
            LastName: last
        };
        return user;
    },
    fetchUser: () =>
        axios
            .get('https://jsonplaceholder.typicode.com/users/1')
            .then(res => res.data)
            .catch(err => 'error')
};

// reverse String
const reverseString = str => {
    return str
        .split('')
        .reverse()
        .join('');
};

// chunks example
const chunkArray = (arr, len) => {
    // Init chunked arr
    const chunkedArr = [];

    // Loop through arr
    arr.forEach(val => {
        // Get last element
        const last = chunkedArr[chunkedArr.length - 1];

        // Check if last and if last length is equal to the chunk len
        if (!last || last.length === len) {
            chunkedArr.push([val]);
        } else {
            last.push(val);
        }
    });

    return chunkedArr;
};

function isAnagram(str1, str2) {
    return formatStr(str1) === formatStr(str2);
}

// Helper function
function formatStr(str) {
    return str
        .replace(/[^\w]/g, '')
        .toLowerCase()
        .split('')
        .sort()
        .join('');
}

// add eventLister and a active Class on Click
function toggle(container, toggle) {
    const elem = document.querySelector(container);
    const button = document.querySelector(toggle);
    if (!button || !elem) return false;
    const toggleActive = () => {
        elem.classList.toggle('active');
    };
    button.addEventListener('click', toggleActive);
}

module.exports = {
    functions,
    reverseString,
    chunkArray,
    isAnagram,
    toggle
};
