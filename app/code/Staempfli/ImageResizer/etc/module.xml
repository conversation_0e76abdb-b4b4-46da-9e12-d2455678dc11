<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * module.xml
 *
 * @copyright Copyright (c) 2016 Staempfli AG
 * <AUTHOR>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Module/etc/module.xsd">
    <module name="Staempfli_ImageResizer" setup_version="0.0.1" >
        <sequence>
            <module name="Magento_Backend"/>
            <module name="Magento_PageCache"/>
            <module name="Magento_CacheInvalidate"/>
        </sequence>
    </module>
</config>