parameters:
    git_dir: .
    bin_dir: ../../../../bin
    tasks:
      phpcsfixer:
          config_file: .
          config: default
          fixers: [-psr0]
          level: psr2
          verbose: true
      composer:
          file: ./composer.json
          no_check_all: false
          no_check_lock: false
          no_check_publish: false
          with_dependencies: false
          strict: false
      git_blacklist:
          keywords:
              - "die("
              - "var_dump("
              - "exit;"
              - "console.log("
      phpcs:
          standard: "../../magento-ecg/coding-standard/EcgM2/"
          show_warnings: true
          tab_width: 4
          ignore_patterns: [test]
          sniffs: []
