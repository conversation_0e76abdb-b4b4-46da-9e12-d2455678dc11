<?php
/**
 * Cache
 *
 * @copyright Copyright (c) 2016 Staempfli AG
 * <AUTHOR>
 */

namespace Staempfli\ImageResizer\Model;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;

class Cache
{
    /**
     * @var Filesystem\Directory\WriteInterface
     */
    protected $mediaDirectory;

    /**
     * Cache constructor.
     * @param Filesystem $filesystem
     */
    public function __construct(Filesystem $filesystem)
    {
        $this->mediaDirectory = $filesystem->getDirectoryWrite(DirectoryList::MEDIA);
    }

    /**
     * Delete Image resizer cache dir
     */
    public function clearResizedImagesCache()
    {
        $this->mediaDirectory->delete(Resizer::IMAGE_RESIZER_CACHE_DIR);
    }
}
