<?xml version="1.0" encoding="UTF-8"?>
<csp_whitelist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Csp:etc/csp_whitelist.xsd">
    <policies>
        <policy id="img-src">
            <values>
                <value id="app_user_centrics" type="host">app.usercentrics.eu</value>
                <value id="diamond_aircrafts_live" type="host">www.diamondaircraft.com</value>
            </values>
        </policy>
        <policy id="connect-src">
            <values>
                <value id="privacy_proxy_user_centrics" type="host">privacy-proxy.usercentrics.eu</value>
                <value id="app_user_centrics" type="host">app.usercentrics.eu</value>
                <value id="api_user_centrics" type="host">api.usercentrics.eu</value>
                <value id="graphql_user_centrics" type="host">graphql.usercentrics.eu</value>
            </values>
        </policy>
        <policy id="script-src">
            <values>
                <value id="privacy_proxy_user_centrics" type="host">privacy-proxy.usercentrics.eu</value>
                <value id="app_user_centrics" type="host">app.usercentrics.eu</value>
            </values>
        </policy>
    </policies>
</csp_whitelist>
