<?php

declare(strict_types=1);

namespace Diamond\Catalog\Block;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Api\Data\WebsiteInterface;

/**
 * Block to add custom product badges
 */
class ProductBadges extends Template
{

    /**
     * @var Registry
     */
    protected $coreRegistry = null;
    /**
     * @var ProductInterface
     */
    private $product = null;

    /**
     * @param Context $context
     * @param Registry $registry
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        array $data = []
    ) {
        $this->coreRegistry = $registry;
        parent::__construct($context, $data);
    }

    /**
     * @return ProductInterface
     */
    public function getProduct(): ProductInterface
    {
        if (!$this->product) {
            $this->product = $this->coreRegistry->registry('product');
        }
        return $this->product;
    }

    /**
     * @param ProductInterface $product
     * @return $this
     */
    public function setProduct(ProductInterface $product)
    {
        $this->product = $product;
        return $this;
    }

    /**
     * @param ProductInterface $product
     * @return bool
     */
    private function isSimpleProductForSale($product): bool
    {
        return (bool)$product->getSpecialPrice() && $this->_localeDate->isScopeDateInInterval(
                WebsiteInterface::ADMIN_CODE,
                $product->getSpecialFromDate(),
                $product->getSpecialToDate()
            );
    }

    /**
     * @return bool
     */
    public function isProductForSale(): bool
    {
        $isProductForSale = false;
        if ($this->getProduct()->getTypeId() === Configurable::TYPE_CODE) {
            /* @var $configurableType Configurable */
            $configurableType = $this->getProduct()->getTypeInstance();
            $children = $configurableType->getUsedProducts($this->getProduct());
            foreach ($children as $child) {
                $isProductForSale = $isProductForSale === true ? $isProductForSale : $this->isSimpleProductForSale($child);
            }
        } else {
            $isProductForSale = $this->isSimpleProductForSale($this->getProduct());
        }
        return $isProductForSale;
    }

    /**
     * @return bool
     */
    public function isProductNew(): bool
    {
        return ($this->getProduct()->getNewsFromDate() !== null || $this->getProduct()->getNewsToDate() !== null) && $this->_localeDate->isScopeDateInInterval(
                WebsiteInterface::ADMIN_CODE,
                $this->getProduct()->getNewsFromDate(),
                $this->getProduct()->getNewsToDate()
            );
    }
}
