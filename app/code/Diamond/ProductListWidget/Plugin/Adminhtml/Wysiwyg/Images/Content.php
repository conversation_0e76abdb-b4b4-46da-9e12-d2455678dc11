<?php

namespace Diamond\ProductListWidget\Plugin\Adminhtml\Wysiwyg\Images;

use Magento\Cms\Block\Adminhtml\Wysiwyg\Images\Content as ImageContent;
use Magento\Framework\App\RequestInterface;

class Content
{
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    private $request;

    /**
     * Content constructor.
     *
     * @param RequestInterface $request
     */
    public function __construct(RequestInterface $request)
    {
        $this->request = $request;
    }


    /**
     * @param ImageContent $subject
     * @param $result
     *
     * @return string
     */
    public function afterGetOnInsertUrl(ImageContent $subject, $result)
    {
        $onInsertUrl = $this->request->getParam('on_insert_url');

        return $onInsertUrl ? urldecode($onInsertUrl) : $result;
    }
}
