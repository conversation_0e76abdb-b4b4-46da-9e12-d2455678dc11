<?xml version="1.0" encoding="UTF-8"?>
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:helloworld:Magento_Widget:etc/widget.xsd">
    <widget id="products_list" class="Magento\CatalogWidget\Block\Product\ProductsList" is_email_compatible="true"
            placeholder_image="Magento_CatalogWidget::images/products_list.png" ttl="86400">
        <label translate="true">Catalog Products List</label>
        <description translate="true">List of Products</description>
        <parameters>
            <parameter name="title" xsi:type="text" required="false" visible="true" sort_order="10">
                <label translate="true">Title</label>
            </parameter>
            <parameter name="show_pager" xsi:type="select" visible="true"
                       source_model="Magento\Config\Model\Config\Source\Yesno" sort_order="20">
                <label translate="true">Display Page Control</label>
            </parameter>
            <parameter name="products_per_page" xsi:type="text" required="true" visible="true" sort_order="30">
                <label translate="true">Number of Products per Page</label>
                <depends>
                    <parameter name="show_pager" value="1" />
                </depends>
                <value>5</value>
            </parameter>
            <parameter name="products_count" xsi:type="text" required="true" visible="true" sort_order="40">
                <label translate="true">Number of Products to Display</label>
                <value>10</value>
            </parameter>
            <parameter name="template" xsi:type="select" required="true" visible="true" sort_order="50">
                <label translate="true">Template</label>
                <options>
                    <option name="default" value="Magento_CatalogWidget::product/widget/content/grid.phtml" selected="true">
                        <label translate="true">Products Grid Template</label>
                    </option>
                </options>
            </parameter>
            <parameter name="cache_lifetime" xsi:type="text" visible="true" sort_order="60">
                <label translate="true">Cache Lifetime (Seconds)</label>
                <description translate="true">
                    <![CDATA[Time in seconds between the widget updates.
                    <br/>If not set, equals to 86400 seconds (24 hours). To update widget instantly, go to Cache Management and clear Blocks HTML Output cache.
                    <br/>Widget will not show products that begin to match the specified conditions until cache is refreshed.]]>
                </description>
            </parameter>
            <parameter name="condition" xsi:type="conditions" visible="true" required="true" sort_order="70"
                       class="Magento\CatalogWidget\Block\Product\Widget\Conditions">
                <label translate="true">Conditions</label>
            </parameter>


            <parameter name="title_type" xsi:type="select" required="false" visible="true" sort_order="11">
                <label translate="true">Title Type</label>
                <options>
                    <option name="heading_one" value="heading_one" selected="true">
                        <label translate="true">Heading 1</label>
                    </option>
                    <option name="heading_two" value="heading_two">
                        <label translate="true">Heading 2</label>
                    </option>
                </options>
            </parameter>
            <parameter name="image" xsi:type="block" visible="true" sort_order="110">
                <label translate="true">Image</label>
                <description translate="true">Image</description>
                <block class="Diamond\ProductListWidget\Block\Adminhtml\Widget\ImageChooser">
                    <data>
                        <item name="button" xsi:type="array">
                            <item name="open" xsi:type="string">Choose Image...</item>
                        </item>
                    </data>
                </block>
            </parameter>
            <parameter name="image_title" xsi:type="text" visible="true" sort_order="120">
                <label translate="true">Image Title</label>
            </parameter>
            <parameter name="image_position" xsi:type="select" required="false" visible="true" sort_order="130">
                <label translate="true">Image Position</label>
                <options>
                    <option name="position_left" value="position_left" selected="true">
                        <label translate="true">Left</label>
                    </option>
                    <option name="position_right" value="position_right">
                        <label translate="true">Right</label>
                    </option>
                    <option name="position_above" value="position_above">
                        <label translate="true">Above</label>
                    </option>
                </options>
            </parameter>
        </parameters>
    </widget>
</widgets>
