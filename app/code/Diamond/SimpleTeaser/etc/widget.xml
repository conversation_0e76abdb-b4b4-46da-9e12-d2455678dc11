<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
	<widget id="slider" class="LimeSoda\Banner\Block\Slider\Widget" is_email_compatible="false"	placeholder_image="Magento_CatalogWidget::images/products_list.png" ttl="86400">
		<label translate="true">Teaser</label>
		<description translate="true">Teaser</description>
		<parameters>
			<parameter name="template" xsi:type="select" required="true" visible="true">
				<label translate="true">Template</label>
				<options>
					<option name="teaser-big" value="Diamond_SimpleTeaser::teaser/newsletter.phtml">
						<label translate="true">Newsletter</label>
					</option>
                    <option name="slider-main" value="Diamond_SimpleTeaser::teaser/slider-main.phtml">
						<label translate="true">Slider Main</label>
					</option>
				</options>
			</parameter>
		</parameters>
	</widget>
</widgets>
