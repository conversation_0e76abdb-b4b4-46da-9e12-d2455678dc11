<?php
$banners = $block->getBannerCollection();
$imageResizer = $block->getImageResizer();
?>

<div class="slider-main">
    <div class="slider-main__container">
        <div class="slider-main__slider slider-list">
            <?php foreach ($banners as $banner) : ?>
                <?php
                /** @var \Staempfli\ImageResizer\Model\Resizer $imageResizer */

                $imageUrl = $banner->getImageUrl();
                if ($imageResizer != null) {
                    $resizedImageUrlBig = $imageResizer->resizeAndGetUrl($imageUrl, 1920, 1048);
                    $resizedImageUrlDesktop = $imageResizer->resizeAndGetUrl($imageUrl, 1200, 655);
                    $resizedImageUrlTablet = $imageResizer->resizeAndGetUrl($imageUrl, 970, 529);
                    $resizedImageUrlMobile = $imageResizer->resizeAndGetUrl($imageUrl, 767, 419);
                    $resizedImageUrlMobileS = $imageResizer->resizeAndGetUrl($imageUrl, 370, 202);
                }
                ?>
                <div class="slider-main__item">
                    <div class="slider-main__content">
                        <?php if ($banner->getImageUrl()) : ?>
                            <?php $link = $block->escapeHtml($banner->getLink()); ?>
                            <?php if ($link): ?>
                                <a class="main-teaser__link" href="<?= $link ?>"/>
                            <?php endif; ?>
                            <img
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
                                alt="<?= $block->escapeHtml($banner->getName()); ?>"
                                data-src="<?= $block->escapeUrl($resizedImageUrlMobileS); ?>"
                                data-srcset="<?= $block->escapeUrl($resizedImageUrlMobile); ?> 767w, <?= $block->escapeUrl($resizedImageUrlTablet); ?> 970w, <?= $block->escapeUrl($resizedImageUrlDesktop); ?> 1200w, <?= $block->escapeUrl($resizedImageUrlBig); ?> 1400w"
                                class="lazyload slider-main__image">
                        <?php endif; ?>

                        <div class="slider-main__description">
                            <?php if ($banner->getHeading()) : ?>
                                <h1 class="slider-main__header">
                                    <?= $block->escapeHtml($banner->getHeading()); ?>
                                </h1>
                            <?php endif; ?>
                            <?php if ($banner->getLinkText() && $banner->getLink()) : ?>
                                <a href="<?= $block->escapeHtml($banner->getLink()); ?>" target="_self"
                                   class="slider-main__button btn-primary">
                                    <?= $block->escapeHtml($banner->getLinkText()); ?>
                                </a>
                            <?php endif; ?>
                        </div>

                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
