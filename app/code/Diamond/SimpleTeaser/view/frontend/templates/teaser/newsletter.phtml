<?php $banners = $block->getBannerCollection(); ?>
<div class="newsletter-banner">
    <div class="newsletter-banner__container">
        <?php $i = 0; ?>
        <?php foreach ($banners as $banner) : ?>
            <?php if ($i === 0) : ?>
                <div class="newsletter-banner__banner">
                    <?php if (($banner->getHeading() && $banner->getContent()) && $banner->getImageUrl()) : ?>
                    <?php if ($banner->getImageUrl()) : ?>
                        <div class="newsletter-banner__image-container">
                            <img
                                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
                                alt="<?= $block->escapeHtml($banner->getName()); ?>"
                                data-src="<?= $banner->getImageUrl(); ?>" class="lazyload newsletter-banner__image"
                                width="1400" height="400">
                        </div>
                    <?php endif; ?>
                    <div class="newsletter-banner__content-wrapper">
                        <?php if ($banner->getHeading()) : ?>
                            <h2 class="newsletter-banner__heading">
                                <?= $block->escapeHtml($banner->getHeading()); ?>
                            </h2>
                        <?php endif; ?>
                        <?php if ($banner->getContent()) : ?>
                            <p class="newsletter-banner__subtext">
                                <?= $block->escapeHtml($banner->getContent()); ?>
                            </p>
                        <?php endif; ?>
                        <?= $this->getLayout()->createBlock("Magento\Newsletter\Block\Subscribe")->setTemplate("Magento_Newsletter::subscribe.phtml")->toHtml(); ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php break ?>
            <?php endif; ?>
            <?php $i++ ?>
        <?php endforeach; ?>
    </div>
</div>
