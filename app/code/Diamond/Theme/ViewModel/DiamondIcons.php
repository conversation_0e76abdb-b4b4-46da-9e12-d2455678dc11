<?php
/**
 * Diamond Aircraft Custom Icons ViewModel
 */

declare(strict_types=1);

namespace Diamond\Theme\ViewModel;

use Hyva\Theme\ViewModel\SvgIcons;

/**
 * Diamond Aircraft custom icons view model
 * 
 * Usage in templates:
 * $diamondIcons = $viewModels->require(DiamondIcons::class);
 * echo $diamondIcons->aircraftHtml('w-6 h-6');
 */
class DiamondIcons extends SvgIcons
{
    /**
     * DiamondIcons constructor.
     * 
     * @param \Magento\Framework\View\Asset\Repository $assetRepository
     * @param \Magento\Framework\View\DesignInterface $design
     * @param \Magento\Framework\App\CacheInterface $cache
     * @param \Magento\Framework\Math\Random $mathRandom
     * @param string $iconPathPrefix
     * @param array $pathPrefixMapping
     * @param string $iconSet
     */
    public function __construct(
        \Magento\Framework\View\Asset\Repository $assetRepository,
        \Magento\Framework\View\DesignInterface $design,
        \Magento\Framework\App\CacheInterface $cache,
        \Magento\Framework\Math\Random $mathRandom,
        string $iconPathPrefix = 'Hyva_Theme::svg/diamond',
        array $pathPrefixMapping = [],
        string $iconSet = ''
    ) {
        parent::__construct(
            $assetRepository,
            $design,
            $cache,
            $mathRandom,
            $iconPathPrefix,
            $pathPrefixMapping,
            $iconSet
        );
    }
}
