define([
    'uiComponent',
    'ko',
    'Magento_Customer/js/customer-data',
    'mage/translate'
], function(Component, ko, customerData, $t) {
    'use strict';
    return Component.extend({

        initialize: function () {
            this._super();
        },

        getTaxInfo: function () {
            let taxText = $t('*incl. VAT within the EU, excl. shipping.<br>VAT will be deducted at the checkout for deliveries to NON-EU countries.');

            if (this._isLoggedIn() != undefined && this._isTaxFreeGroup() == true) {
                taxText = $t('*Net price, excl. shipping.');
            }

            return taxText;
        },

        _isTaxFreeGroup: function () {
            let isTaxFreeGroup = false;
            let customerInfo = customerData.get('customer')();

            if (customerInfo.group_code == 'DAI-PS IC' || customerInfo.group_code == 'DAI-PS SP') {
                isTaxFreeGroup = true;
            }

            return isTaxFreeGroup;
        },

        _isLoggedIn: function() {
            var customerInfo = customerData.get('customer')();

            return customerInfo.firstname && customerInfo.fullname;
        }
    });
});
