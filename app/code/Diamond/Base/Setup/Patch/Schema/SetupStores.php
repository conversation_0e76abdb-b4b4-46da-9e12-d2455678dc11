<?php
declare(strict_types=1);

namespace Diamond\Base\Setup\Patch\Schema;

use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\Patch\SchemaPatchInterface;
use Magento\Store\Model\ResourceModel\Store;
use Magento\Store\Model\StoreFactory;
use Magento\Store\Model\GroupFactory;
use Magento\Store\Model\WebsiteFactory;


/**
 * Class CreateGermanStoreView
 * @package Diamond\Base\Setup\Patch\Schema
 *
 * Updates storename of default store and adds new german store
 * Although this patch adds data, it has to be a schema patch cause store updates require ddl operations which are only possible via schema patches
 */
class SetupStores implements SchemaPatchInterface
{
    /**
     * @var SchemaSetupInterface
     */
    private $schemaSetup;
    /**
     * @var ManagerInterface
     */
    private $eventManager;
    /**
     * @var GroupFactory
     */
    private $groupFactory;
    /**
     * @var StoreFactory
     */
    private $storeFactory;
    /**
     * @var Store
     */
    private $storeResourceModel;
    /**
     * @var WebsiteFactory
     */
    private $websiteFactory;

    /**
     * InstallData constructor.
     * @param SchemaSetupInterface $schemaSetup
     * @param ManagerInterface $eventManager
     * @param WebsiteFactory $websiteFactory
     * @param GroupFactory $groupFactory
     * @param StoreFactory $storeFactory
     * @param Store $storeResourceModel
     */
    public function __construct(
        SchemaSetupInterface $schemaSetup,
        ManagerInterface $eventManager,
        WebsiteFactory $websiteFactory,
        GroupFactory $groupFactory,
        StoreFactory $storeFactory,
        Store $storeResourceModel
    ) {
        $this->schemaSetup = $schemaSetup;
        $this->eventManager = $eventManager;
        $this->websiteFactory = $websiteFactory;
        $this->groupFactory = $groupFactory;
        $this->storeFactory = $storeFactory;
        $this->storeResourceModel = $storeResourceModel;
    }

    /**
     * {@inheritdoc}
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public function apply()
    {
        $this->schemaSetup->startSetup();

        $stores = [
            [
                'website_code' => 'base',
                'group_code' => 'main_website_store',
                'store_code' => 'en',
                'store_name' => 'English',
                'is_active' => '1'
            ],
            [
                'website_code' => 'base',
                'group_code' => 'main_website_store',
                'store_code' => 'de',
                'store_name' => 'Deutsch',
                'is_active' => '1'
            ]
        ];

        foreach ($stores as $storeData) {
            /** @var  \Magento\Store\Model\Store $store */
            $store = $this->storeFactory->create();
            $store->load($storeData['store_code']);

            /** @var \Magento\Store\Model\Website $website */
            $website = $this->websiteFactory->create();
            $website->load($storeData['website_code']);

            /** @var \Magento\Store\Model\Group $group */
            $group = $this->groupFactory->create();
            $group->load($storeData['group_code']);
            $group->getResource()
                ->load($group, $storeData['group_code'], 'code');

            $store->setCode($storeData['store_code']);
            $store->setName($storeData['store_name']);
            $store->setWebsite($website);
            $store->setGroup($group);
            $store->setData('is_active', $storeData['is_active']);

            $this->storeResourceModel->save($store);
            $this->eventManager
                ->dispatch('store_add', ['store' => $store]);
        }
        $this->schemaSetup->endSetup();
    }

    /**
     * {@inheritdoc}
     */
    public function getAliases(): array
    {
        return [];
    }

}
