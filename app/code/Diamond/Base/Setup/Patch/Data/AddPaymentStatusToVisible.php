<?php
declare(strict_types=1);

namespace Diamond\Base\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Sales\Model\Order\Status;

class AddPaymentStatusToVisible implements DataPatchInterface
{
    /**
     * @var Status
     */
    private $paymentStatus;

    /**
     * AddPaymentStatusToVisible constructor.
     * @param Status $paymentStatus
     */
    public function __construct(Status $paymentStatus)
    {
        $this->paymentStatus = $paymentStatus;
    }

    /**
     * @return AddPaymentStatusToVisible|void
     * @throws \Exception
     */
    public function apply()
    {
        $status = $this->paymentStatus->load('pending_payment');
        if($status !== null) {
            $status->assignState('pending_payment', 1, 1);
        }
    }

    /**
     * @return array
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return array
     */
    public function getAliases(): array
    {
        return [];
    }
}
