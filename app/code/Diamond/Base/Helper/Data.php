<?php
declare(strict_types=1);

namespace Diamond\Base\Helper;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\App\Helper\AbstractHelper;

class Data extends AbstractHelper
{
    /**
     * Default store code
     */
    const STORE_CODE_DEFAULT = 'default';
    /**
     * German store code
     */
    const STORE_CODE_GERMAN = 'german';


    /**
     * @var \Magento\Store\Api\StoreRepositoryInterface
     */
    private $storeRepository;

    /**
     * Data constructor.
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Store\Api\StoreRepositoryInterface $storeRepository
     */
    public function __construct
    (
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Store\Api\StoreRepositoryInterface $storeRepository
    ) {
        $this->storeRepository = $storeRepository;
        parent::__construct($context);
    }

    /**
     * Return store id by store code
     * @param string $code
     * @return mixed
     */
    public function getStoreIdByCode(string $code): ?int
    {
        try {
            $store = $this->storeRepository->get($code);
            return (int) $store->getId();
        } catch (NoSuchEntityException $e) {
            //return null;
        }
        return null;
    }

}
