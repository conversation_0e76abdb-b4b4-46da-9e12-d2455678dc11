# Usage

Call the magento shell script php bin/magento from command line with desired action.
Make sure you are in the magento root folder when executing the command.

Available actions and parameters:

    - php bin/magento monitoring:serverload [1,5,15]
    - php bin/magento monitoring:cronjobs +int:warningMinutes +int:criticalMinutes
    - php bin/magento monitoring:diskspace +int:warningPercent +int:criticalPercent string:"Partition"
    - php bin/magento monitoring:countfiles +int:warningFilesCount +int:criticalFilesCount string:"Path to Directory"
    - php bin/magento monitoring:filesize +int:warningFileSize +int:criticalFileSize string:"Path to File" [true]
    - php bin/magento monitoring:fileage +int:warningFileAge(Minutes) +int:criticalFileAge(Minutes) string:"Path to File"
    - php bin/magento monitoring:databasetablesize +float:warningTableSize(Megabyte) +float:criticalTableSize(Megabyte)

## Server Load
> This check monitors the actual CPU utilization. You can choose whether the average of the last minute, the last 5 minutes or the last 15 minutes is spent. Only one value can be given per check. 
```example (php bin/magento monitoring:serverload 5)```

## Cron Job 
### Heartbeat (Time)
> This check monitors the last successfully executed cron job. The time after which you want to receive a warning is given in minutes.
```example (php bin/magento monitoring:cronjobs 30 50)```
 
### Status
> This check monitors the status of last cron job.
```example (php bin/magento monitoring:cronjobs 0 0 cronname --mode status)```

## Disk Space
> This check monitors the available space on the specified hard disk partition. The limits are expressed as an integer in percent.
```example (php bin/magento monitoring:diskspace 30 20 "/")```

## Count Files
> This check monitors the number of files in a directory. The limits are given as an integer.
```example (php bin/magento monitoring:filescount 20 30 "var/log")```

## File Size
> This check monitors the current size of the file in MB. The limits are given as an integer in MB.
```example (php bin/magento monitoring:filesize 20 30 "var/log/system.log")```

> You can reverse this check and monitor if a file has a minimum size, by passing in the optional parameter minSizeCheck with any value you want
> ```example (php bin/magento monitoring:filesize 40 20 "var/log/system.log" true)```
> The example will trigger a warning when filesize is below 40 MB and trigger a critical status when filesize is below 20 MB.

## File Age
> This check monitors the age of the specified file. The limits are given as an integer in minutes.
```example (php bin/magento monitoring:fileage 20 30 "var/log/system.log")```

## Database Table Size
> This check monitors the size of the specified tables in a database. The limits can be specified as floating point numbers in megabytes.
```example (php bin/magento monitoring:databasetablesize 0.05 0.1 "customer_log, report_event")```

# License and Copyright

LIMESODA Interactive Marketing GmbH
