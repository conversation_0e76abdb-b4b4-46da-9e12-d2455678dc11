<?php

namespace LimeSoda\Monitoring\Console;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class DiskSpace extends Command
{
    protected $monitoringStatus = 3;

    protected $warningLimit = null;

    protected $criticalLimit = null;

    protected $checkMode;

    protected function configure()
    {
        $this->setName('monitoring:diskspace');
        $this->addArgument('warning', InputArgument::REQUIRED,
            __('Type the Warning Limit for available Space in Percent')->render());
        $this->addArgument('critical', InputArgument::REQUIRED,
            __('Type the Critical Limit for available Space in Percent')->render());
        $this->addArgument('checkmode', InputArgument::REQUIRED,
            __('Type the Partition to be monitored')->render());
        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->warningLimit = intval($input->getArgument('warning'));
        $this->criticalLimit = intval($input->getArgument('critical'));
        $this->checkMode = $input->getArgument('checkmode');
        $ds = disk_total_space($this->checkMode);
        $df = disk_free_space($this->checkMode);
        $relativeFreeSpace = ((100 / $ds) * $df);

        if ($relativeFreeSpace >= $this->warningLimit) {
            $this->monitoringStatus = 0;
            $format
                = '%u;The available hard disk space is %g percent.|\'percent\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $relativeFreeSpace, $relativeFreeSpace));
            return $this->monitoringStatus;
        } elseif ($relativeFreeSpace < $this->warningLimit
            && $relativeFreeSpace >= $this->criticalLimit
        ) {
            $this->monitoringStatus = 1;
            $format
                = '%u;The available hard disk space is %g percent. Warning limit of %g percent exceeded.|\'percent\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $relativeFreeSpace, $this->warningLimit, $relativeFreeSpace));
            return $this->monitoringStatus;
        } elseif ($relativeFreeSpace < $this->criticalLimit) {
            $this->monitoringStatus = 2;
            $format
                = '%u;The available hard disk space is %g percent. Critical limit of %g percent exceeded.|\'percent\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $relativeFreeSpace, $this->criticalLimit, $relativeFreeSpace));
            return $this->monitoringStatus;
        } else {
            $this->monitoringStatus = 3;
            $format
                = '%u;The available space on the hard disk can not be shown for some unknown reason';
            $output->writeln(sprintf($format, $this->monitoringStatus));
            return $this->monitoringStatus;
        }
    }
}