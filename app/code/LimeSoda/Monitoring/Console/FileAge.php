<?php

namespace LimeSoda\Monitoring\Console;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FileAge extends Command
{
    protected $monitoringStatus = 3;

    protected $warningLimit = null;

    protected $criticalLimit = null;

    protected $checkMode;

    protected function configure()
    {
        $this->setName('monitoring:fileage');
        $this->addArgument('warning', InputArgument::REQUIRED,
            __('Type the Warning Limit for the FileAge')->render());
        $this->addArgument('critical', InputArgument::REQUIRED,
            __('Type the Critical Limit for the FileAge')->render());
        $this->addArgument('checkmode', InputArgument::REQUIRED,
            __('Type the File to be monitored')->render());
        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->warningLimit = intval($input->getArgument('warning'));
        $this->criticalLimit = intval($input->getArgument('critical'));
        $this->checkMode = $input->getArgument('checkmode');
        if (!is_readable($this->checkMode)) {
            return false;
        }
        $currentTime = new \DateTime();
        $fileMTime = new \DateTime(date("F d Y H:i:s",
            $this->getCorrectMTime($this->checkMode)));


        $fileAgeSeconds = $currentTime->getTimestamp()
            - $fileMTime->getTimestamp();
        $fileAgeMinutes = floatval(round($fileAgeSeconds / 60, 2,
            PHP_ROUND_HALF_UP));

        if ($fileAgeMinutes <= $this->warningLimit) {
            $this->monitoringStatus = 0;
            $format
                = '%u;The current File Age for %s is: %g Seconds.|\'Seconds\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $this->checkMode, $fileAgeSeconds, $fileAgeSeconds));
            return $this->monitoringStatus;
        } elseif ($fileAgeMinutes <= $this->criticalLimit) {
            $this->monitoringStatus = 1;
            $format
                = '%u;The current File Size for %s is: %g Seconds. Warning limit of %g Minutes exceeded.|\'Seconds\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $this->checkMode, $fileAgeSeconds, $this->warningLimit,
                $fileAgeSeconds));
            return $this->monitoringStatus;
        } elseif ($fileAgeMinutes > $this->criticalLimit) {
            $this->monitoringStatus = 2;
            $format
                = '%u;The current File Size for %s is: %g Seconds. Critical limit of %g Minutes exceeded.|\'Seconds\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $this->checkMode, $fileAgeSeconds, $this->criticalLimit,
                $fileAgeSeconds));
            return $this->monitoringStatus;
        } else {
            $this->monitoringStatus = 3;
            $format
                = '%u;The current File Age can not be shown for some unknown reason';
            $output->writeln(sprintf($format, $this->monitoringStatus));
            return $this->monitoringStatus;
        }
    }

    /**
     * @param $filePath
     *
     * @return bool|int
     */

    protected function getCorrectMTime($filePath)
    {
        $time = filemtime($filePath);

        $isDST = (date('I', $time) == 1);
        $systemDST = (date('I') == 1);

        $adjustment = 0;

        if ($isDST == false && $systemDST == true) {
            $adjustment = 3600;
        } else {
            if ($isDST == true && $systemDST == false) {
                $adjustment = -3600;
            } else {
                $adjustment = 0;
            }
        }
        return ($time + $adjustment);
    }
}