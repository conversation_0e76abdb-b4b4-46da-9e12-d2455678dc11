<?php
/**
 * Copyright © LimeSoda Interactive Marketing GmbH. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace LimeSoda\Monitoring\Console;

use LimeSoda\Monitoring\Helper\CronData;
use Magento\Cron\Model\Schedule;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CronJobs extends Command
{
    private const MOD = 'mod';
    private const STATUS_MOD = 'status';
    private const TIME_MOD = 'time';
    /** @var CronData */
    protected $cronHelper;
    /** @var integer */
    protected $monitoringStatus = 3;
    /** @var integer */
    protected $warningLimit = null;
    /** @var integer */
    protected $criticalLimit = null;
    /** @var string */
    protected $jobCode = null;

    /**
     * @param CronData $cronHelper
     */
    public function __construct(
        CronData $cronHelper
    ) {
        $this->cronHelper = $cronHelper;
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('monitoring:cronjobs');
        $this->setDescription("LimeSoda CronJobs Monitoring");
        $this->setHelp(
            "time (Default) - Check if the last successfull executed cronjob need less time than warning and critical\nstatus - Check if the last cronjob was executed successfull"
        );
        $this->addArgument(
            'warning',
            InputArgument::REQUIRED,
            __('Type the Warning Limit in Minutes (ignored in status mode)')->render()
        );
        $this->addArgument(
            'critical',
            InputArgument::REQUIRED,
            __('Type the Critical Limit in Minutes (ignored in status mode)')->render()
        );
        $this->addArgument(
            'jobCode',
            InputArgument::OPTIONAL,
            __('Type the job code')->render()
        );
        $this->addOption(
            self::MOD,
            'm',
            InputArgument::OPTIONAL,
            __('Select Mod (time or status)')->render()
        );
        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        // Select Mod
        $mod = $input->getOption(self::MOD) == 'status' || $input->getOption(self::MOD) == 's' ?
            self::STATUS_MOD : self::TIME_MOD;
        $this->jobCode = $input->getArgument('jobCode');

        $this->warningLimit = intval($input->getArgument('warning'));
        $this->criticalLimit = intval($input->getArgument('critical'));

        if ($mod == self::TIME_MOD) {
            $intervalSeconds = $this->cronHelper->compareLastSuccessToCurrent($this->jobCode);
            if ($intervalSeconds === null) {
                $this->monitoringStatus = 3;
                $format = '%u;Magento 2 no cron jobs found in time mode%s';
                $output->writeln(
                    sprintf(
                        $format,
                        $this->monitoringStatus,
                        $this->jobCode ? ' (' . $this->jobCode . ')' : ''
                    )
                );
                return $this->monitoringStatus;
            }
            $intervalMinutes = intval(round(($intervalSeconds / 60), 0, PHP_ROUND_HALF_UP));

            if ($intervalMinutes <= $this->warningLimit) {
                $this->monitoringStatus = 0;
                $format = '%u;Magento 2 scheduler heartbeat run %g seconds ago.|\'seconds\'=%g%s';
                $output->writeln(
                    sprintf(
                        $format,
                        $this->monitoringStatus,
                        $intervalSeconds,
                        $intervalSeconds,
                        $this->jobCode ? ' (' . $this->jobCode . ')' : ''
                    )
                );
            } elseif ($intervalMinutes > $this->warningLimit && $intervalMinutes <= $this->criticalLimit) {
                $this->monitoringStatus = 1;
                $format = '%u;Magento 2 scheduler heartbeat run %g seconds ago. Warning limit of %g Minutes exceeded.|\'seconds\'=%g%s';
                $output->writeln(
                    sprintf(
                        $format,
                        $this->monitoringStatus,
                        $intervalSeconds,
                        $this->warningLimit,
                        $intervalSeconds,
                        $this->jobCode ? ' (' . $this->jobCode . ')' : ''
                    )
                );
            } elseif ($intervalMinutes > $this->criticalLimit) {
                $this->monitoringStatus = 2;
                $format = '%u;Magento 2 scheduler heartbeat run %g seconds ago. Critical limit of %g Minutes exceeded.|\'seconds\'=%g%s';
                $output->writeln(
                    sprintf(
                        $format,
                        $this->monitoringStatus,
                        $intervalSeconds,
                        $this->criticalLimit,
                        $intervalSeconds,
                        $this->jobCode ? ' (' . $this->jobCode . ')' : ''
                    )
                );
            } else {
                $this->monitoringStatus = 3;
                $format = '%u;Magento 2 scheduler heartbeat has some unknown errors%s';
                $output->writeln(
                    sprintf(
                        $format,
                        $this->monitoringStatus,
                        $this->jobCode ? ' (' . $this->jobCode . ')' : ''
                    )
                );
            }
        } else {
            $status = $this->cronHelper->getStatusLastCronExecute($this->jobCode);
            if ($status === Schedule::STATUS_SUCCESS) {
                $this->monitoringStatus = 0;
                $format = '%u;Magento 2 last cron job executed correctly%s';
                $output->writeln(
                    sprintf(
                        $format,
                        $this->monitoringStatus,
                        $this->jobCode ? ' (' . $this->jobCode . ')' : ''
                    )
                );
            } elseif ($status === Schedule::STATUS_ERROR) {
                $this->monitoringStatus = 2;
                $format = '%u;Magento 2 last cron job executed failed%s';
                $output->writeln(
                    sprintf(
                        $format,
                        $this->monitoringStatus,
                        $this->jobCode ? ' (' . $this->jobCode . ')' : ''
                    )
                );
            } else {
                $this->monitoringStatus = 3;
                $format = '%u;Magento 2 no cron jobs found in status mode%s';
                $output->writeln(
                    sprintf(
                        $format,
                        $this->monitoringStatus,
                        $this->jobCode ? ' (' . $this->jobCode . ')' : ''
                    )
                );
            }
        }
        return $this->monitoringStatus;
    }
}
