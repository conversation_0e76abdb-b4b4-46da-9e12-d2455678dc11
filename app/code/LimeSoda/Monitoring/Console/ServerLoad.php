<?php

declare(strict_types=1);

namespace LimeSoda\Monitoring\Console;

use Magento\Framework\Shell;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ServerLoad extends Command
{
    protected $warningLimit = null;
    protected $criticalLimit = null;
    protected $checkMode = null;
    protected $numberOfCpus = null;
    protected $monitoringStatus = 3;
    protected $currentLoad = null;

    /**
     * @var Shell
     */
    private $shell;

    public function __construct(Shell $shell, $name = null)
    {
        $this->shell = $shell;
        $this->numberOfCpus = intval($this->getNumberOfCpu());
        parent::__construct($name);
    }

    public function configure()
    {
        $this->setName('monitoring:serverload');
        $this->setDescription('LimeSoda Server Load Monitoring');
        $this->addArgument('exp', InputArgument::REQUIRED, __('Type a number')->render());
        $this->addArgument('warning', InputArgument::OPTIONAL, __('Warning limit as float')->render());
        $this->addArgument('critical', InputArgument::OPTIONAL, __('Critical limit as float')->render());
        parent::configure();
    }

    public function execute(InputInterface $input, OutputInterface $output)
    {
        if (intval($input->getArgument('exp')) === 1
            || intval($input->getArgument('exp')) === 5
            || intval($input->getArgument('exp')) === 15
        ) {
            $this->checkMode = intval($input->getArgument('exp'));
        } else {
            return false;
        }

        if ($this->checkMode === 1) {
            $this->warningLimit = $this->getDefinedLimits(floatval($input->getArgument('warning')), 0.9);
            $this->criticalLimit = $this->getDefinedLimits(floatval($input->getArgument('critical')), 1);
            $this->currentLoad = round($this->getServerLoad($this->checkMode), 2);
        } elseif ($this->checkMode === 5) {
            $this->warningLimit = $this->getDefinedLimits(floatval($input->getArgument('warning')), 0.7);
            $this->criticalLimit = $this->getDefinedLimits(floatval($input->getArgument('critical')), 0.8);
            $this->currentLoad = round($this->getServerLoad($this->checkMode), 2);
        } elseif ($this->checkMode === 15) {
            $this->warningLimit = $this->getDefinedLimits(floatval($input->getArgument('warning')), 0.5);
            $this->criticalLimit = $this->getDefinedLimits(floatval($input->getArgument('critical')), 0.6);
            $this->currentLoad = round($this->getServerLoad($this->checkMode), 2);
        } else {
            $this->warningLimit = null;
            $this->criticalLimit = null;
        }

        if ($this->currentLoad <= $this->warningLimit
        ) {
            $this->monitoringStatus = 0;
            $output->writeln($this->monitoringStatus . ';The currently load average over the last ' . $this->checkMode . ' minutes is ' . $this->currentLoad . '|\'load average\'=' . $this->currentLoad);
            $output->writeln(json_encode($this->getProcessSnapshot('cpu'), JSON_PRETTY_PRINT));
            return $this->monitoringStatus;
        } elseif ($this->currentLoad <= $this->criticalLimit
        ) {
            $this->monitoringStatus = 1;
            $output->writeln($this->monitoringStatus . ';The currently load average over the last ' . $this->checkMode . ' minutes is ' . $this->currentLoad . '. Warning limit of ' . $this->warningLimit . ' Load Average exceeded.|\'load average\'=' . $this->currentLoad);
            $output->writeln(json_encode($this->getProcessSnapshot('cpu'), JSON_PRETTY_PRINT));
            return $this->monitoringStatus;
        } elseif ($this->currentLoad > $this->criticalLimit
        ) {
            $this->monitoringStatus = 2;
            $output->writeln($this->monitoringStatus . ';The currently load average over the last ' . $this->checkMode . ' minutes is ' . $this->currentLoad . '. Critical limit of ' . $this->criticalLimit . ' Load Average exceeded.|\'load average\'=' . $this->currentLoad);
            $output->writeln(json_encode($this->getProcessSnapshot('cpu'), JSON_PRETTY_PRINT));
            return $this->monitoringStatus;
        } else {
            $this->monitoringStatus = 3;
            $output->writeln($this->monitoringStatus . ';The currently load average over the last ' . $this->checkMode . ' minutes is unknown. Please Check your Settings.');
            return $this->monitoringStatus;
        }
    }

    /**
     * @param float|null $inputValue
     * @param float|null $defaultValue
     * @return float
     */
    private function getDefinedLimits(float $inputValue = null, float $defaultValue = null): float
    {
        if (isset($inputValue) && $inputValue > 0) {
            return $inputValue;
        } elseif (isset($defaultValue) && $defaultValue > 0) {
            return $this->numberOfCpus * $defaultValue;
        }
        return 0;
    }

    /**
     * Returns the current CPU load 1, 5, 15
     *
     *  Should work for Linux, Windows, Mac & BSD
     *
     * @return float
     */
    private function getServerLoad($exp): float
    {
        $loadAverage = sys_getloadavg();
        if ($exp === 1) {
            return $loadAverage[0];
        } elseif ($exp === 5) {
            return $loadAverage[1];
        } else {
            return $loadAverage[2];
        }
    }

    /**
     * @param string $sorting 'can be cpu or mem'
     * @return array
     */
    private function getProcessSnapshot($sorting)
    {
        if (isset($sorting) && ($sorting === 'mem' || $sorting === 'cpu')) {
            $command = 'ps -eo pid,ppid,cmd,%mem,%cpu --sort=-%' . $sorting . ' | head';
            $output = $this->shell->execute($command);
            return $output;
        } else {
            return [];
        }
    }

    /**
     * Returns the number of available CPU cores
     *
     *  Should work for Linux, Windows, Mac & BSD
     *
     * @return integer
     */
    private function getNumberOfCpu()
    {
        if (ini_get('open_basedir')) {
            $command = "cat /proc/cpuinfo | grep processor | wc -l";
            $result = $this->shell->execute($command);
            return (int)$result;
        } else {
            $numCpus = 1;
            if (is_file('/proc/cpuinfo')) {
                $cpuinfo = file_get_contents('/proc/cpuinfo');
                preg_match_all('/^processor/m', $cpuinfo, $matches);
                $numCpus = count($matches[0]);
            }
            return $numCpus;
        }
    }
}
