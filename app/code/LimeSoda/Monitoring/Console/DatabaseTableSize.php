<?php

namespace LimeSoda\Monitoring\Console;

use Symfony\Component\Console\Command\Command;
use Magento\Framework\App\ResourceConnection;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class DatabaseTableSize extends Command
{
    protected $monitoringStatus = 3;

    protected $warningLimit = null;

    protected $criticalLimit = null;

    protected $checkMode;

    protected $tablesToCheck;

    protected $resourceConnection;

    public function __construct(ResourceConnection $resourceConnection)
    {
        parent::__construct();
        $this->resourceConnection = $resourceConnection;

    }

    protected function configure()
    {
        $this->setName('monitoring:databasetablesize');
        $this->addArgument('warning', InputArgument::REQUIRED,
            __('Type the Warning Limit for TableSize')->render());
        $this->addArgument('critical', InputArgument::REQUIRED,
            __('Type the Critical Limit for TableSize')->render());
        $this->addArgument('checkmode', InputArgument::REQUIRED,
            __('Add the Tables to be monitored')->render());
        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->warningLimit = floatval($input->getArgument('warning'));
        $this->criticalLimit = floatval($input->getArgument('critical'));
        $this->checkMode = str_replace(" ", "",
            $input->getArgument('checkmode'));
        $this->tablesToCheck = explode(',', $this->checkMode);

        $databaseConnection = $this->resourceConnection->getConnection();
        $sizeInByte = 0;
        foreach ($this->tablesToCheck as $logTable) {
            $query = "SHOW TABLE STATUS LIKE '" . $logTable . "';";
            $results = $databaseConnection->fetchAll($query);
            $sizeInByte += $results[0]['Data_length']
                + $results[0]['Index_length'];
        }

        $sizeInKB = $sizeInByte / 1024;
        $sizeInMB = $sizeInKB / 1024;

        if ($sizeInMB <= $this->warningLimit) {
            $this->monitoringStatus = 0;
            $format
                = '%u;Total size of tables %s is %g Megabyte|\'Megabyte\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $this->checkMode, $sizeInMB, $sizeInMB));
            return $this->monitoringStatus;
        }
        if ($sizeInMB <= $this->criticalLimit
            && $sizeInMB > $this->warningLimit
        ) {
            $this->monitoringStatus = 1;
            $format
                = '%u;Total size of tables %s is %g Megabyte. Warning limit of %g Megabyte exceeded.|\'Megabyte\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $this->checkMode, $sizeInMB,
                $this->warningLimit, $sizeInMB));
            return $this->monitoringStatus;
        }
        if ($sizeInMB > $this->criticalLimit) {
            $this->monitoringStatus = 2;
            $format
                = '%u;Total size of tables %s is %g Megabyte. Critical limit of %g Megabyte exceeded.|\'Megabyte\'=%g';
            $output->writeln(sprintf($format, $this->monitoringStatus,
                $this->checkMode, $sizeInMB,
                $this->criticalLimit, $sizeInMB));
            return $this->monitoringStatus;
        }

        $this->resourceConnection->closeConnection();
    }
}