<?php


namespace LimeSoda\Monitoring\Console;

use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\User\Model\ResourceModel\User\Collection as AdminUser;
use Symfony\Component\Console\Style\SymfonyStyle;

class UserBackend extends Command
{
    /**
     * @var AdminUser
     */
    private $adminUser;

    /**
     * UserBackend constructor.
     * @param AdminUser $adminUser
     * @param null $name
     */
    public function __construct(
        AdminUser $adminUser,
        $name = null
    ) {
        $this->adminUser = $adminUser;
        parent::__construct($name);
    }

    /**
     * configure
     */
    protected function configure()
    {
        $this->setDescription('Show entries from the admin_users database table.');
        $this->setHelp('Prints a list of recent admin_users entries.' . PHP_EOL . 'If you want to get more detailed information, use the --verbose option.');
        $this->setName('user:backend');
        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void|null
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        $format = $input->hasOption('format') ? $input->getOption('format') : 'txt';

        $backendUsers = $this->getBackendUsers();

        if ($format === 'json') {
            $io->writeln(json_encode($backendUsers));
        } else {
            $io->title($this->getDescription());
            $table = new Table($output);
            $table->setHeaders([
                'Uid',
                'Username',
                'Firstname',
                'Lastname',
                'Email',
                'Enabled',
                'Role',
                'Last login',
            ]);

            foreach ($backendUsers as $user) {
                $table->addRow($user);
            }

            $table->render();
        }
    }

    /**
     * Reads out the backend users from the database.
     *
     * @return array
     */
    protected function getBackendUsers(): array
    {
        $backendUsers = [];

        foreach ($this->adminUser->getData() as $record) {
            $backendUsers[] = [
                'uid' => $record['user_id'],
                'username' => $record['username'],
                'firstname' => $record['firstname'],
                'lastname' => $record['lastname'],
                'email' => $record['email'],
                'is_active' => $record['is_active'],
                'role_name' => $record['role_name'],
                'lastlogin' => empty($record['logdate']) ? 'Never' : $record['logdate'],
            ];
        }
        return $backendUsers;
    }
}
