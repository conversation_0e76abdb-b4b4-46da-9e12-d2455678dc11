<?php

declare(strict_types=1);

namespace LimeSoda\Monitoring\Console;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FileSize extends Command
{
    private const ROTATIONPATTERN = 'rotation-pattern';

    private const ROTATIONSIZE = 'rotation-size';

    private const DAYS = 'days';

    protected $monitoringStatus = 3;

    protected $warningLimit = null;

    protected $criticalLimit = null;

    protected $filename;

    protected $enableMinSizeCheck;

    protected $rotationPattern;

    protected $rotationSize;

    protected $days;

    protected function configure()
    {
        $this->setName('monitoring:filesize');
        $this->addArgument(
            'warning',
            InputArgument::REQUIRED,
            __('Type the Warning Limit for FileSize')->render()
        );
        $this->addArgument(
            'critical',
            InputArgument::REQUIRED,
            __('Type the Critical Limit for FileSize')->render()
        );
        $this->addArgument(
            'filename',
            InputArgument::REQUIRED,
            __('Type the File to be monitored')->render()
        );
        $this->addArgument(
            'minSizeCheck',
            InputArgument::OPTIONAL,
            __('Check for a minimum file size instead of the maximum file size')->render()
        );
        $this->addOption(
            self::DAYS,
            'd',
            InputArgument::OPTIONAL,
            __('Count of days including files')->render()
        );
        $this->addOption(
            self::ROTATIONPATTERN,
            'p',
            InputArgument::OPTIONAL,
            __('Rotation Filepattern (e.g.:"/var/www/current/src/var/log/system*.log")')->render()
        );

        $this->addOption(
            self::ROTATIONSIZE,
            's',
            InputArgument::OPTIONAL,
            __('Rotation Filesize')->render()
        );
        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->warningLimit = intval($input->getArgument('warning'));
        $this->criticalLimit = intval($input->getArgument('critical'));
        $this->filename = $input->getArgument('filename');
        $this->enableMinSizeCheck = $input->getArgument('minSizeCheck');
        $this->rotationPattern = $input->getOption(self::ROTATIONPATTERN);
        $this->rotationSize = $input->getOption(self::ROTATIONSIZE);
        $this->days = intval($input->getOption(self::DAYS));

        $rotationPatternLog = $this->rotationPattern ? ' (incl. pattern: ' . $this->rotationPattern . ')' : '';
        $enableMinSizeCheckLog = $this->enableMinSizeCheck ? 'undershot' : 'exceeded';

        $filenames = array();
        if ($this->rotationPattern) {
            $filenames = glob($this->rotationPattern);
        }
        $filenames[] = $this->filename;
        $filenames = array_unique($filenames);
        $totalFileSize = 0;
        foreach ($filenames as $filename) {
            if (!is_readable($filename)) {
                $this->monitoringStatus = 2;
                $format = '%u;Error reading file: %s';
                $output->writeln(sprintf($format, $this->monitoringStatus, $filename));
                return $this->monitoringStatus;
            }

            if ($this->isFileInTimeRange($filename)) {
                if ($this->rotationSize) {
                    $totalFileSize += (int)$this->rotationSize;
                } else {
                    $fileSize = filesize($filename);
                    $totalFileSize += round(floatval($fileSize / 1024 / 1024), 2);
                }
            }
        }


        if ($this->isCritical($totalFileSize)) {
            $this->monitoringStatus = 2;
            $format = '%u;The current File Size for %s%s is: %g Megabyte. Critical limit of %g Megabyte %s.|\'Megabyte\'=%g';
            $output->writeln(
                sprintf(
                    $format,
                    $this->monitoringStatus,
                    $this->filename,
                    $rotationPatternLog,
                    $totalFileSize,
                    $this->criticalLimit,
                    $enableMinSizeCheckLog,
                    $totalFileSize
                )
            );
        } elseif ($this->isWarning($totalFileSize)) {
            $this->monitoringStatus = 1;
            $format = '%u;The current File Size for %s%s is: %g Megabyte. Warning limit of %g Megabyte %s.|\'Megabyte\'=%g';
            $output->writeln(
                sprintf(
                    $format,
                    $this->monitoringStatus,
                    $this->filename,
                    $rotationPatternLog,
                    $totalFileSize,
                    $this->warningLimit,
                    $enableMinSizeCheckLog,
                    $totalFileSize
                )
            );
        } else {
            $this->monitoringStatus = 0;
            $format = '%u;The current File Size for %s%s is: %g Megabyte.|\'Megabyte\'=%g';
            $output->writeln(
                sprintf(
                    $format,
                    $this->monitoringStatus,
                    $this->filename,
                    $rotationPatternLog,
                    $totalFileSize,
                    $totalFileSize
                )
            );
        }

        return $this->monitoringStatus;
    }

    private function isFileInTimeRange($filename): bool
    {
        if (!$this->days) {
            return true;
        }
        $timeRang = strtotime('-' . $this->days . ' days', time());
        return $timeRang <= filemtime($filename);
    }

    private function isCritical($totalFileSize): bool
    {
        if ($this->enableMinSizeCheck) {
            return $this->criticalLimit >= $totalFileSize;
        }
        return $this->criticalLimit <= $totalFileSize;
    }

    private function isWarning($totalFileSize): bool
    {
        if ($this->enableMinSizeCheck) {
            return $this->warningLimit >= $totalFileSize;
        }
        return $this->warningLimit <= $totalFileSize;
    }
}
