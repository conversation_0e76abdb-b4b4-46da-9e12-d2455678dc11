<?xml version="1.0" encoding="UTF-8" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="ServerLoad" xsi:type="object">LimeSoda\Monitoring\Console\ServerLoad</item>
                <item name="CronJobs" xsi:type="object">LimeSoda\Monitoring\Console\CronJobs</item>
                <item name="DiskSpace" xsi:type="object">LimeSoda\Monitoring\Console\DiskSpace</item>
                <item name="CountFiles" xsi:type="object">LimeSoda\Monitoring\Console\CountFiles</item>
                <item name="FileSize" xsi:type="object">LimeSoda\Monitoring\Console\FileSize</item>
                <item name="FileAge" xsi:type="object">LimeSoda\Monitoring\Console\FileAge</item>
                <item name="DatabaseTableSize" xsi:type="object">LimeSoda\Monitoring\Console\DatabaseTableSize</item>
                <item name="UserBackend" xsi:type="object">LimeSoda\Monitoring\Console\UserBackend</item>
            </argument>
        </arguments>
    </type>
</config>
