<?php
/**
 * Copyright © LimeSoda Interactive Marketing GmbH. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace LimeSoda\Monitoring\Helper;

use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Cron\Model\ResourceModel\Schedule\Collection;
use Magento\Cron\Model\Schedule;
use Magento\Framework\App\ProductMetadata;
use Psr\Log\LoggerInterface;

class CronData
{
    /** @var \Magento\Cron\Model\ResourceModel\Schedule\Collection $collection */
    protected $collection;

    /**
     * @var DateTime
     */
    protected $datetime;

    /**
     * @var mixed|string
     */
    protected $_magentoVersion;
    protected $logger;

    /**
     * @param \Magento\Cron\Model\ResourceModel\Schedule\Collection $collection
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $datetime
     * @param \Magento\Framework\App\ProductMetadata $productMetaData
     */
    public function __construct(
        Collection $collection,
        DateTime $datetime,
        ProductMetadata $productMetaData,
        LoggerInterface $logger
    ) {
        $this->collection = $collection;
        $this->datetime = $datetime;
        $explodedVersion = explode("-", $productMetaData->getVersion());
        $this->_magentoVersion = $explodedVersion[0];
        $this->logger = $logger;
    }

    public function compareLastSuccessToCurrent($jobCode = null)
    {
        $collection = $this->collection;
        $collection->addFieldToFilter('status', Schedule::STATUS_SUCCESS);
        if ($jobCode) {
            $collection->addFieldToFilter('job_code', $jobCode);
        }
        $collection->setOrder('status', 'ASC');
        if ($collection->count() !== 0) {
            try {
                $currentDateTime = new \DateTime();
                $finishedAt = $collection->getLastItem()->getData('finished_at') ?: '';
                $lastSuccess = new \DateTime($finishedAt);
                return $currentDateTime->getTimestamp() - $lastSuccess->getTimestamp();
            } catch (\Exception $e) {
                $this->logger->error($e);
            }
        }
        return null;
    }

    public function getStatusLastCronExecute($jobCode = null)
    {
        $collection = $this->collection;
        $collection->addFieldToFilter('status', [Schedule::STATUS_SUCCESS, Schedule::STATUS_ERROR]);
        if ($jobCode) {
            $collection->addFieldToFilter('job_code', $jobCode);
        }
        $collection->setOrder('schedule_id', 'ASC');
        if ($collection->count() !== 0) {
            return $collection->getLastItem()->getData('status');
        }
        return null;
    }
}
