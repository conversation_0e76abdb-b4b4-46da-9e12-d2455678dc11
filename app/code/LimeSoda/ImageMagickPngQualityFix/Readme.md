# LimeSoda ImageMagick PNG Quality Fix for Magento 2

## Composer module name

    limesoda/magento2-imagemagickpngqualityfix

## Magento module name

    LimeSoda_ImageMagickPngQualityFix

## The problem

Handling of PNG files in combination with ImageMagick instead of GD2 for image processing in Magento 2, is not optimized.

The parameter for compression is ignored for PNG, and the settings for colordepth and amount of colors are not handled by Magento.

This might result in files that have a bigger filesize after the resize process, than the original files that where uploaded in the Magento backend.

## The solution

This is where this module plugs in for PNG Files:

    * set the amount of bit per color channel to 8
    * reduce the amount of available colors per bit to 256

## Examples

Image size before (8,86 MB total imagesize on the page)

![Image size before](docs/No_Fix.png)

Image size with special parameters for mimetype image/png (1,37 MB total imagesize on the page)

![Image size before](docs/LimeSoda_ImageMagickPngQualityFix.png)
