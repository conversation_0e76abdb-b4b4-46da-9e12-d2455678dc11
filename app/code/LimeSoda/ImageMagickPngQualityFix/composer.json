{"name": "limesoda/magento2-imagemagickpngqualityfix", "description": "Compression quality parameter is ignored if filetype is png. Image color depth and colorspace are the relevant parameters for image size with PNG.", "type": "magento2-module", "license": "proprietary", "version": "1.0.0", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "minimum-stability": "dev", "require": {}, "autoload": {"psr-4": {"LimeSoda\\ImageMagickPngQualityFix\\": ""}, "files": ["registration.php"]}}