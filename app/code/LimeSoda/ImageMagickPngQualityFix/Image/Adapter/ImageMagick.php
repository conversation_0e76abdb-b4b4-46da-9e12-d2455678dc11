<?php

namespace LimeSoda\ImageMagickPngQualityFix\Image\Adapter;

/**
 * Class ImageMagick
 * @package LimeSoda\ImageMagickPngQualityFix\Image\Adapter
 */
class ImageMagick extends \Magento\Framework\Image\Adapter\ImageMagick
{
    const BIT_PER_COLOR_CHANNEL = 8;
    const FALLBACK_COLOR_SPACE = \Imagick::COLORSPACE_SRGB;
    const COLOR_AMOUNT = 256;

    /**
     * We set some additional options for PNG files here.
     * Otherwise we might end up with bigger files after resizing.
     *
     * @return $this|ImageMagick
     */
    protected function _applyOptions()
    {
        parent::_applyOptions();

        if ($this->getMimeType() == 'image/png') {
            $this->_imageHandler->setImageDepth(self::BIT_PER_COLOR_CHANNEL);
            $colorspace = $this->_imageHandler->getImageColorspace();
            if (!$colorspace) {
                $colorspace = self::FALLBACK_COLOR_SPACE;
            }
            $this->_imageHandler->quantizeImage(self::COLOR_AMOUNT, $colorspace, 0, false, false);
        }

        return $this;
    }
}
