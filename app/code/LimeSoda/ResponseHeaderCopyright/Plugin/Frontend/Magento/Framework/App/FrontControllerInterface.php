<?php
declare(strict_types=1);

namespace LimeSoda\ResponseHeaderCopyright\Plugin\Frontend\Magento\Framework\App;

use Magento\Framework\App\Response\HttpInterface;
use Magento\Framework\Controller\ResultInterface;

class FrontControllerInterface
{
    /**
     * @param \Magento\Framework\App\FrontControllerInterface $subject
     * @param $result
     * @return HttpInterface|ResultInterface|mixed
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterDispatch(
        \Magento\Framework\App\FrontControllerInterface $subject,
                                                        $result
    )
    {
        if (
            $result instanceof ResultInterface ||
            $result instanceof HttpInterface
        ) {
            // no utf-8 is supported...
            $result->setHeader('x-built-by-limesoda', 'LIMESODA Interactive Marketing GmbH, 1170 Vienna');
            $result->setHeader('x-built-by-limesoda-force', 'Welcome to the LimeSide ... may the force be with you');
            $result->setHeader('x-built-by-limesoda-team', 'Kamino');
        }
        //Your plugin code
        return $result;
    }
}

