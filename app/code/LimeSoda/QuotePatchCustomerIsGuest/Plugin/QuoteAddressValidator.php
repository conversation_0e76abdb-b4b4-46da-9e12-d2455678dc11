<?php

namespace LimeSoda\QuotePatchCustomerIsGuest\Plugin;

use Magento\Quote\Api\Data\AddressInterface;
use Magento\Quote\Api\Data\CartInterface;

class QuoteAddressValidator
{
    /**
     * setCustomerIsGuest if customer is set
     *
     * @param \Magento\Quote\Model\QuoteAddressValidator $subject
     * @param CartInterface $cart
     * @param AddressInterface $address
     */
    public function beforeValidateForCart(\Magento\Quote\Model\QuoteAddressValidator $subject, CartInterface $cart, AddressInterface $address)
    {
        $customer = $cart->getCustomer();
        if (isset($customer) && $customer->getId()) {
            $cart->setCustomerIsGuest(0);
        }
        return [$cart, $address];
    }
}
