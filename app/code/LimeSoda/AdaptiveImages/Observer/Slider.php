<?php

namespace LimeSoda\AdaptiveImages\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use LimeSoda\AdaptiveImages\Model\Sizes;
use LimeSoda\AdaptiveImages\Model\ImageResizer;

class Slider implements ObserverInterface
{
    /**
     * @var Sizes
     */
    protected $sizes;

    /**
     * @var ImageResizer
     */
    protected $resizer;

    /**
     * Slider constructor.
     *
     * @param ImageResizer $resizer
     * @param Sizes $sizes
     */
    public function __construct(
        ImageResizer $resizer,
        Sizes $sizes
    )
    {
        $this->sizes = $sizes;
        $this->resizer = $resizer;
    }

    /**
     * @param Observer $observer
     */
    public function execute(Observer $observer)
    {
        $block = $observer->getEvent()->getBlock();
        $collection = $observer->getEvent()->getCollection();
        $dataKey = 'adaptive_images_data_list';

        if ($block->getSliderType() == 'background_image') {
            $dataKey = 'image_set_background';
        }

        if ($block->getTemplate() === 'LimeSoda_Banner::banner/slider-background.phtml') {
            $sizes = $this->sizes->getSizes('sliders__background');
        } elseif ($block->getTemplate() === 'LimeSoda_Banner::banner/slider-multiple.phtml') {
            $sizes = $this->sizes->getSizes('sliders__multiple');
        } else {
            $sizes = $this->sizes->getSizes('sliders__default');
        }

        foreach ($collection as $item) {
            $backgroundSet = [];
            foreach ($sizes as $key => $size) {
                $backgroundSet[] = $this->resizer->getResizedUrl($item->getImageUrl(), $size, true);
                $item->setData('image_' . $key, $this->resizer->getResizedUrl($item->getImageUrl(), $size));
            }
            $item->setData($dataKey, implode(',', $backgroundSet));
        }
    }
}