<?php

namespace LimeSoda\AdaptiveImages\Model;

use Magento\Framework\View\ConfigInterface;

class Sizes
{
    /**
     * @var ConfigInterface
     */
    protected $viewConfig;

    protected $defaultSizes = [];

    const MAX_WIDTH = 2400;

    const MAX_HEIGHT = 2400;

    /**
     * Sizes constructor.
     * @param ConfigInterface $viewConfig
     */
    public function __construct(
        ConfigInterface $viewConfig
    )
    {
        $this->viewConfig = $viewConfig->getViewConfig();
        $this->defaultSizes['product_list_images'] = ['width' => 240, 'height' => 300];
        $this->defaultSizes['category_image'] = ['width' => 2400, 'height' => 2400];
        $this->defaultSizes['slider'] = ['width' => 2400, 'height' => 2400];
    }

    /**
     * @param $type
     * @return array|false|string
     */
    public function getSizes($type)
    {
        $sizes = $this->viewConfig->getVarValue('LimeSoda_AdaptiveImages', $type);

        if (!isset($sizes['size_default'])) {
            $sizes = $this->getDefaultSize($type);
            $sizes['size_default'] = $sizes;
        }

        return $sizes;
    }

    /**
     * @param $type
     * @return array
     */
    protected function getDefaultSize($type)
    {
        if (isset($this->defaultSizes[$type])) {
            return $this->defaultSizes[$type];
        } else {
            return ['width' => self::MAX_WIDTH, 'height' => self::MAX_WIDTH];
        }
    }
}

