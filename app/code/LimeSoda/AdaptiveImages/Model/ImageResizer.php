<?php
namespace LimeSoda\AdaptiveImages\Model;

use Staempfli\ImageResizer\Model\Resizer;

class ImageResizer
{
    /**
     * @var Resizer
     */
    protected $resizer;

    public function __construct(
        Resizer $resizer
    ) {
        $this->resizer = $resizer;
    }

    public function getResizedUrl($url, $settings, $breakpoints = false)
    {
        $resizedImageUrl = $this->resizer->resizeAndGetUrl(
            $url,
            $settings['width'] ?? null,
            $settings['height'] ?? null,
            ['quality' => 100]
        );

        if ($breakpoints) {
            $breakpoint = $settings['breakpoint'] ?? '';
            if (!empty($breakpoint)) {
                $resizedImageUrl .= ' ' . $breakpoint . 'w';
            }
        }

        return $resizedImageUrl;
    }
}
