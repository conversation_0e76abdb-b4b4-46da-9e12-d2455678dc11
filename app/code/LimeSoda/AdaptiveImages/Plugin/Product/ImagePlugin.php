<?php

namespace LimeSoda\AdaptiveImages\Plugin\Product;

use LimeSoda\AdaptiveImages\Model\ImageResizer;
use LimeSoda\AdaptiveImages\Model\Sizes;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\UrlInterface;

class ImagePlugin
{
    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var Sizes
     */
    protected $sizes;

    /**
     * @var Resizer
     */
    protected $resizer;

    /**
     * ImagePlugin constructor.
     * @param ImageResizer $resizer
     * @param Sizes $sizes
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        ImageResizer $resizer,
        Sizes $sizes,
        StoreManagerInterface $storeManager
    ) {
        $this->resizer = $resizer;
        $this->sizes = $sizes;
        $this->storeManager = $storeManager;
    }

    public function beforeToHtml($block)
    {
        $block->setTemplate('LimeSoda_AdaptiveImages::product/image.phtml');
        $product = $block->getData('image_product');
        $mediaUrl = $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
        $baseUrl = $mediaUrl . 'catalog/product';
        if ($product) {
            $sizes = $this->sizes->getSizes('product_list_images');
            $images = [];
            foreach ($sizes as $sizeKey => $size) {
                if ($product->getImage()) {
                    $images[$sizeKey] = $this->resizer->getResizedUrl($baseUrl . $product->getImage(), $size);
                } elseif ($product->getSmallImage()) {
                    $images[$sizeKey] = $this->resizer->getResizedUrl($baseUrl . $product->getSmallImage(), $size);
                } elseif ($product->getThumbnail()) {
                    $images[$sizeKey] = $this->resizer->getResizedUrl($baseUrl . $product->getThumbnail(), $size);
                }
            }

            if (isset($images['size_default'])) {
                $block->setData(
                    'image_default_data_src',
                    $images['size_default']
                );
            }

            $block->setData('adaptive_images_data_list', implode(',', $images));
        }
    }
}
