<?php

namespace LimeSoda\AdaptiveImages\Plugin\Slider;

use LimeSoda\AdaptiveImages\Model\ImageResizer;
use LimeSoda\AdaptiveImages\Model\Sizes;

class SliderPlugin
{
    /**
     * ImagePlugin constructor.
     * @param ImageResizer $resizer
     * @param Sizes $sizes
     */
    public function __construct(
        ImageResizer $resizer,
        Sizes $sizes
    )
    {
        $this->resizer = $resizer;
        $this->sizes = $sizes;
    }

    public function beforeApplyBannerCollectionPlugins($subject, $result)
    {
        $block = $subject;
        $collection = $result;
        $dataKey = 'adaptive_images_data_list';

        if ($block->getSliderType() == 'background_image') {
            $dataKey = 'image_set_background';
        }

        if ($block->getTemplate() === 'LimeSoda_Banner::banner/slider-background.phtml') {
            $sizes = $this->sizes->getSizes('sliders__background');
        } elseif ($block->getTemplate() === 'LimeSoda_Banner::banner/slider-multiple.phtml') {
            $sizes = $this->sizes->getSizes('sliders__multiple');
        } else {
            $sizes = $this->sizes->getSizes('sliders__default');
        }

        foreach ($collection as $item) {
            $backgroundSet = [];
            foreach ($sizes as $size) {
                $backgroundSet[] = $this->resizer->getResizedUrl($item->getImageUrl(), $size);
            }
            $item->setData($dataKey, implode(',', $backgroundSet));
            if (isset($size['sizes_default'])) {
                $item->setData('image_default_data_src', $size['sizes_default']);
            }
        }

    }
}
