<?php
/**
 * @var $block \Magento\Catalog\Block\Category\View
 */
$category = $block->getCurrentCategory();
/* check if image exists, avoid unnecessary logging */
$image = $category->getImage();
$imgUrl = $category->getImageUrl();
if ( $image && $imgUrl) : ?>
    <?php
    $helper = $this->helper('Magento\Catalog\Helper\Output');
    $dataSrcSet = "data-src=\"{$block->escapeUrl($block->getData('image_default_data_src'))}\"";
    $srcSetParts = explode(',', $block->getData('adaptive_images_data_list'));
    if (count($srcSetParts) <= 1) {
        $dataSrcSet = '';
    }
    ?>
    <div class="category-image ratio-container lazyload">
        <img src=""
             alt="<?= $block->escapeHtml($category->getName()) ?>"
             title="<?= $block->escapeHtml($category->getName()) ?>"
             class="lazyload"
             data-expand="1"
             src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
             data-src="<?= $block->getData('image_default_data_src') ?>"
             data-sizes="auto"
             <?= $dataSrcSet ?>
        />
    </div>
<?php endif ?>
