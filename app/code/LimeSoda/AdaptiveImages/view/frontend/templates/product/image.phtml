<?php
/* @var $block \Magento\Catalog\Block\Product\Image */
$dataSrcSet = "data-src=\"{$block->escapeUrl($block->getData('image_default_data_src'))}\"";
$srcSetParts = $block->getData('adaptive_images_data_list') ? explode(',', $block->getData('adaptive_images_data_list')) : array();
if (count($srcSetParts) <= 1) {
    $dataSrcSet = '';
}
?>
<span class="product-image-container">
    <span class="product-image-wrapper" style="padding-bottom: <?php /* @escapeNotVerified */ echo ($block->getRatio() * 100); ?>%;">
        <?php /** @var $block \Magento\Catalog\Block\Product\Image */ ?>
        <img class="product-image-photo ratio-container lazyload"
            <?php /* @escapeNotVerified */ echo is_array($block->getCustomAttributes()) ? implode(' ',$block->getCustomAttributes()) : $block->getCustomAttributes(); ?>
             width="<?php /* @escapeNotVerified */ echo $block->getWidth(); ?>"
             height="<?php /* @escapeNotVerified */ echo $block->getHeight(); ?>"
             alt="<?php /* @escapeNotVerified */ echo $block->stripTags($block->getLabel(), null, true); ?>"
             data-expand="1"
             data-sizes="auto"
             src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
             data-src="<?= $block->escapeUrl($block->getData($block->getData('image_default_data_src')?'image_default_data_src':'image_url')) ?>"
            <?php echo $dataSrcSet ?>
        />
    </span>
</span>
