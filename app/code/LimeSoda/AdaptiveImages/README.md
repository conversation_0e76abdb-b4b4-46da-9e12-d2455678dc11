LimeSoda_AdaptiveImages
=====================

Magento 2 Module to add simple image resizing capabilities in all blocks and .phtml templates

Examples:
- product_list_images
- sliders
- category_image
- product_list_images

Installation Instructions
-------------------------
1, Clone the extension as a composer repository 

```
composer require limesoda/magento2-adaptive-images
```

2, Install the module & clear the cache

```
php bin/magento setup:upgrade --keep-generated 
php bin/magento cache:c
php bin/magento cache:f
```

3, Logout from the admin panel and then login again.

4, Change the config in your theme's view.xml file:

Example settings: 

```
<vars module="LimeSoda_AdaptiveImages">
    <var name="product_list_images">
        <var name="size_default">
            <var name="width">240</var>
            <var name="height">300</var>
            <var name="breakpoint">240</var>
        </var>
        <var name="size_l">
            <var name="width">1200</var>
            <var name="height">400</var>
            <var name="breakpoint">1200</var>
        </var>
        <var name="size_l">
            <var name="width">1600</var>
            <var name="height">400</var>
            <var name="breakpoint">2000</var>
        </var>
    </var>
    <var name="sliders">
        <var name="size_default">
            <var name="width">1200</var>
            <var name="height">400</var>
            <var name="breakpoint">1200</var>
        </var>
        <var name="size_l">
            <var name="width">1600</var>
            <var name="height">400</var>
            <var name="breakpoint">200-</var>
        </var>
    </var>
</vars>
```

Category Image

Banners

Banners Wide


```
.category-image {
  img {
    position: absolute;
    width: 100%;
    height: auto;
    + :after {
      display: none;
    }
  }
  &.ratio-container {
    position: relative;
    &:after {
      content:'';
      display: block;
      height: 0;
      width: 100%;
      /* 1600*467 = 29.25% = calc(467 / 1600 * 100%) */
      padding-bottom: 29.2%;
      transition: height .2s ease;
    }
  }
}
```

```
&.ratio-container {
   position: relative;
 }
 &.ratio-container:after {
   content:'';
   display: block;
   height: 0;
   width: 1px;
   padding-bottom: calc(41.9% - 20px);
 }
 @include min-screen($screen__xs) {
  // min-height: 200px;
   &.ratio-container:after {
     padding-bottom: calc(41.9% - 60px);
   }
 }
 @include min-screen($screen__m) {
   padding: 25px 20px 35px;
  // min-height: 300px;
 }
 @include min-screen($screen__l) {
   padding: 45px 55px;
 //  min-height: 385px;
   &.ratio-container:after {
     padding-bottom: calc(41.9% - 90px);
   }
 }
 @include min-screen($screen__l2) {
   padding: 65px;
//   min-height: 470px;
   &.ratio-container:after {
     padding-bottom: calc(41.9% - 130px);
   }
 }
``` 

JS Dependency
-------------------------------

https://github.com/aFarkas/lazysizes
https://github.com/aFarkas/lazysizes/tree/gh-pages/plugins/bgset

These files are combined into one js file.

Image resize module dependency
--------------
https://github.com/staempfli/magento2-module-image-resizer

Cache
--------------

Resized images are saved in cache to improve performance. That way, if an image was already resized, we just use the one in cache.

If you need to, you can clear the resized images cache on the Admin Cache Management
 
Uninstallation
--------------

1, Remove via Composer and clear the caches

```
composer remove limesoda/magento2-adaptive-images
```

Support
-------
If you have any issues with this extension, open an issue in the tracker.

Developer
---------

LimeSoda Interactive Marketing GmbH  
[http://www.limesoda.com](http://www.limesoda.com)  
[@LimeSoda_at](https://twitter.com/LimeSoda_at)

Licence
-------
[OSL - Open Software Licence 3.0](http://opensource.org/licenses/osl-3.0.php)

Copyright
---------
(c) 2021 LimeSoda Interactive Marketing GmbH
