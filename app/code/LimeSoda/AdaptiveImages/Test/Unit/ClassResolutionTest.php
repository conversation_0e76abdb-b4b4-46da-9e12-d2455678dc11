<?php
namespace LimeSoda\AdaptiveImages\Test\Unit;

class ClassResolutionTest extends \PHPUnit\Framework\TestCase
{
    /**
     * @var \Magento\Framework\TestFramework\Unit\Helper\ObjectManager
     */
    protected $objectManager;

    protected function setUp(): void
    {
        $this->objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);
    }

    protected function tearDown(): void
    {
        $this->objectManager  = null;
    }

    public function testClassNameResolution()
    {
        $classes = [
            'LimeSoda\AdaptiveImages\Plugin\Product\ListImagePlugin',
            'LimeSoda\AdaptiveImages\Plugin\Product\ImagePlugin',
            'LimeSoda\AdaptiveImages\Plugin\Category\ImagePlugin',
            'LimeSoda\AdaptiveImages\Model\Sizes'
        ];

        foreach ($classes as $class) {
            $object = $this->objectManager->getObject($class);
            $this->assertTrue(is_object($object));
        }
    }
}
