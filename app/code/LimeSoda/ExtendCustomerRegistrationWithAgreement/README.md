LimeSoda_ExtendCustomerRegistrationWithAgreement for Magento 2.x
=====================

Extend the new account registration form with agreement field

-------------------------------
Installation Instructions
-------------------------
1, <PERSON>lone the extension as a composer repository 

```
composer require limesoda/magento2-extended-customer-registration-with-agreement
```

2, Install the module & clear the cache

```
php bin/magento setup:upgrade --keep-generated 
php bin/magento cache:c
php bin/magento cache:f
```

3, Logout from the admin panel and then login again.

4, Change the config in Stores -> Configuration -> Customers -> Customer Configuration -> Agreement New Account Options


Example settings: 

![Example setup](https://gitlab.limesoda.com/limesoda/magento-2-extensions/magento2-extended-customer-registration-with-aggreement/raw/master/limesoda_extended_registration_setting.png)

Example frontend:

![Example frontend](https://gitlab.limesoda.com/limesoda/magento-2-extensions/magento2-extended-customer-registration-with-aggreement/raw/master/limesoda_extended_registration_frontend.png)
 
 
Uninstallation
--------------

1, Remove via Composer and clear the caches

```
composer remove limesoda/magento2-extended-customer-registration-with-agreement
```

Support
-------
If you have any issues with this extension, open an issue in the tracker.

Developer
---------
Adam Varga

LimeSoda Interactive Marketing GmbH  
[http://www.limesoda.com](http://www.limesoda.com)  
[@LimeSoda_at](https://twitter.com/LimeSoda_at)

Licence
-------
[OSL - Open Software Licence 3.0](http://opensource.org/licenses/osl-3.0.php)

Copyright
---------
(c) 2019 LimeSoda Interactive Marketing GmbH
