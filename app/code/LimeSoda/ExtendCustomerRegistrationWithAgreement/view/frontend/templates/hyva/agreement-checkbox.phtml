<?php

declare(strict_types=1);

use LimeSoda\ExtendCustomerRegistrationWithAgreement\Block\Account\Form;
use Magento\Framework\Escaper;

/** @var Form $block */
/** @var Escaper $escaper */

?>

<div class="field choice agreement-checkbox">
    <input type="checkbox"
           id="agreement-checkbox"
           name="agreement-checkbox"
           class="<?= /* @noEscape */ $block->isRequired(); ?>"
           value="1"
           <?= /* @noEscape */ $block->isRequired() ?>
    >

    <label for="agreement-checkbox">
        <span>
            <?= /* @noEscape */ $block->getAgreementText(); ?>
        </span>
    </label>
</div>
