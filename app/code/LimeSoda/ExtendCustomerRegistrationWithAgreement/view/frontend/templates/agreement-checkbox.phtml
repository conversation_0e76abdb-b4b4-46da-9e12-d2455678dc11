<?php

declare(strict_types=1);

use LimeSoda\ExtendCustomerRegistrationWithAgreement\Block\Account\Form;
use Magento\Framework\Escaper;

/** @var Form $block */
/** @var Escaper $escaper */

?>

<div class="field agreement-checkbox <?= $escaper->escapeHtml($block->isRequired()); ?>">
    <div class="control agreement-text">
        <input type="checkbox"
               id="agreement-checkbox"
               name="agreement-checkbox"
               data-validate="{required:<?= /* @noEscape */ $block->isRequiredDataValidate(); ?>}"
               class="input-checkbox checkbox <?= /* @noEscape */ $block->isRequired(); ?>"
               value="1"
        >

        <label for="agreement-checkbox" class="label">
            <?= /* @noEscape */ $block->getAgreementText(); ?>
        </label>
    </div>
</div>
