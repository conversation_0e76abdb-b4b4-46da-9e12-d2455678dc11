{"name": "limesoda/magento2-extended-customer-registration-with-agreement", "description": "Extend the new account registration form with agreement field", "type": "magento-module", "license": ["proprietary"], "config": {"sort-packages": true}, "require": {"magento/framework": "^103.0", "magento/module-config": "^101.2"}, "require-dev": {"magento/magento-coding-standard": "^6.0"}, "autoload": {"files": ["registration.php"], "psr-4": {"LimeSoda\\ExtendCustomerRegistrationWithAgreement\\": ""}}, "repositories": [{"type": "composer", "url": "https://magento2-packages.limesoda.com/"}], "scripts": {"post-install-cmd": ["([ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/phpcs --config-set installed_paths ../../magento/magento-coding-standard/)"], "post-update-cmd": ["([ $COMPOSER_DEV_MODE -eq 0 ] || vendor/bin/phpcs --config-set installed_paths ../../magento/magento-coding-standard/)"]}}