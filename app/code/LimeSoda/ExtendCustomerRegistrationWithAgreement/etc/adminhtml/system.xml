<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="customer" translate="label" sortOrder="300">
            <label>Customers</label>
        </tab>
        <section id="customer" translate="label" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Customer Configuration</label>
            <tab>customer</tab>
            <resource>Magento_Customer::config_customer</resource>
            <group id="agreement_registration" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Agreement New Account Options</label>
                <comment><![CDATA[Enable agreement checkbox on the new account registration page]]></comment>
                <field id="enable_agreement" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Show agreement</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="is_required" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Is required</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="agreement_text" translate="label comment" sortOrder="30" type="editor" showInStore="1" showInDefault="1" >
                    <label>Agreement text</label>
                    <frontend_model>LimeSoda\ExtendCustomerRegistrationWithAgreement\Block\Adminhtml\System\Config\Editor</frontend_model>
                </field>
            </group>
        </section>
    </system>
</config>
