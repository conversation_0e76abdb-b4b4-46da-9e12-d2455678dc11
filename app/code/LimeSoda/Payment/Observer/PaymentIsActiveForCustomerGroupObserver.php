<?php

declare(strict_types=1);

namespace LimeSoda\Payment\Observer;

use Magento\Customer\Model\Session;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;
use Magento\Store\Model\ScopeInterface;

class PaymentIsActiveForCustomerGroupObserver implements ObserverInterface
{

    /**
     * @var string
     */
    const ID_ALL_GROUPS = '1';

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @param Session $customerSession
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        Session $customerSession,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->customerSession = $customerSession;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Restrict payment method to customer group
     *
     * @param EventObserver $observer
     * @return void
     */
    public function execute(EventObserver $observer): void
    {
        if ($this->isMethodAllowedForGroup($observer->getEvent()->getMethodInstance()->getCode(),
                (string)$this->customerSession->getCustomerGroupId()) === false) {
            /** @var \Magento\Framework\DataObject $result */
            $result = $observer->getEvent()->getResult();
            $result->setData('is_available', false);
        }
    }

    /**
     * Check if payment method is allowed for customer group
     *
     * @param string $paymentMethodCode
     * @param string $customerGroupId
     * @return bool
     */
    private function isMethodAllowedForGroup($paymentMethodCode, $customerGroupId): bool
    {
        $allowedGroupIDs = $this->scopeConfig->getValue(
            'payment/' . $paymentMethodCode . '/customergroups',
            ScopeInterface::SCOPE_STORE
        );

        return (isset($allowedGroupIDs) === false || $allowedGroupIDs === self::ID_ALL_GROUPS) ? true : (is_string($allowedGroupIDs) ? in_array($customerGroupId,
            explode(',', $allowedGroupIDs), true) : false);
    }
}
