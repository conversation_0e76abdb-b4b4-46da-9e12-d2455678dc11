# LimeSoda Payment

This module extends Magentos payment module and offers these additional features:

* Restrict payment methods to customer groups
* Currently only Magento default offline payment methods and paypal express are supported (see Todos for more...)

## Scope of this extension

The extension has been developed for these contexts:

* Payment methods block on checkout page

## Installation and setup

* Install using composer
* No special setup steps required

## Usage

* In Magento admin panel go to 'Stores > Configuration > Sales > Payment Methods'
* Open payment method section you wish to restrict to customer groups
* Select one ore more customer groups in config multiselect field "Restrict to Customer Groups"
* Config can be set on website, store group or store view level

## Todos

* Currently only Magento default offline payment methods and paypal express are supported
* In case you need to configure additional payment methods, you can simply add them to the system.xml (ask a developer to do that)
* A more complex solution would be to add the "Restrict to customer group" config value programatically to all available payment methods
* But adding payment methods to the system.xml is far more easier and in case payment methods added are not available in other projects, these are just ignored and no error is thrown
