<?php

declare(strict_types=1);

namespace LimeSoda\CookieAdvanced\Helper;

use <PERSON><PERSON><PERSON>\Cookie\Helper\Cookie as MageCookie;

class <PERSON>ie extends MageCookie
{
    /**
     * Cookie name for users who allow all cookies
     */
    const IS_USER_ALLOWED_ESSENTIAL_SAVE_COOKIE = 'user_allowed_save_essential_cookie';

    /**
     * Cookie name for users who disallowed cookies
     */
    const IS_USER_DISALLOWED_SAVE_COOKIE = 'user_disallowed_save_cookie';
}
