define(['jquery'], function ($) {
    'use strict';

    var widgetMixin = {
        options: {
            cookieAllowButtonSelector: null,
            cookieAllowEssentialButtonSelector: null,
            cookieDisallowButtonSelector: null,
            cookieStatisticsCheckboxSelector: null,
            noCookiesUrl: null,
            cookieNameAllowEssential: null,
            cookieNameAllow: null,
            cookieNameDisallow: null,
            cookieValue: null,
            cookieLifetime: null
        },

        _create: function () {
            this._setElementVisibility();

            $(this.options.cookieAllowButtonSelector).on('click', $.proxy(function () {
                this._handleAllow(true)
            }, this));

            $(this.options.cookieAllowEssentialButtonSelector).on('click', $.proxy(function () {
                this._handleAllow(false)
            }, this));

            $(this.options.cookieDisallowButtonSelector).on('click', $.proxy(this._handleDisallow, this));
        },

        _setElementVisibility: function () {
            if (this._getAllowEssentialCookie() || this._getDisallowCookie()) {
                this.element.hide();
            } else {
                this.element.show();
            }
        },

        _handleAllow: function (all) {
            all = all || this._isStatisticsCheckboxChecked();

            if (all) {
                this._setAllowCookie()
            }

            this._setAllowEssentialCookie();

            if (this._getAllowEssentialCookie()) {
                this.element.hide();
                $(document).trigger('user:allowed:save:cookie');
            } else {
                window.location.href = this.options.noCookiesUrl;
            }
        },

        _handleDisallow: function () {
            this._setDisallowCookie();

            if (this._getDisallowCookie()) {
                this.element.hide();
            } else {
                window.location.href = this.options.noCookiesUrl;
            }
        },

        _isStatisticsCheckboxChecked: function () {
            return $(this.options.cookieStatisticsCheckboxSelector).is(":checked");
        },

        _getAllowEssentialCookie: function () {
            return this._getCookie(this.options.cookieNameAllowEssential);
        },

        _getDisallowCookie: function () {
            return this._getCookie(this.options.cookieNameDisallow);
        },

        _getCookie: function (name) {
            return $.mage.cookies.get(name);
        },

        _setAllowEssentialCookie: function () {
            this._setCookie(this.options.cookieNameAllowEssential);
        },

        _setAllowCookie: function () {
            this._setCookie(this.options.cookieNameAllow);
        },

        _setDisallowCookie: function () {
            this._setCookie(this.options.cookieNameDisallow);
        },

        _setCookie: function (name) {
            $.mage.cookies.set(name, JSON.stringify(this.options.cookieValue), {
                expires: new Date(new Date().getTime() + this.options.cookieLifetime * 1000)
            });
        },
    };

    return function (targetWidget) {
        $.widget('mage.cookieNotices', targetWidget, widgetMixin);
        return $.mage.cookieNotices;
    };
});
