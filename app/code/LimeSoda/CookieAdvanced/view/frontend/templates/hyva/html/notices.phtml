<?php
/** @var Notices $block */
/** @var Escaper $escaper */

/** @var Cookie $cookieHelper */

use Magento\Cookie\Block\Html\Notices;
use Magento\Framework\Escaper;
use LimeSoda\CookieAdvanced\Helper\Cookie;

$cookieHelper = $block->getData('cookieHelper');
?>

<?php if ($cookieHelper->isCookieRestrictionModeEnabled()): ?>
    <script>
        function initCookieBanner() {
            return {
                visible: false,
                cookieNameAllow: '<?= /* @noEscape */ Cookie::IS_USER_ALLOWED_SAVE_COOKIE ?>',
                cookieNameAllowEssential: '<?= /* @noEscape */ Cookie::IS_USER_ALLOWED_ESSENTIAL_SAVE_COOKIE ?>',
                cookieNameDisallow: '<?= /* @noEscape */ Cookie::IS_USER_DISALLOWED_SAVE_COOKIE ?>',
                cookieValue: '<?= /* @noEscape */ $cookieHelper->getAcceptedSaveCookiesWebsiteIds() ?>',
                cookieLifetime: '<?= /* @noEscape */ $cookieHelper->getCookieRestrictionLifetime() ?>',
                noCookiesUrl: '<?= $escaper->escapeJs($block->getUrl('cookie/index/noCookies')) ?>',
                acceptStatisticsCookie: false,

                initVisibility: function () {
                    this.visible = (this.getAllowEssentialCookie() === null) && (this.getDisallowCookie() === null);
                },

                getAllowEssentialCookie: function () {
                    return hyva.getCookie(this.cookieNameAllowEssential);
                },

                getDisallowCookie: function () {
                    return hyva.getCookie(this.cookieNameDisallow);
                },

                performEssentialCookieCheck: function () {
                    if (this.getAllowEssentialCookie() === null) {
                        window.location.href = this.noCookiesUrl;
                    }
                },

                performDisallowCookieCheck: function () {
                    if (this.getDisallowCookie() === null) {
                        window.location.href = this.noCookiesUrl;
                    }
                },

                setAcceptAllCookies: function () {
                    this.setCookie(this.cookieNameAllow);
                    this.setCookie(this.cookieNameAllowEssential);
                    this.performEssentialCookieCheck();
                },

                setAcceptCookies: function () {
                    if (this.acceptStatisticsCookie) {
                        this.setCookie(this.cookieNameAllow);
                    }

                    this.setCookie(this.cookieNameAllowEssential);
                    this.performEssentialCookieCheck();
                },

                setDisallowCookies: function () {
                    this.setCookie(this.cookieNameDisallow);
                    this.performDisallowCookieCheck();
                },

                setCookie: function (name) {
                    hyva.setCookie(name, JSON.stringify(this.cookieValue), (this.cookieLifetime / 60 / 60 / 24));
                },
            }
        }
    </script>

    <section id="notice-cookie-block"
             x-data="initCookieBanner();"
             @private-content-loaded.window="initVisibility()"
    >
        <template x-if="visible">
            <div role="dialog"
                 aria-modal="true"
                 class="container fixed py-0 right-0 bottom-0 z-30 flex max-w-full bg-container-darker
                border-t-2 border-container-darker"
            >
                <button @click="visible = false;" aria-label="Close panel"
                        class="absolute right-0 top-0 p-4">
                    <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round"
                              stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>

                <div class="p-2 pr-4">
                    <p class="mb-1">
                    <span class="font-semibold">
                        <?= $escaper->escapeHtml(__('We use cookies to make your experience better.')) ?>
                    </span>
                    </p>
                    <p class="mb-1">
                    <span>
                        <?= $escaper->escapeHtml(__( // phpcs:disable Generic.Files.LineLength.TooLong
                            'To comply with the new e-Privacy directive, we need to ask for your consent to set the cookies.'
                        )) // phpcs:enable  ?>
                    </span>
                    </p>
                    <p class="mb-4">
                        <a href="<?= $escaper->escapeUrl($block->getPrivacyPolicyLink()) ?>"
                           class="underline">
                            <?= $escaper->escapeHtml(__('Read our policy')) ?>
                        </a>
                    </p>
                    <fieldset class="my-4">
                        <div class="field choice">
                            <input type="checkbox" id="cookie_essential" value="1" class="checkbox" disabled="disabled"
                                   checked />
                            <label class="label" for="cookie_essential">
                                <span><?= $escaper->escapeHtml(__('Essential')) ?></span>
                            </label>
                        </div>
                        <div class="field choice">
                            <input type="checkbox" id="cookie_statistics" value="0" class="checkbox"
                                   x-model="acceptStatisticsCookie"/>
                            <label class="label" for="cookie_statistics">
                                <span><?= $escaper->escapeHtml(__('Statistics')) ?></span>
                            </label>
                        </div>
                    </fieldset>
                    <div class="my-2 flex">
                        <button id="btn-cookie-allow" class="btn btn-primary"
                                @click="setAcceptAllCookies(); visible = false">
                            <?= $escaper->escapeHtml(__('Accept All Cookies')) ?>
                        </button>
                        <button id="btn-cookie-allow-essential" class="btn btn-secondary ml-4"
                                @click="setAcceptCookies(); visible = false">
                            <?= $escaper->escapeHtml(__('Accept Cookies')) ?>
                        </button>
                        <button id="btn-cookie-disallow" class="btn btn-secondary ml-4"
                                @click="setDisallowCookies(); visible = false">
                            <?= $escaper->escapeHtml(__('Close')) ?>
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </section>
<?php endif; ?>
