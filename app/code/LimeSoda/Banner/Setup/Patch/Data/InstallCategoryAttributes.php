<?php

declare(strict_types=1);

namespace LimeSoda\Banner\Setup\Patch\Data;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Catalog\Setup\CategorySetup;
use Magento\Catalog\Setup\CategorySetupFactory;

/**
 * Add slider category attribute.
 */
class InstallCategoryAttributes implements DataPatchInterface
{

    private ModuleDataSetupInterface $moduleDataSetup;
    private CategorySetupFactory $categorySetupFactory;

    /**
     * PatchInitial constructor.
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param CategorySetupFactory $categorySetupFactory
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        CategorySetupFactory $categorySetupFactory
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->categorySetupFactory = $categorySetupFactory;
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function apply()
    {
        $attributeGroup = 'Category Slider';
        /** @var CategorySetup $categorySetup */
        $categorySetup = $this->categorySetupFactory->create(['setup' => $this->moduleDataSetup]);
        $entityTypeId = $categorySetup->getEntityTypeId(\Magento\Catalog\Model\Category::ENTITY);
        $attributeSetId = $categorySetup->getDefaultAttributeSetId($entityTypeId);
        $categorySetup->addAttributeGroup($entityTypeId, $attributeSetId, $attributeGroup, 20);

        $attributes = [
            'category_slider' => [
                'type' => 'int',
                'label' => 'Slider',
                'input' => 'select',
                'required' => false,
                'sort_order' => 20,
                'user_defined' => true,
                'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                'group' => $attributeGroup,
            ]
        ];

        foreach ($attributes as $attributeCode => $data) {
            $categorySetup->addAttribute($entityTypeId, $attributeCode, $data);
            $categorySetup->addAttributeToGroup(
                $entityTypeId,
                $attributeSetId,
                $attributeGroup,
                $attributeCode
            );
        }
    }
}
