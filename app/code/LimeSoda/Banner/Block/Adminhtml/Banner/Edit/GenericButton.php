<?php
namespace LimeSoda\Banner\Block\Adminhtml\Banner\Edit;

use LimeSoda\Banner\Api\BannerRepositoryInterface;
use Magento\Backend\Block\Widget\Context;
use Magento\Framework\Exception\NoSuchEntityException;
use LimeSoda\Banner\Model\Banner\ModuleConfig;

/**
 * Class GenericButton
 */
class GenericButton
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var BannerRepositoryInterface
     */
    protected $entityRepository;

    /**
     * @param Context $context
     * @param BannerRepositoryInterface $entityRepository
     */
    public function __construct(
        Context $context,
        BannerRepositoryInterface $entityRepository
    ) {
        $this->context = $context;
        $this->entityRepository = $entityRepository;
    }

    public function getEntityId()
    {
        try {
            return $this->entityRepository->getById(
                $this->context->getRequest()->getParam(ModuleConfig::ADMIN_URL_ENTITY_ID)
            )->getId();
        } catch (NoSuchEntityException $e) {
        }
        return null;
    }
    /**
     * Generate url by route and parameters
     *
     * @param   string $route
     * @param   array $params
     * @return  string
     */
    public function getUrl($route = '', $params = [])
    {
        return $this->context->getUrlBuilder()->getUrl($route, $params);
    }
}
