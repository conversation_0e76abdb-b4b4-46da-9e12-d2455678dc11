<?php
namespace LimeSoda\Banner\Block\Catalog\Category;

use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template\Context;
use LimeSoda\Banner\Model\SliderRepository;
use LimeSoda\Banner\Model\BannerRepository;
use LimeSoda\Banner\Api\Data\SliderInterface;
use LimeSoda\Banner\Block\AbstractSlider;

class Slider extends AbstractSlider
{
    /**
     * @var Registry
     */
    protected $registry;

    /**
     * @var string
     */
    protected $sliderType = 'background_image';

    /**
     * Slider constructor.
     * @param Context $context
     * @param SliderRepository $sliderRepository
     * @param BannerRepository $bannerRepository
     * @param Registry $registry
     * @param array $data
     */
    public function __construct(
        Context $context,
        SliderRepository $sliderRepository,
        BannerRepository $bannerRepository,
        Registry $registry,
        array $data = []
    ) {
        parent::__construct($context, $sliderRepository, $bannerRepository, $data);
        $this->registry = $registry;
    }

    /**
     * @return mixed
     */
    public function getCategory()
    {
        return $this->registry->registry('current_category');
    }

    /**
     * @return SliderInterface
     */
    public function getSlider()
    {
        if (!$this->slider) {
            $category = $this->getCategory();
            $slideId = $category->getData('category_slider');
            $slider = null;
            try {
                $slider = $this->sliderRepository->getById($slideId);
                if (!$slider) {
                }
            } catch (\Exception $e) {

            }
            if (!$slider) {
                $this->slider = $this->sliderRepository->getEmptyItem();
            } else {
                $this->slider = $slider;
            }
        }

        return $this->slider;
    }
}
