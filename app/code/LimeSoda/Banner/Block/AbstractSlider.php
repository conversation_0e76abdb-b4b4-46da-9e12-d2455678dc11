<?php
namespace LimeSoda\Banner\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\View\Element\Template\Context;
use Magento\Framework\DataObject\IdentityInterface;
use LimeSoda\Banner\Model\SliderRepository;
use LimeSoda\Banner\Model\BannerRepository;
use LimeSoda\Banner\Api\Data\SliderInterface;
use LimeSoda\Banner\Api\Data\BannerInterface;

class AbstractSlider extends Template implements IdentityInterface
{
    const SLIDER_TYPE_IMAGE = 'image';

    const SLIDER_TYPE_BACKGROUND = 'background_image';

    /**
     * Block name for the event prefix
     *
     * @var string
     */
    protected $blockEventPrefix = '';

    /**
     * @var \LimeSoda\Banner\Model\SliderRepository
     */
    protected $sliderRepository;

    /**
     * @var \LimeSoda\Banner\Model\BannerRepository
     */
    protected $bannerRepository;

    /**
     * @var \LimeSoda\Banner\Api\Data\SliderInterface
     */
    protected $slider;

    /**
     * @var integer
     */
    protected $sliderId;


    /**
     * @var ManagerInterface
     */
    protected $eventManager;

    /**
     * @var string
     */
    protected $sliderType = self::SLIDER_TYPE_IMAGE;
    
    /**
     * AbstractSlider constructor.
     *
     * @param Context $context
     * @param SliderRepository $sliderRepository
     * @param BannerRepository $bannerRepository
     * @param array $data
     */
    public function __construct(
        Context $context,
        SliderRepository $sliderRepository,
        BannerRepository $bannerRepository,
        array $data = []
    ) {
        parent::__construct($context, $data);

        $this->sliderRepository = $sliderRepository;
        $this->bannerRepository = $bannerRepository;
        $this->eventManager = $context->getEventManager();
    }

    /**
     * @return string
     */
    public function getSliderType()
    {
        return $this->getData('slider_type') ?? $this->sliderType;
    }

    /**
     * @param $sliderType
     * @return $this
     */
    public function setSliderType($sliderType)
    {
        $this->sliderType = $sliderType;

        return $this;
    }

    /**
     * @return mixed
     */
    public function getSliderId()
    {
        return $this->sliderId;
    }

    /**
     * @param mixed $sliderId
     */
    public function setSliderId($sliderId)
    {
        $this->sliderId = $sliderId;
    }

    /**
     * @param $slider
     */
    public function setSlider(SliderInterface $slider)
    {
        $this->slider = $slider;
    }

    /**
     * @return SliderInterface|\Magento\Framework\DataObject
     */
    public function getSlider()
    {
        if (!$this->slider) {
            $sliderId = $this->getSliderId();
            $slider = null;
            try {
                $slider = $this->sliderRepository->getById($sliderId);
            } catch (\Exception $e) {

            }

            if (!$slider) {
                $this->slider = $this->sliderRepository->getEmptyItem();
            }
        }

        return $this->slider;
    }

    /**
     * @return array
     */
    public function getBannerIds()
    {
        $slider = $this->getSlider();

        $ids = [];
        if ($slider) {
            $ids = explode(',', (string) $slider->getData('slider_banner_ids'));
            $ids = array_filter($ids);
        }

        // @todo add logging when fails

        return array_reverse($ids);
    }

    /**
     * @return \LimeSoda\Banner\Model\ResourceModel\Banner\Collection
     */
    public function getBannerCollection()
    {
        $ids = $this->getBannerIds();
        $collection = $this->bannerRepository->getCollection();
        $collection->addFieldToFilter('banner_id', ['in' => $ids]);
        $collection->getSelect()->order(new \Zend_Db_Expr('FIELD(banner_id, ' . implode(',', $ids).')'));
        $this->eventManager->dispatch(
            $this->blockEventPrefix . 'block_get_banner_collection_after',
            [
                'collection' => $collection,
                'block' => $this
            ]
        );

        return $collection;
    }

    /**
     * @param BannerInterface $banner
     * @return string
     */
    public function getLinkUrl(BannerInterface $banner)
    {
        return $this->getUrl($banner->getData('link'));
    }

    /**
     * @return array
     */
    public function getIdentities()
    {
        return [\LimeSoda\Banner\Model\Slider::CACHE_TAG . '_' . $this->getSliderId()];
    }
}
