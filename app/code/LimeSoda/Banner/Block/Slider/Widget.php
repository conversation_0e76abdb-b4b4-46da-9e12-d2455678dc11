<?php
namespace LimeSoda\Banner\Block\Slider;

use Magento\Widget\Block\BlockInterface;
use LimeSoda\Banner\Block\AbstractSlider;

class Widget extends AbstractSlider implements BlockInterface
{
    /**
     * @return \LimeSoda\Banner\Api\Data\SliderInterface|\LimeSoda\Banner\Model\Slider|\Magento\Framework\DataObject
     */
    public function getSlider()
    {
        if (!$this->slider) {
            $sliderCode = $this->getData('slider_code');
            $slider = null;
            try {
                $slider = $this->sliderRepository->getByCode($sliderCode);
                $this->slider = $slider;
            } catch (\Exception $e) {

            }

            if (!$slider) {
                $this->slider = $this->sliderRepository->getEmptyItem();
            }
        }

        return $this->slider;
    }
}
