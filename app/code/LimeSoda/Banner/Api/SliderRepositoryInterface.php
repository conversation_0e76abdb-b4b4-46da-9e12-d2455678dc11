<?php
namespace LimeSoda\Banner\Api;

use LimeSoda\Banner\Api\Data\SliderInterface;

/**
 * @api
 */
interface SliderRepositoryInterface
{
    /**
     * @param SliderInterface $model
     * @return SliderInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(SliderInterface $model);

    /**
     * @param int $id
     * @return SliderInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($id);

    /**
     * @param string $code
     * @return SliderInterface
     */
    public function getByCode($code);

    /**
     * @param SliderInterface $model
     * @return mixed
     */
    public function delete(SliderInterface $model);

    /**
     * @param int id
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($id);

    /**
     * @return SliderInterface
     */
    public function getEmptyItem();
}
