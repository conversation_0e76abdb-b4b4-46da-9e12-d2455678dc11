<?php
declare(strict_types=1);

namespace LimeSoda\Banner\Controller\Adminhtml\Slider;

use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface as HttpPostActionInterface;
use Magento\Ui\Component\MassAction\Filter;
use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action;
use LimeSoda\Banner\Model\ResourceModel\Slider\CollectionFactory;
use LimeSoda\Banner\Model\Slider\MassAction;
use Psr\Log\LoggerInterface;

class MassActivate extends Action implements HttpPostActionInterface
{
    /**
     * @var CollectionFactory
     */
    private $sliderCollectionFactory;

    /**
     * @var Filter
     */
    private $filter;

    /**
     * @var MassAction
     */
    private $sliderMassAction;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param Context $context
     * @param Filter $filter
     * @param CollectionFactory $collectionFactory
     * @param MassAction $sliderMassAction
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory,
        MassAction $sliderMassAction,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->filter = $filter;
        $this->sliderCollectionFactory = $collectionFactory;
        $this->sliderMassAction = $sliderMassAction;
        $this->logger = $logger;
    }

    /**
     * Activate sliders.
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        try {
            $collection = $this->filter->getCollection($this->sliderCollectionFactory->create());
            $this->sliderMassAction->activate($collection);
            $this->messageManager->addSuccessMessage(
                __('A total of %1 record(s) were updated.', $this->sliderMassAction->getUpdatedCount($collection))
            );
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('Something went wrong while activating slider(s).'));
            $this->logger->error($e->getMessage());
        }

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setPath('*/*/index');

        return $resultRedirect;
    }
}
