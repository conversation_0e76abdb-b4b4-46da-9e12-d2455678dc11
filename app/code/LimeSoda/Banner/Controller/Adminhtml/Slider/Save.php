<?php
namespace LimeSoda\Banner\Controller\Adminhtml\Slider;

use Magento\Backend\App\Action\Context;
use LimeSoda\Banner\Model\SliderRepository;
use LimeSoda\Banner\Model\Slider\ModuleConfig;
use LimeSoda\Banner\Controller\Adminhtml\Action;

class Save extends Action
{
    /**
     * @var SliderRepository
     */
    protected $repository;

    /**
     * Save constructor.
     * @param Context $context
     * @param SliderRepository $repository
     */
    public function __construct(
        Context $context,
        SliderRepository $repository
    ) {
        parent::__construct($context);
        $this->repository = $repository;
    }

    public function execute()
    {
        $data = $this->getRequest()->getPostValue();

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();

        if ($data) {
            $model = $this->getModelFromRequest();

            if (isset($data[ModuleConfig::DB_PK]) && empty($data[ModuleConfig::DB_PK])) {
                unset($data[ModuleConfig::DB_PK]);
            }

            if (isset($data['slider_banners'])) {
                usort($data['slider_banners'], function ($itemA, $itemB) {
                    $a = (int) $itemA['sort_order'];
                    $b = (int) $itemB['sort_order'];
                    if ($a == $b) {
                        return 0;
                    }
                    return ($a < $b) ? 1 : -1;
                });

                $idValues = array_column($data['slider_banners'], 'banner_id');
                $idValues = array_unique($idValues);
                $data['slider_banner_ids'] = implode(',', $idValues);
            }

            $model->setData($data);

            try {
                $this->repository->save($model);
                $this->getMessageManager()->addSuccess(__('Item Updated'));
                $this->_session->setFormData(false);
                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit',
                        [
                            ModuleConfig::ADMIN_URL_ENTITY_ID => $model->getId(), '_current' => true
                        ]
                    );
                }
                return $resultRedirect->setPath('*/*/');
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $this->messageManager->addError($e->getMessage());
            } catch (\RuntimeException $e) {
                $this->getMessageManager()->addError($e->getMessage());
            } catch (\Exception $e) {
                $this->getMessageManager()->addException($e, __('Something went wrong while saving.'));
            }

            $this->_getSession()->setFormData($data);

            return $resultRedirect->setPath(
                '*/*/edit',
                [
                    ModuleConfig::ADMIN_URL_ENTITY_ID =>
                    $this->getRequest()->getParam(ModuleConfig::ADMIN_URL_ENTITY_ID)
                ]
            );
        }

        return $resultRedirect->setPath('*/*/');
    }

    /**
     * @return \LimeSoda\Banner\Model\Slider|mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function getModelFromRequest()
    {
        $model = $this->repository->getEmptyItem();
        $id = $this->getRequest()->getParam(ModuleConfig::ADMIN_URL_ENTITY_ID);

        if ($id) {
            $model = $this->repository->getById($id);
        }

        return $model;
    }
}
