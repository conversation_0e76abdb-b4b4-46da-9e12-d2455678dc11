<?php
namespace LimeSoda\Banner\Controller\Adminhtml\Slider;

use Magento\Backend\App\Action\Context;
use LimeSoda\Banner\Model\SliderRepository;
use LimeSoda\Banner\Model\Slider\ModuleConfig;
use LimeSoda\Banner\Controller\Adminhtml\Action;

class Delete extends Action
{

    /**
     * @var SliderRepository
     */
    protected $repository;

    /**
     * @var ModuleConfig
     */
    protected $moduleConfig;

    /**
     * @param Context $context
     * @param SliderRepository $repository
     * @param ModuleConfig $moduleConfig
     */
    public function __construct(
        Context $context,
        SliderRepository $repository,
        ModuleConfig $moduleConfig
    ) {
        parent::__construct($context);
        $this->repository = $repository;
    }

    public function execute()
    {
        $id = $this->getRequest()->getParam(ModuleConfig::ADMIN_URL_ENTITY_ID);
        try {
            $this->repository->deleteById($id);
        } catch (\Exception $e) {

        }

        return $this->_redirect('*/*');
    }
}
