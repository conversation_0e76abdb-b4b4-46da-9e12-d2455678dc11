<?php
declare(strict_types=1);

namespace LimeSoda\Banner\Controller\Adminhtml\Banner;

use Magento\Backend\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface as HttpPostActionInterface;
use Magento\Ui\Component\MassAction\Filter;
use Magento\Framework\Controller\ResultFactory;
use Magento\Backend\App\Action;
use LimeSoda\Banner\Model\ResourceModel\Banner\CollectionFactory;
use LimeSoda\Banner\Model\Banner\MassAction;
use Psr\Log\LoggerInterface;

class MassDeactivate extends Action implements HttpPostActionInterface
{
    /**
     * @var CollectionFactory
     */
    private $bannerCollectionFactory;

    /**
     * @var Filter
     */
    private $filter;

    /**
     * @var MassAction
     */
    private $bannerMassAction;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @param Context $context
     * @param Filter $filter
     * @param CollectionFactory $collectionFactory
     * @param MassAction $bannerMassAction
     * @param LoggerInterface $logger
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory,
        MassAction $bannerMassAction,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->filter = $filter;
        $this->bannerCollectionFactory = $collectionFactory;
        $this->bannerMassAction = $bannerMassAction;
        $this->logger = $logger;
    }

    /**
     * Deactivate banners.
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        try {
            $collection = $this->filter->getCollection($this->bannerCollectionFactory->create());
            $this->bannerMassAction->deactivate($collection);
            $this->messageManager->addSuccessMessage(
                __('A total of %1 record(s) were updated.', $this->bannerMassAction->getUpdatedCount($collection))
            );
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('Something went wrong while deactivating banner(s).'));
            $this->logger->error($e->getMessage());
        }

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setPath('*/*/index');

        return $resultRedirect;
    }
}
