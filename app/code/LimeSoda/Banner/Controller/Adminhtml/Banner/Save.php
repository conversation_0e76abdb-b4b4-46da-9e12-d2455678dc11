<?php

namespace LimeSoda\Banner\Controller\Adminhtml\Banner;

use Magento\Backend\App\Action\Context;
use Magento\MediaStorage\Model\File\UploaderFactory;
use LimeSoda\Banner\Model\BannerRepository;
use LimeSoda\Banner\Model\Banner\ModuleConfig;
use LimeSoda\Banner\Controller\Adminhtml\Action;

class Save extends Action
{
    /**
     * @var BannerRepository
     */
    protected $repository;

    /**
     * @var UploaderFactory
     */
    protected $uploaderFactory;

    /**
     * Save constructor.
     * @param Context $context
     * @param BannerRepository $repository
     * @param UploaderFactory $uploaderFactory
     */
    public function __construct(
        Context $context,
        BannerRepository $repository,
        UploaderFactory $uploaderFactory
    )
    {
        parent::__construct($context);
        $this->repository = $repository;
        $this->uploaderFactory = $uploaderFactory;
    }

    public function execute()
    {
        $data = $this->getRequest()->getPostValue();

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();

        $model = $this->getModelFromRequest();

        if (isset($data['banner_image'][0]['name'])) {
            $data['image'] = $data['banner_image'][0]['name'];
        } else {
            $data['image'] = null;
        }

        if (isset($data['banner_image_fallback'][0]['name'])) {
            $data['fallback_image'] = $data['banner_image_fallback'][0]['name']; //banner_image_fallback
        } else {
            $data['fallback_image'] = null;
        }

        if (isset($data['category']) && is_array($data['category'])) {
            $idValues = array_values($data['category']);
            $data['category'] = is_array($idValues) ? implode(',', $idValues) : $idValues;
        }

        if (isset($data[ModuleConfig::DB_PK]) && empty($data[ModuleConfig::DB_PK])) {
            unset($data[ModuleConfig::DB_PK]);
        }

        $model->setData($data);

        try {
            $this->repository->save($model);

            $this->getMessageManager()->addSuccess(__('Item Updated'));
            $this->_session->setFormData(false);
            if ($this->getRequest()->getParam('back')) {
                return $resultRedirect->setPath(
                    '*/*/edit',
                    [
                        ModuleConfig::ADMIN_URL_ENTITY_ID => $model->getId(),
                        '_current' => true
                    ]
                );
            }
            return $resultRedirect->setPath('*/*/');
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $this->messageManager->addError($e->getMessage());
        } catch (\RuntimeException $e) {
            $this->getMessageManager()->addError($e->getMessage());
        } catch (\Exception $e) {
            $this->getMessageManager()->addException($e, __('Something went wrong while saving.'));
        }

        $this->_getSession()->setFormData($data);

        return $resultRedirect->setPath(
            '*/*/edit',
            [
                ModuleConfig::ADMIN_URL_ENTITY_ID =>
                    $this->getRequest()->getParam(ModuleConfig::ADMIN_URL_ENTITY_ID)
            ]
        );

        return $resultRedirect->setPath('*/*/');
    }

    /**
     * @return \LimeSoda\Banner\Model\Banner|mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function getModelFromRequest()
    {
        $model = $this->repository->getEmptyItem();
        $id = $this->getRequest()->getParam(ModuleConfig::ADMIN_URL_ENTITY_ID);
        if ($id) {
            $model = $this->repository->getById($id);
        }

        return $model;
    }
}
