<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <attribute name="class" value="category-with-slider" />
        <referenceBlock name="category.image" remove="true" />
        <referenceContainer name="page.top">
            <block class="LimeSoda\Banner\Block\Catalog\Category\Slider"
                   name="category.slider" before="page.main.title"
                   template="LimeSoda_Banner::banner/slider-background.phtml" />
        </referenceContainer>
        <referenceContainer name="columns">
            <block class="Magento\Theme\Block\Html\Title" name="page.main.title" template="html/title.phtml"  before="-"/>
        </referenceContainer>
        <move element="category.description" destination="category.view.container" before="-" />
    </body>
</page>
