<?php $banners = $block->getBannerCollection(); ?>
<div class="slick-slider--outer js-multicol--outer">
    <div class="theme-container js-multicol-banners slick-slider--default" >
        <?php foreach ($banners as $banner) : ?>
            <?php
            $dataSrcSet = "data-src=\"{$block->escapeUrl($banner->getData('image_default_data_src'))}\"";
            $srcSetParts = explode(',', $banner->getData('image_set_background'));
            if (count($srcSetParts) <= 1) {
                $dataSrcSet = '';
            }
            ?>
            <div class="banner__item">
                <div class="banner__item--outer banner--background  ratio-container  lazyload"
                     data-expand="1"
                     data-sizes="auto"
                     data-bgset="<?= $block->escapeHtml($banner->getData('image_set_background')) ?>"
                    <?= $dataSrcSet ?>>
                    <div class="banner__item--inner">
                        <?php if ($banner->getData('heading') || $banner->getData('content') || $banner->getData('link_text')): ?>
                            <div class="banner__content banner__content--absolute is-absolute">
                                <?php if ($banner->getData('heading')): ?>
                                    <h2 class="banner__header">
                                        <?php echo $block->escapeHtml($banner->getData('heading')); ?>
                                    </h2>
                                <?php endif; ?>
                                <?php if ($banner->getData('content')): ?>
                                    <p class="banner__description">
                                        <?php echo $block->escapeHtml($banner->getData('content')); ?>
                                    </p>
                                <?php endif; ?>
                                <?php if ($banner->getData('link_text')): ?>
                                    <a href="<?php echo $block->escapeUrl($block->getLinkUrl($banner)); ?>"
                                       class="banner__button"><?php echo $block->escapeHtml($banner->getData('link_text')); ?><svg class="category-promote-svg" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                                                                                                                   viewBox="0 0 37.8 10.1" style="enable-background:new 0 0 37.8 10.1;" xml:space="preserve">
<polygon class="st0" points="31.9,0 30.9,1.1 34.6,4.3 0,4.3 0,5.8 34.6,5.8 30.9,9 31.9,10.1 37.8,5.1 "/>
</svg></a>
                                <?php endif; ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
