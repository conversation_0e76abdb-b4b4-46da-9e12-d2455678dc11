<?php
    $banners = $block->getBannerCollection();
    $resizerHelper = $block->getImageResizer();
?>
<div class="slick-slider--outer">
    <div class="js-content-banners slick-slider--default">
        <?php foreach ($banners as $banner) : ?>
            <?php
            $resizerSourceImageUrl = $block->escapeUrl($banner->getImageUrl());
            $resizedImageUrlDefault = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 800, 473);
            $resizeImageUrlM = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 600, 355);
            $resizeImageUrlS = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 420, 249);
            $resizeImageUrlXS = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 240, 142);
            ?>
            <?php
            $dataSrcSet = "data-src=\"{$block->escapeUrl($resizedImageUrlDefault)}\"";
            $srcSetParts = explode(',', $banner->getData('adaptive_images_data_list'));
            ?>
            <div class="banner__item">
                <div class="ratio-container">
                    <a href="<?php echo $resizerSourceImageUrl ?>">
                        <img class="lazyload banner__image"
                            <?php /* @escapeNotVerified */ echo $block->getCustomAttributes(); ?>
                             width="<?php /* @escapeNotVerified */ echo $block->getWidth(); ?>"
                             height="<?php /* @escapeNotVerified */ echo $block->getHeight(); ?>"
                             alt="<?php /* @escapeNotVerified */ echo $block->stripTags($block->getLabel(), null, true); ?>"
                             data-expand="1"
                             data-sizes="auto"
                             data-srcset="<?php echo $resizedImageUrlDefault ?> 800w, <?php echo $resizeImageUrlM ?> 600w, <?php echo $resizeImageUrlS ?> 420w, <?php echo $resizeImageUrlXS ?> 240w"
                        />
                    </a>
                    <?php if ($banner->getData('heading') || $banner->getData('content') || $banner->getData('link_text')): ?>
                        <div class="banner__content banner__content--absolute is-absolute">
                            <?php if ($banner->getData('heading')): ?>
                                <h2 class="banner__header">
                                    <?php echo $block->escapeHtml($banner->getData('heading')); ?>
                                </h2>
                            <?php endif; ?>
                            <?php if ($banner->getData('content')): ?>
                                <p class="banner__description">
                                    <?php echo $block->escapeHtml($banner->getData('content')); ?>
                                </p>
                            <?php endif; ?>
                            <?php if ($banner->getData('link_text')): ?>
                                <a href="<?php echo $block->escapeUrl($block->getLinkUrl($banner)); ?>"
                                   class="banner__button"><?php echo $block->escapeHtml($banner->getData('link_text')); ?></a>
                            <?php endif; ?>

                        </div>
                    <?php endif; ?>
                    <?php if ($banner->getData('secondary_content')): ?>
                        <div class="banner__bubble bubble is-circle">
                            <p class="bubble__content"><?php echo nl2br($block->escapeHtml($banner->getData('secondary_content'))) ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
