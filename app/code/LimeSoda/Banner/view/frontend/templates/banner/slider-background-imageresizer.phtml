<?php
    $banners = $block->getBannerCollection();
    $resizerHelper = $block->getImageResizer();
?>
<div class="js-home-banners" >
    <?php foreach ($banners as $banner) : ?>

        <?php
        /** @var \Staempfli\ImageResizer\Helper\Resizer $resizerHelper */
        $resizerSourceImageUrl = $block->escapeUrl($banner->getImageUrl());
        $resizedImageUrlBig = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 1600, 625);
        $resizedImageUrlDesktop = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 1200, 469);
        $resizedImageUrlTablet = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 970, 379);
        $resizedImageUrlMobile = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 767, 300);
        $resizedImageUrlMobileS = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 370, 145);
        $resizedImageUrlMobileXs = $resizerHelper->resizeAndGetUrl($resizerSourceImageUrl, 245, 96);
        ?>
        <div class="banner-background ratio-container lazyload" data-expand="1" data-sizes="auto" data-bgset="<?php echo $block->escapeUrl($resizedImageUrlMobileS); ?> 767w, <?php echo $block->escapeUrl($resizedImageUrlMobile); ?> 970w, <?php echo $block->escapeUrl($resizedImageUrlTablet); ?> 1200w, <?php echo $block->escapeUrl($resizedImageUrlDesktop); ?> 1600w, <?php echo $block->escapeUrl($resizedImageUrlBig); ?> 1601w">
            <div class="vertikal-align-middle">
                <?php if($block->escapeHtml($banner->getData('heading'))== !null): ?>
                    <h2 class="banner-header"><?php echo $block->escapeHtml($banner->getData('heading')); ?></h2>
                <?php endif; ?>
                <?php if($block->escapeHtml($banner->getData('content'))== !null): ?>
                    <p class="banner-description"><?php echo $block->escapeHtml($banner->getData('content')); ?></p>
                <?php endif; ?>
                <?php if($block->escapeHtml($banner->getData('link_text'))== !null): ?>
                    <a href="<?php echo $block->escapeUrl($block->getLinkUrl($banner)); ?>" class="bannerbutton"><?php echo $block->escapeHtml($banner->getData('link_text')); ?></a>
                <?php endif; ?>
                <?php if($block->escapeHtml($banner->getData('secondary_content'))== !null): ?>
                    <div class="banner-bubble circle">
                        <p><?php echo nl2br($block->escapeHtml($banner->getData('secondary_content'))) ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>
</div>