<?php $banners = $block->getBannerCollection(); ?>
<div class="slick-slider--outer">
    <div class="js-home-banners  slick-slider--default" >
        <?php foreach ($banners as $banner) : ?>
            <?php
            $dataSrcSet = "data-src=\"{$block->escapeUrl($banner->getData('image_default_data_src'))}\"";
            $srcSetParts = explode(',', $banner->getData('image_set_background'));
            if (count($srcSetParts) <= 1) {
                $dataSrcSet = '';
            }
            ?><div class="banner__item">
            <div class="banner--background  ratio-container  lazyload"
                 data-expand="1"
                 data-sizes="auto"
                 data-bgset="<?= $block->escapeHtml($banner->getData('image_set_background')) ?>"
            >
                <?php if ($banner->getData('heading') || $banner->getData('content') || $banner->getData('link_text')): ?>
                    <div class="banner__content banner__content--relative is-absolute">
                        <?php if ($banner->getData('heading')): ?>
                            <h2 class="banner__header">
                                <?php echo $block->escapeHtml($banner->getData('heading')); ?>
                            </h2>
                        <?php endif; ?>
                        <?php if ($banner->getData('content')): ?>
                            <p class="banner__description">
                                <?php echo $block->escapeHtml($banner->getData('content')); ?>
                            </p>
                        <?php endif; ?>
                        <?php if ($banner->getData('link_text')): ?>
                            <a href="<?php echo $block->escapeUrl($block->getLinkUrl($banner)); ?>"
                               class="banner__button"><?php echo $block->escapeHtml($banner->getData('link_text')); ?></a>
                        <?php endif; ?>

                    </div>
                <?php endif; ?>
                <?php if ($banner->getData('secondary_content')): ?>
                    <div class="banner__bubble bubble is-circle">
                        <p class="bubble__content"><?php echo nl2br($block->escapeHtml($banner->getData('secondary_content'))) ?></p>
                    </div>
                <?php endif; ?>


            </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>