<?xml version="1.0" encoding="UTF-8"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">banner_form.banner_form_data_source</item>
            <item name="deps" xsi:type="string">banner_form.banner_form_data_source</item>
        </item>
        <item name="label" xsi:type="string" translate="true">General Information</item>
        <item name="config" xsi:type="array">
            <item name="dataScope" xsi:type="string">data</item>
            <item name="namespace" xsi:type="string">banner_form</item>
        </item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
        <item name="buttons" xsi:type="array">
            <item name="back" xsi:type="string">LimeSoda\Banner\Block\Adminhtml\Banner\Edit\BackButton</item>
            <item name="delete" xsi:type="string">LimeSoda\Banner\Block\Adminhtml\Banner\Edit\DeleteButton</item>
            <item name="save" xsi:type="string">LimeSoda\Banner\Block\Adminhtml\Banner\Edit\SaveButton</item>
            <item name="save_and_continue" xsi:type="string">LimeSoda\Banner\Block\Adminhtml\Banner\Edit\SaveAndContinueButton</item>
        </item>
    </argument>
    <dataSource name="banner_form_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">LimeSoda\Banner\Model\Banner\DataProvider</argument>
            <argument name="name" xsi:type="string">banner_form_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">banner_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="submit_url" xsi:type="url" path="banners/banner/save"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>
    </dataSource>
    <fieldset name="general">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="label" xsi:type="string"/>
            </item>
        </argument>
        <field name="banner_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="visible" xsi:type="boolean">false</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="dataScope" xsi:type="string">banner_id</item>
                </item>
            </argument>
        </field>
        <field name="code">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Code</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="dataScope" xsi:type="string">code</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Name</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="dataScope" xsi:type="string">name</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="is_active">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Magento\Config\Model\Config\Source\Yesno</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Is Active</item>
                    <item name="formElement" xsi:type="string">select</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">15</item>
                    <item name="dataScope" xsi:type="string">is_active</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="heading">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Heading</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                    <item name="dataScope" xsi:type="string">heading</item>
                </item>
            </argument>
        </field>
        <field name="content">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Content</item>
                    <item name="formElement" xsi:type="string">textarea</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                    <item name="dataScope" xsi:type="string">content</item>
                </item>
            </argument>
        </field>
        <field name="secondary_content">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Secondary Content</item>
                    <item name="formElement" xsi:type="string">textarea</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                    <item name="dataScope" xsi:type="string">secondary_content</item>
                </item>
            </argument>
        </field>
        <field name="link_text">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Link Text</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                    <item name="dataScope" xsi:type="string">link_text</item>
                </item>
            </argument>
        </field>
        <field name="link">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Link</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">80</item>
                    <item name="dataScope" xsi:type="string">link</item>
                </item>
            </argument>
        </field>
        <field name="banner_image">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="label" xsi:type="string" translate="true">Image</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="formElement" xsi:type="string">fileUploader</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
                    <item name="previewTmpl" xsi:type="string">Magento_Catalog/image-preview</item>
                    <item name="required" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="banners/banner/image"/>
                    </item>
                </item>
            </argument>
        </field>
        <field name="banner_image_fallback">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="label" xsi:type="string" translate="true">Fallback Image</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="formElement" xsi:type="string">fileUploader</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
                    <item name="previewTmpl" xsi:type="string">Magento_Catalog/image-preview</item>
                    <item name="required" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="banners/banner/fallback"/>
                    </item>
                </item>
            </argument>
        </field>
        <field name="css_classes">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">CSS Classes</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="source" xsi:type="string">banner</item>
                    <item name="sortOrder" xsi:type="number">200</item>
                    <item name="dataScope" xsi:type="string">css_classes</item>
                </item>
            </argument>
        </field>
        <field name="category">
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">LimeSoda\Banner\Model\Source\BannerCategoryOptions</item>
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Banner Category Options List</item>
                    <item name="componentType" xsi:type="string">field</item>
                    <item name="formElement" xsi:type="string">multiselect</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/form/element/multiselect</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/multiselect</item>
                    <item name="dataScope" xsi:type="string">category</item>
                    <item name="sortOrder" xsi:type="number">400</item>
                </item>
            </argument>
        </field>
    </fieldset>
</form>
