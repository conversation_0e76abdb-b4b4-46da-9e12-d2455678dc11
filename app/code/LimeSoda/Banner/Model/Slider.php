<?php declare(strict_types=1);

namespace LimeSoda\Banner\Model;

use Magento\Framework\Model\AbstractModel;
use LimeSoda\Banner\Api\Data\SliderInterface;

class Slider extends AbstractModel implements SliderInterface
{
    const CACHE_TAG = 'banner_slider';

    const NAME = 'name';

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    protected function _construct()
    {
        $this->_init(\LimeSoda\Banner\Model\ResourceModel\Slider::class);
    }

    /**
     * Get vendor name
     *
     * @return string
     */
    public function getName()
    {
        return $this->_getData(self::NAME);
    }

    /**
     * @param string $name
     * @return $this
     */
    public function setName($name)
    {
        return $this->setData(self::NAME, $name);
    }

    public function loadBannerIdsArray()
    {
        if (isset($this['slider_banner_ids'])) {
            $ids = explode(',', $this['slider_banner_ids']);
            $sortOrder = count($ids);
            $banners = [];
            foreach ($ids as $index => $id) {
                $banners[$index] = ['banner_id' => $id, 'sort_order' => $sortOrder];
                if ($sortOrder > 0) {
                    $sortOrder--;
                }
            }

            $this->setData('slider_banners', $banners);
        }

        return $this;
    }

    public function afterLoad()
    {
        $this->loadBannerIdsArray();

        return parent::afterLoad();
    }

    public function getBannerIds(): array
    {
        $this->loadBannerIdsArray();
        $ids = array_filter(explode(',', (string) $this->getData('slider_banner_ids')));

        return array_reverse($ids);
    }

    public function isActive() : bool
    {
        return (bool) $this->getData('is_active');
    }
}
