<?php
namespace LimeSoda\Banner\Model\Source;

use Magento\Framework\Option\ArrayInterface;
use LimeSoda\Banner\Model\ResourceModel\Slider\CollectionFactory;

class Slider implements ArrayInterface
{
    protected $options = [];

    /**
     * Block collection factory
     *
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(CollectionFactory $collectionFactory)
    {
        $this->collectionFactory = $collectionFactory;
    }

    public function getAllOptions()
    {
        if (!$this->options) {
            $this->options = $this->collectionFactory->create()->load()->toOptionArray();
            array_unshift($this->options, ['value' => '', 'label' => __('-- No Slider Selected --')]);
        }

        return $this->options;
    }

    public function toOptionArray()
    {
        return $this->getAllOptions();
    }
}
