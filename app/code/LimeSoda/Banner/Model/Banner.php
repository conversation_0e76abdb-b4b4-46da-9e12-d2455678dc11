<?php

namespace LimeSoda\Banner\Model;

use Magento\Framework\Model\AbstractModel;
use LimeSoda\Banner\Api\Data\BannerInterface;
use Magento\Framework\DataObject\IdentityInterface;
use LimeSoda\Banner\Model\Slider;

class Banner extends AbstractModel implements BannerInterface,
    IdentityInterface
{
    public const CACHE_TAG = 'banner_banner';

    public const NAME = 'name';

    public const CATEGORY = 'category';

    protected $uploader;

    protected $storeManager;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Catalog\Model\ImageUploader $imageUploader,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    )
    {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
        $this->uploader = $imageUploader;
        $this->storeManager = $storeManager;
    }

    protected function _construct()
    {
        $this->_init('LimeSoda\Banner\Model\ResourceModel\Banner');
    }

    /**
     * Get vendor name
     *
     * @return string
     */
    public function getName()
    {
        return $this->_getData(self::NAME);
    }

    /**
     * @param string $name
     * @return $this
     */
    public function setName($name)
    {
        return $this->setData(self::NAME, $name);
    }

    /**
     * @return mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */

    public function getMediaUrl() {
        return $this->storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
    }

    /**
     * @return string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getImageUrl()
    {
        $url = '';
        $image = $this->getData('image');
        if ($image && is_string($image)) {
            $uploader = $this->uploader;
            $url = $this->getMediaUrl() . $uploader->getBasePath() . $image;
        }

        return $url;
    }

    /**
     * @return string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */

    public function getFallbackImageUrl()
    {
        $fallbackUrl = '';
        $fallbackImage = $this->getData('fallback_image');

        if ($fallbackImage && is_string($fallbackImage)) {
            $uploader = $this->uploader;
            $fallbackUrl = $this->getMediaUrl() . $uploader->getBasePath() . $fallbackImage;
        }
        return $fallbackUrl;
    }

    /**
     * Get category.
     *
     * @return string
     */
    public function getCategory(): string
    {
        return $this->getData(self::CATEGORY);
    }

    /**
     * Set category.
     *
     * @var string $category
     * @return $this
     */
    public function setCategory(string $category)
    {
        return $this->setData(self::CATEGORY, $category);
    }

    /**
     * @return array
     */
    public function getIdentities()
    {
        return [Slider::CACHE_TAG . '_'];
    }
}
