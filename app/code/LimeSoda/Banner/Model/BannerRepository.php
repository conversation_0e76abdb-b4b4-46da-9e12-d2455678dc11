<?php
namespace LimeSoda\Banner\Model;

use LimeSoda\Banner\Api\Data\BannerInterface;
use LimeSoda\Banner\Api\BannerRepositoryInterface;
use LimeSoda\Banner\Model\ResourceModel\Banner as ResourceBanner;
use LimeSoda\Banner\Model\BannerFactory as ModelFactory;
use LimeSoda\Banner\Model\ResourceModel\Banner\CollectionFactory;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;

class BannerRepository implements BannerRepositoryInterface
{
    /**
     * @var ResourceBanner
     */
    protected $resource;

    /**
     * @var BannerFactory
     */
    protected $modelFactory;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $storeManager;

    public function __construct(
        ResourceBanner $resource,
        ModelFactory $modelFactory,
        CollectionFactory $collectionFactory,
        StoreManagerInterface $storeManager
    ) {
        $this->resource = $resource;
        $this->modelFactory = $modelFactory;
        $this->collectionFactory = $collectionFactory;
        $this->storeManager = $storeManager;
    }

    /**
     * @param BannerInterface $model
     * @return BannerInterface
     * @throws CouldNotSaveException
     */
    public function save(BannerInterface $model)
    {
        try {
            $this->resource->save($model);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__($exception->getMessage()));
        }

        return $model;
    }

    /**
     * @param int $Id
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function getById($Id)
    {
        $model = $this->modelFactory->create();
        $this->resource->load($model, $Id);
        if (!$model->getId()) {
            throw new NoSuchEntityException(__('Item with id "%1" does not exist.', $Id));
        }

        return $model;
    }

    /**
     * @param BannerInterface $block
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(BannerInterface $block)
    {
        try {
            $this->resource->delete($block);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__($exception->getMessage()));
        }
        return true;
    }

    /**
     * @param int $id
     * @return bool
     * @throws CouldNotDeleteException
     * @throws NoSuchEntityException
     */
    public function deleteById($id)
    {
        return $this->delete($this->getById($id));
    }

    public function getByCode($code)
    {
        $model = $this->modelFactory->create();
        $this->resource->load($model, $code, 'code');
        if (!$model->getId()) {
            throw new NoSuchEntityException(__('Item with code "%1" does not exist.', $code));
        }

        return $model;
    }

    public function getEmptyItem()
    {
        return $this->collectionFactory->create()->getNewEmptyItem();
    }

    public function getCollection()
    {
        return $this->collectionFactory->create();
    }

    public function getCollectionForSlider($slider)
    {
        $collection = $this->getCollection();
        $collection->addFieldToFilter('banner_id', ['in' => $slider->getBannerIds()]);
        $collection->getSelect()->order(
            new \Zend_Db_Expr('FIELD(banner_id, ' . implode(',', $slider->getBannerIds()).')')
        );

        return $collection;
    }
}
