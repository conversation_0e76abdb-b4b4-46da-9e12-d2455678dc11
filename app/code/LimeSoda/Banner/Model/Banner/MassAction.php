<?php
declare(strict_types=1);

namespace LimeSoda\Banner\Model\Banner;

use LimeSoda\Banner\Api\BannerRepositoryInterface;
use LimeSoda\Banner\Model\MassActionInterface;
use Magento\Framework\Exception\LocalizedException;

class MassAction implements MassActionInterface
{
    /**
     * @var BannerRepositoryInterface
     */
    private $bannerRepository;

    /**
     * @param BannerRepositoryInterface $bannerRepository
     */
    public function __construct(
        BannerRepositoryInterface $bannerRepository
    ) {
        $this->bannerRepository = $bannerRepository;
    }

    /**
     * @inheritdoc
     */
    public function activate($collection): bool
    {
        return $this->massAction($collection, 1);
    }

    /**
     * @inheritdoc
     */
    public function deactivate($collection): bool
    {
        return $this->massAction($collection, 0);
    }

    /**
     * Banner mass action (de)activate.
     *
     * @return bool
     * @throws LocalizedException
     */
    public function massAction($collection, int $isActive): bool
    {
        foreach ($collection->getAllIds() as $bannerId) {
            $banner = $this->bannerRepository->getById($bannerId);
            $banner->setIsActive($isActive);
            $this->bannerRepository->save($banner);
        }

        return true;
    }

    /**
     * @inheritdoc
     */
    public function getUpdatedCount($collection): int
    {
        return count($collection->getAllIds());
    }
}
