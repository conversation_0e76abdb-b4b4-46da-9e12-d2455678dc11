<?php
namespace LimeSoda\Banner\Model;

use LimeSoda\Banner\Api\Data\SliderInterface;
use LimeSoda\Banner\Api\SliderRepositoryInterface;
use LimeSoda\Banner\Model\ResourceModel\Slider as ResourceSlider;
use LimeSoda\Banner\Model\SliderFactory as ModelFactory;
use LimeSoda\Banner\Model\ResourceModel\Slider\CollectionFactory;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;

class SliderRepository implements SliderRepositoryInterface
{
    /**
     * @var ResourceSlider
     */
    protected $resource;

    /**
     * @var SliderFactory
     */
    protected $modelFactory;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $storeManager;

    public function __construct(
        ResourceSlider $resource,
        ModelFactory $modelFactory,
        CollectionFactory $collectionFactory,
        StoreManagerInterface $storeManager
    ) {
        $this->resource = $resource;
        $this->modelFactory = $modelFactory;
        $this->collectionFactory = $collectionFactory;
        $this->storeManager = $storeManager;
    }

    /**
     * @param SliderInterface $model
     * @return SliderInterface
     * @throws CouldNotSaveException
     */
    public function save(SliderInterface $model)
    {
        $storeId = $this->storeManager->getStore()->getId();
        $model->setStoreId($storeId);
        try {
            $this->resource->save($model);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__($exception->getMessage()));
        }

        return $model;
    }

    /**
     * @param int $Id
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function getById($Id)
    {
        $model = $this->modelFactory->create();
        $this->resource->load($model, $Id);
        if (!$model->getId()) {
            throw new NoSuchEntityException(__('Item with id "%1" does not exist.', $Id));
        }

        return $model;
    }

    /**
     * @param SliderInterface $block
     * @return bool
     * @throws CouldNotDeleteException
     */
    public function delete(SliderInterface $block)
    {
        try {
            $this->resource->delete($block);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__($exception->getMessage()));
        }

        return true;
    }

    /**
     * @param int $id
     * @return bool
     * @throws CouldNotDeleteException
     * @throws NoSuchEntityException
     */
    public function deleteById($id)
    {
        return $this->delete($this->getById($id));
    }

    public function getByCode($code)
    {
        $model = $this->modelFactory->create();
        $this->resource->load($model, $code, 'code');
        if (!$model->getId()) {
            throw new NoSuchEntityException(__('Item with code "%1" does not exist.', $code));
        }

        return $model;
    }

    public function getEmptyItem()
    {
        return $this->collectionFactory->create()->getNewEmptyItem();
    }
}
