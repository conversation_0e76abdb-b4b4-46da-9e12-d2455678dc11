<?php
declare(strict_types=1);

namespace LimeSoda\Banner\Model;

interface MassActionInterface
{
    /**
     * Entity mass activate.
     *
     * @param mixed $collection
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function activate($collection): bool;

    /**
     * Entity mass deactivate.
     *
     * @param mixed $collection
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deactivate($collection): bool;

    /**
     * Get updated entities count.
     *
     * @param $collection
     * @return int
     */
    public function getUpdatedCount($collection): int;
}
