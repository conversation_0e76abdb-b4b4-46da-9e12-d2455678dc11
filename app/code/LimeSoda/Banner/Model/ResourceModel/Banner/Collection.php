<?php
namespace LimeSoda\Banner\Model\ResourceModel\Banner;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    /**
     * @var string
     */
    protected $_idFieldName = 'banner_id';

    /**
     * @inheritdoc
     */
    protected function _construct()
    {
        $this->_init('LimeSoda\Banner\Model\Banner', 'LimeSoda\Banner\Model\ResourceModel\Banner');
    }

    /**
     * @return array
     */
    public function toOptionArray()
    {
        return $this->_toOptionArray('banner_id', 'name');
    }
}
