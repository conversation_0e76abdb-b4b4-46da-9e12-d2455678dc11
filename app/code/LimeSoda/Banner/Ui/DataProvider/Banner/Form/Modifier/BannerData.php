<?php
namespace LimeSoda\Banner\Ui\DataProvider\Banner\Form\Modifier;

use Magento\Ui\DataProvider\Modifier\ModifierInterface;
use LimeSoda\Banner\Model\ResourceModel\Banner\CollectionFactory;

class BannerData implements ModifierInterface
{
    /**
     * @var \Sample\News\Model\ResourceModel\Author\Collection
     */
    protected $collection;

    /**
     * @param CollectionFactory $authorCollectionFactory
     */
    public function __construct(
        CollectionFactory $authorCollectionFactory
    ) {
        $this->collection = $authorCollectionFactory->create();
    }

    /**
     * @param array $meta
     * @return array
     */
    public function modifyMeta(array $meta)
    {
        return $meta;
    }

    /**
     * @param array $data
     * @return array|mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function modifyData(array $data)
    {
        $items = $this->collection->getItems();
        foreach ($items as $item) {
            $_data = $item->getData();
            $image = [];
            if (isset($_data['image'])) {
                $image[0]['name'] = $item->getData('image');
                $image[0]['url'] = $item->getImageUrl();
                $_data['banner_image'] = $image;
            }

            if (isset($_data['fallback_image'])) {
                $image[0]['name'] = $item->getData('fallback_image');
                $image[0]['url'] = $item->getFallbackImageUrl();
                $_data['banner_image_fallback'] = $image;
            }

            $item->setData($_data);
            $data[$item->getId()] = $_data;
        }

        return $data;
    }
}
