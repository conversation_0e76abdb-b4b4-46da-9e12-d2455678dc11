<?php
namespace LimeSoda\Banner\Ui\Component\Listing\Column\Lsbannerslidergrid;

class PageActions extends \Magento\Ui\Component\Listing\Columns\Column
{
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                $name = $this->getData('name');
                $id = 'X';
                if (isset($item['slider_id'])) {
                    $id = $item['slider_id'];
                }
                $item[$name]['view'] = [
                    'href' => $this->getContext()->getUrl(
                        'banners/slider/edit',
                        ['id' => $id]
                    ),
                    'label' => __('Edit')
                ];
            }
        }

        return $dataSource;
    }
}
