<?php
namespace LimeSoda\Banner\Ui\Component\Listing\Column\Lsbannerbannergrid;

use Magento\Ui\Component\Listing\Columns\Column;

class PageActions extends Column
{
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as &$item) {
                $name = $this->getData('name');
                $id = 'X';
                if (isset($item['banner_id'])) {
                    $id = $item['banner_id'];
                }
                $item[$name]['view'] = [
                    'href' => $this->getContext()->getUrl(
                        'banners/banner/edit', ['id' => $id]),
                    'label' => __('Edit')
                ];
            }
        }

        return $dataSource;
    }
}
