<?php
declare(strict_types=1);

namespace LimeSoda\Banner\Ui\Component\Listing\FulltextSearch;

use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Data\Collection;

/**
 * Adds fulltext filter.
 */
class Banner
{
    /**
     * @var FilterBuilder
     */
    private $filterBuilder;

    /**
     * @param FilterBuilder $filterBuilder
     */
    public function __construct(FilterBuilder $filterBuilder)
    {
        $this->filterBuilder = $filterBuilder;
    }

    /**
     * @inheritdoc
     */
    public function addFilter(Collection $collection, $value = null)
    {
        $collection->addFieldToFilter(
            ['name', 'code'],
            [
                ['like' => sprintf('%%%s%%', $value)],
                ['like' => sprintf('%%%s%%', $value)]
            ]
        );
    }
}
