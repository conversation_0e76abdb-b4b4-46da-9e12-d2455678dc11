<?php
namespace LimeSoda\Banner\Ui\Component\Listing\DataProviders\Ls\Banner\Slider;

use Magento\Ui\DataProvider\AbstractDataProvider;
use Magento\Framework\Api\Filter;

class Grid extends AbstractDataProvider
{
    /**
     * @var array
     */
    private $additionalFilterPool;

    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        \LimeSoda\Banner\Model\ResourceModel\Slider\CollectionFactory $collectionFactory,
        array $meta = [],
        array $data = [],
        array $additionalFilterPool = []
    ) {
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
        $this->collection = $collectionFactory->create();
        $this->additionalFilterPool = $additionalFilterPool;
    }

    /**
     * Additional filtration.
     *
     * @param Filter $filter
     * @return mixed|void
     */
    public function addFilter(Filter $filter)
    {
        if (isset($this->additionalFilterPool[$filter->getField()])) {
            $this->additionalFilterPool[$filter->getField()]
                ->addFilter($this->getCollection(), $filter->getValue());
        } else {
            parent::addFilter($filter);
        }
    }
}
