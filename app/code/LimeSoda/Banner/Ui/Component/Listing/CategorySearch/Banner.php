<?php
declare(strict_types=1);

namespace LimeSoda\Banner\Ui\Component\Listing\CategorySearch;

use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Data\Collection;
use LimeSoda\Banner\Model\Banner as BannerModel;

/**
 * Adds category filter.
 */
class Banner
{
    /**
     * @var FilterBuilder
     */
    private $filterBuilder;

    /**
     * @param FilterBuilder $filterBuilder
     */
    public function __construct(FilterBuilder $filterBuilder)
    {
        $this->filterBuilder = $filterBuilder;
    }

    /**
     * @inheritdoc
     */
    public function addFilter(Collection $collection, $value = null)
    {
        $collection->addFieldToFilter(
            [
                BannerModel::CATEGORY,
                BannerModel::CATEGORY,
                BannerModel::CATEGORY,
                BannerModel::CATEGORY
            ],
            [
                ['like' => sprintf('%%,%s,%%', $value)],
                ['like' => sprintf('%s,%%', $value)],
                ['like' => sprintf('%%,%s', $value)],
                ['like' => sprintf('%s', $value)],
            ]
        );
    }
}
