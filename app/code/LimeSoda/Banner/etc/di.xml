<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <preference for="LimeSoda\Banner\Api\SliderRepositoryInterface" type="LimeSoda\Banner\Model\SliderRepository" />
    <preference for="LimeSoda\Banner\Api\BannerRepositoryInterface" type="LimeSoda\Banner\Model\BannerRepository" />
    <preference for="Staempfli\ImageResizer\Model\Resizer" type="LimeSoda\Banner\Model\Resizer" />

    <virtualType name="LimeSoda\Banner\Model\ImageUploader" type="Magento\Catalog\Model\ImageUploader">
        <arguments>
            <argument name="baseTmpPath" xsi:type="string">slider/tmp/</argument>
            <argument name="basePath" xsi:type="string">slider/</argument>
            <argument name="allowedExtensions" xsi:type="array">
                <item name="jpg" xsi:type="string">jpg</item>
                <item name="jpeg" xsi:type="string">jpeg</item>
                <item name="gif" xsi:type="string">gif</item>
                <item name="png" xsi:type="string">png</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="LimeSoda\Banner\Model\Banner">
        <arguments>
            <argument name="imageUploader" xsi:type="object">LimeSoda\Banner\Model\ImageUploader</argument>
        </arguments>
    </type>
</config>
