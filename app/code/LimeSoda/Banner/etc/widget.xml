<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget id="slider" class="LimeSoda\Banner\Block\Slider\Widget" is_email_compatible="false"
            placeholder_image="Magento_CatalogWidget::images/products_list.png" ttl="86400">
        <label translate="true">Slider</label>
        <description translate="true">Slider</description>
        <parameters>
            <parameter name="slider_code" xsi:type="select" visible="true"
                       source_model="LimeSoda\Banner\Model\Source\Slider\Code">
                <label translate="true">Slider</label>
            </parameter>
            <parameter name="slider_type" xsi:type="select" required="true" visible="true">
                <label translate="true">Type</label>
                <options>
                    <option name="image" value="image" selected="true">
                        <label translate="true">Image</label>
                    </option>
                    <option name="background" value="background_image">
                        <label translate="true">Background Image</label>
                    </option>
                </options>
            </parameter>
            <parameter name="template" xsi:type="select" required="true" visible="true">
                <label translate="true">Template</label>
                <options>
                    <option name="default" value="LimeSoda_Banner::banner/slider.phtml" selected="true">
                        <label translate="true">Image Slider</label>
                    </option>
                    <option name="slider_background" value="LimeSoda_Banner::banner/slider-background.phtml">
                        <label translate="true">Image Slider Background</label>
                    </option>
                    <option name="multiple" value="LimeSoda_Banner::banner/slider-multiple.phtml">
                        <label translate="true">Image Slider Multiple Items</label>
                    </option>
                    <option name="slider_image_imageresizer" value="LimeSoda_Banner::banner/slider-imageresizer.phtml">
                        <label translate="true">Image Slider with Image Resizer</label>
                    </option>
                    <option name="slider_background_imageresizer" value="LimeSoda_Banner::banner/slider-background-imageresizer.phtml">
                        <label translate="true">Image Slider Background with Image Resizer</label>
                    </option>
                </options>
            </parameter>
        </parameters>
    </widget>
</widgets>
