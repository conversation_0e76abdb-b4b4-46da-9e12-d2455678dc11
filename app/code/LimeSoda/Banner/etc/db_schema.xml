<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="ls_banner_entity" resource="default" engine="innodb"
           comment="LimeSoda Banner Entity Table">
        <column xsi:type="int" name="banner_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Banner ID"/>
        <column xsi:type="smallint" name="is_active" padding="6" unsigned="false" nullable="false"
                comment="Is Active Banner"/>
        <column xsi:type="varchar" name="name" nullable="true" length="255" comment="Name"/>
        <column xsi:type="varchar" name="code" nullable="true" length="255" comment="Code"/>
        <column xsi:type="varchar" name="image" nullable="true" length="255" comment="Image"/>
        <column xsi:type="varchar" name="fallback_image" nullable="true" length="255" comment="Fallback Image"/>
        <column xsi:type="varchar" name="link" nullable="true" length="255" comment="Link"/>
        <column xsi:type="varchar" name="link_text" nullable="true" length="255" comment="Link Text"/>
        <column xsi:type="varchar" name="heading" nullable="true" length="255" comment="Heading"/>
        <column xsi:type="text" name="content" nullable="true" comment="Content"/>
        <column xsi:type="varchar" name="secondary_content" nullable="true" length="255" comment="Secondary Content"/>
        <column xsi:type="varchar" name="css_classes" nullable="true" length="255" comment="CSS Classes"/>
        <column xsi:type="varchar" name="category" nullable="true" length="255" comment="Banner Category"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Creation Time"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Update Time"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="banner_id"/>
        </constraint>
        <index referenceId="IDX_LS_BANNER_ENTITY_NAME_CODE" indexType="fulltext">
            <column name="name"/>
            <column name="code"/>
        </index>
    </table>
    <table name="ls_slider_entity" resource="default" engine="innodb"
           comment="LimeSoda Slider Entity Table">
        <column xsi:type="int" name="slider_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Slider ID"/>
        <column xsi:type="smallint" name="is_active" padding="6" unsigned="false" nullable="false"
                comment="Is Active Slider"/>
        <column xsi:type="varchar" name="code" nullable="true" length="255" comment="Code"/>
        <column xsi:type="varchar" name="name" nullable="true" length="255" comment="Name"/>
        <column xsi:type="varchar" name="slider_banner_ids" nullable="true" length="255" comment="Name"/>
        <column xsi:type="timestamp" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Creation Time"/>
        <column xsi:type="timestamp" name="updated_at" on_update="true" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Update Time"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="slider_id"/>
        </constraint>
        <index referenceId="IDX_LS_SLIDER_ENTITY_NAME_CODE" indexType="fulltext">
            <column name="name"/>
            <column name="code"/>
        </index>
    </table>
</schema>
