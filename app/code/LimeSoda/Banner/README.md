LimeSoda_Banner
=====================

With this module you have the possibility to create your own custom frontend widget.

Installation Instructions
-------------------------
1, Clone the extension as a composer repository 

```
composer require limesoda/magento2-banner
```

2, Install the module & clear the cache

```
php bin/magento setup:upgrade --keep-generated 
php bin/magento cache:c
php bin/magento cache:f
```

3, Logout from the admin panel and then login again.
 
Uninstallation
--------------

1, Remove via Composer and clear the caches

```
composer remove limesoda/magento2-banner
```

Configuration in backend
-------------------------

To create a widget, the following steps must be taken. 

Here is an example of the procedure:

#### 1. Create Banner (Content > Manage Banners)

##### Banner summary im backend

![Banner Summary im Backend](docs/create_banner_summary.png)

##### Banner configuration

![Banner Setup](docs/create_banner.png)

#### 2. This banner(s) must be linked in a slider (Content > Manage Sliders)

##### Slider summary im backend

![Slider Summary im Backend](docs/create_slider_summary.png)

##### Slider configuration im backend

![Slider Setup im Backend](docs/create_slider.png)

#### 3. The widget must be configured (Content > Widgets)

##### Create widget step 1

![Create widget step 1](docs/create_widget_1.png)

##### Create widget step 2

![Create widget step 2](docs/create_widget_2.png)

##### 4. Frontend view example

![Frontend example](docs/banner_fe.png)

Extendibility
-------

##### Banner & Slider fields configuration & extendibility

The standard fields by Slider & Banner can be changed / supplemented with UI Component.
The standard configuration can be found under: 

    magento2-banner/view/adminhtml/ui_component/banner_form.xml
    
    magento2-banner/view/adminhtml/ui_component/slider_form.xml

##### Template changes & extendibility

The standard templates can be found under:

    magento2-banner/view/frontend/templates/banner/...

These templates can be selected by default under Layout Updates in Widget Configuration.
You have the option to rewrite these templates (use magento standard template rewrite) or create your own.

If you want to use your own widget template:

1, Create your own module

2, Create your template under `app/code/Vendorname/Modulname/view/frontend/templates/`

3, Create your own `widget.xml`. Example:

```
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
	<widget id="slider" class="LimeSoda\Banner\Block\Slider\Widget" is_email_compatible="false"	placeholder_image="Magento_CatalogWidget::images/products_list.png" ttl="86400">
		<label translate="true">Teaser</label>
		<description translate="true">Teaser</description>
		<parameters>
			<parameter name="template" xsi:type="select" required="true" visible="true">
				<label translate="true">Template</label>
				<options>
					<option name="teaser-big" value="Vendorname_Modulname::yourtemplatename.phtml">
						<label translate="true">Your custom template</label>
					</option>
				</options>
			</parameter>
		</parameters>
	</widget>
</widgets>
```

4, Extend module.xml with dependecies:

```
<?xml version="1.0" encoding="utf-8" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Module/etc/module.xsd">
	<module name="Vendorname_Modulname" setup_version="1.0.0" active="true"/>
	<sequence>
		<module name="LimeSoda_Banner"/>
	</sequence>
</config>
```

After this step and modul installation, your custom template can be selected under widget settings.

Support
-------
If you have any issues with this extension, open an issue in the tracker.

ToDo
-------

- version in setup setup/install script check
- admin controller tests
- frontend blocks
- frontend block tests
- clean UI component naming
- category form option model

Developer
---------

LimeSoda Interactive Marketing GmbH  
[http://www.limesoda.com](http://www.limesoda.com)  
[@LimeSoda_at](https://twitter.com/LimeSoda_at)

Licence
-------
[OSL - Open Software Licence 3.0](http://opensource.org/licenses/osl-3.0.php)

Copyright
---------
(c) 2020 LimeSoda Interactive Marketing GmbH
