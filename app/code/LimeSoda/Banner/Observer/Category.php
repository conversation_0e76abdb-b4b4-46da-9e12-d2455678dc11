<?php
namespace LimeSoda\Banner\Observer;

use Magento\Framework\Event\ObserverInterface;

class Category implements ObserverInterface
{
    /**
     * @var \Magento\Framework\Registry
     */
    protected $registry;

    /**
     * Category constructor.
     * @param \Magento\Framework\Registry $registry
     */
    public function __construct(
        \Magento\Framework\Registry $registry
    ) {
        $this->registry = $registry;
    }

    /**
     * customer register event handler
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        if ('catalog_category_view' === $observer->getData('full_action_name')) {
            $category = $this->registry->registry('current_category');
            if ($category && $category->getData('category_slider')) {
                $layout = $observer->getData('layout');
                $layout->getUpdate()->addHandle('category_use_slider');
            }
        }
    }
}
