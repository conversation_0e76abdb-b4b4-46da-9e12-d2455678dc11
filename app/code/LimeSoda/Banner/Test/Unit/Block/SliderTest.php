<?php

namespace LimeSoda\Banner\Test\Unit\Block;

class SliderTest extends \PHPUnit\Framework\TestCase
{
    /**
     * @var \Magento\Cms\Block\Block
     */
    protected $block;

    /**
     * @var \Magento\Framework\TestFramework\Unit\Helper\ObjectManager
     */
    protected $objectManager;

    /** @var \Magento\Backend\Block\Template\Context */
    protected $context;

    protected $storeManagerMock, $bannerRepositoryMock, $sliderRepositoryMock, $blockSlider;

    protected function setUp(): void
    {
        $this->objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);

        $store = $this->getMockBuilder('Magento\Store\Model\Store')
            ->disableOriginalConstructor()
            ->getMock();

        $this->storeManagerMock = $this->getMockBuilder('Magento\Store\Model\StoreManagerInterface')
            ->disableOriginalConstructor()
            ->getMock();

        $this->context = $this->objectManager->getObject('Magento\Backend\Block\Template\Context', [
            'storeManager' => $this->storeManagerMock
        ]);

        $this->bannerRepositoryMock = $this->getMockBuilder('LimeSoda\Banner\Model\BannerRepository')
            ->disableOriginalConstructor()
            ->getMock();

        $this->sliderRepositoryMock = $this->getMockBuilder('LimeSoda\Banner\Model\SliderRepository')
            ->disableOriginalConstructor()
            ->getMock();

        $bannerCollection = $this->getMockBuilder('LimeSoda\Banner\Model\ResourceModel\Banner\Collection')
            ->disableOriginalConstructor()
            ->getMock();

        $this->blockSlider = $this->objectManager->getObject('LimeSoda\Banner\Model\Slider');

        $this->block = $this->objectManager->getObject('LimeSoda\Banner\Block\AbstractSlider', [
            $this->context,
            $this->sliderRepositoryMock,
            $this->bannerRepositoryMock,
        ]);
    }

    protected function tearDown(): void
    {
        $this->block = null;
    }

    public function testBlockIsInstanceOfTemplate()
    {
        $this->assertInstanceOf('Magento\Framework\View\Element\Template', $this->block);
    }

    public function testGetBannerIdsReturnsArray()
    {
        $this->blockSlider->setData('slider_banner_ids', '7,2,6,4');
        $this->block->setSlider($this->blockSlider);
        $ids = $this->block->getBannerIds();
        $this->assertTrue(is_array($ids));
        $this->assertCount(4, $ids);

        $this->blockSlider->setData('slider_banner_ids', null);
        $this->block->setSlider($this->blockSlider);
        $ids = $this->block->getBannerIds();
        $this->assertTrue(is_array($ids));
        $this->assertTrue(empty($ids));
    }

    public function testGetIdentities()
    {
        $id = 1;
        $this->block->setSliderId($id);
        $this->assertEquals([\LimeSoda\Banner\Model\Slider::CACHE_TAG . '_' . $id], $this->block->getIdentities());
    }
}
