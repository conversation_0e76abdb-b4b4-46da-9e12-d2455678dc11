<?php
namespace LimeSoda\Banner\Test\Unit\Controller\Banner;

class IndexTest extends \PHPUnit\Framework\TestCase
{
    /**
     * @var \Magento\Framework\TestFramework\Unit\Helper\ObjectManager
     */
    protected $objectManager;

    protected function setUp(): void
    {
        $this->objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);
    }

    protected function tearDown(): void
    {
        $this->objectManager  = null;
    }

    public function testExecute()
    {
        $page = $this->getMockBuilder('Magento\Framework\View\Result\Page')
            ->disableOriginalConstructor()
            ->setMethods(['setActiveMenu', 'getConfig'])
            ->getMock();

        $pageConfig = $this->getMockBuilder('Magento\Framework\View\Result\Config')
            ->disableOriginalConstructor()
            ->setMethods(['getTitle', 'prepend'])
            ->getMock();

        $pageConfig->method('getTitle')
            ->will($this->returnSelf());

        $pageConfig->method('prepend')
        ->will($this->returnSelf());

        $page->method('setActiveMenu')
            ->will($this->returnSelf());

        $page->method('getConfig')
            ->willReturn($pageConfig);

        $resultFactory = $this->getMockBuilder('Magento\Framework\View\Result\PageFactory')
            ->disableOriginalConstructor()
            ->getMock();

        $resultFactory->expects($this->once())->method('create')->willReturn($page);

        $model = $this->objectManager->getObject('LimeSoda\Banner\Controller\Adminhtml\Banner\Index',
            ['resultPageFactory' => $resultFactory]
        );

        $this->assertSame($page, $model->execute());
    }
}
