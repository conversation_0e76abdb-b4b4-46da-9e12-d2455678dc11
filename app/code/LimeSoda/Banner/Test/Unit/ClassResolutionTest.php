<?php
namespace LimeSoda\Banner\Test\Unit;

class ClassResolutionTest extends \PHPUnit\Framework\TestCase
{
    /**
     * @var \Magento\Framework\TestFramework\Unit\Helper\ObjectManager
     */
    protected $objectManager;

    protected function setUp(): void
    {
        $this->objectManager = new \Magento\Framework\TestFramework\Unit\Helper\ObjectManager($this);
    }

    protected function tearDown(): void
    {
        $this->objectManager  = null;
    }

    public function testClassNameResolution()
    {
        $classes = [
            'LimeSoda\Banner\Controller\Adminhtml\Banner\Create',
            'LimeSoda\Banner\Controller\Adminhtml\Banner\Edit',
            'LimeSoda\Banner\Controller\Adminhtml\Banner\Image',
            'LimeSoda\Banner\Controller\Adminhtml\Banner\Index',
            'LimeSoda\Banner\Controller\Adminhtml\Banner\Save',
            'LimeSoda\Banner\Controller\Adminhtml\Banner\Delete',

            'LimeSoda\Banner\Controller\Adminhtml\Slider\Create',
            'LimeSoda\Banner\Controller\Adminhtml\Slider\Edit',
            'LimeSoda\Banner\Controller\Adminhtml\Slider\Save',
            'LimeSoda\Banner\Controller\Adminhtml\Slider\Index',
            'LimeSoda\Banner\Controller\Adminhtml\Slider\Delete',

            'LimeSoda\Banner\Model\Banner\ModuleConfig',
            'LimeSoda\Banner\Model\Banner\DataProvider',

            'LimeSoda\Banner\Model\Banner',
            'LimeSoda\Banner\Model\BannerRepository',

            'LimeSoda\Banner\Model\Slider',
            'LimeSoda\Banner\Model\SliderRepository',

            'LimeSoda\Banner\Model\Slider\ModuleConfig',
            'LimeSoda\Banner\Model\Slider\DataProvider',

            'LimeSoda\Banner\Block\Homepage',
            'LimeSoda\Banner\Block\AbstractSlider',
            'LimeSoda\Banner\Block\Catalog\Category\Slider',
        ];

        foreach ($classes as $class) {
            $object = $this->objectManager->getObject($class);
            $this->assertTrue(is_object($object));
        }
    }
}
