# Anonymization

## Usage Instructions

Run database anonymization:
```
bin/magento project:db:anonymize [environment]
```

Run database analyzation:

- Search for column values containing unanonymized email addresses

- Optional: Search for columns names containing strings like mail|street|address|city|name|country

```
Usage:

bin/magento project:db:analyze [options] [--] [<ignore-domains>]

Arguments:
  ignore-domains                    Comma separated list of ignored email domains.

Options:
  -c, --check-sensitive-columns     Search for columns names containing strings like mail|street|address|city|name|country (true or false). [default: "false"]

Example:

bin/magento project:db:analyze --check-sensitive-columns true limesoda.com,anotherdomain.com

```

## References
Find further documentation here:
- https://limedoc.limesoda.com/display/LSMAGENTO2/Umgebungskonfiguration+und+Anonymisierung
- https://gitlab.limesoda.com/limesoda/project-boilerplates/magento-2/-/blob/2.4.x/config/db-script/README.md
