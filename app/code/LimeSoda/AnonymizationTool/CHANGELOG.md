# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.1.0] - 2023-10-13

### Added
- Add analyze command to search for unanonymized customer data

## [2.0.4] - 2023-04-11

- Fix error "Return value of "LimeSoda\AnonymizationTool\Console\Command\Anonymization\Interceptor::execute()" must be of the type int, "null" returned".

## [2.0.3] - 2023-02-27

### Added / Improved
- Fix coding standard error

## [2.0.1] - 2022-11-18
### Changed
- Use directory config/db-script/src instead of config/db-script/anonymization

## [2.0.0] - 2022-11-18
### Changed
- Adapt configuration directory requirement from /devs/ to /envs/
- Support moved anonymization script under directory anonymization/

## [1.0.1] - 2019-12-04
### Removed
- magento/bin command project:db:configure

## [1.0.0] - 2019-12-04
### Added
- magento/bin command project:db:anonymize
- magento/bin command project:db:configure
