<?php
declare(strict_types=1);

namespace LimeSoda\CategoryNav\Block\Catalog\Category;

use Magento\Catalog\Helper\Category as CategoryHelper;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

/**
 * Class Nav
 * @package LimeSoda\CategoryNav\Block\Catalog\Category
 */
class Nav extends Template
{

    /**
     * @var int
     */
    protected $level = 1;

    /**
     * @var CategoryHelper
     */
    protected $categoryHelper;

    /**
     * @var Registry
     */
    protected $registry;


    /**
     * @param Context $context
     * @param CategoryHelper $categoryHelper
     * @param Registry $registry
     * @param array $data
     */
    public function __construct(
        Context $context,
        CategoryHelper $categoryHelper,
        Registry $registry,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->registry = $registry;
        $this->categoryHelper = $categoryHelper;
    }

    /**
     * @return mixed
     * @throws LocalizedException
     */
    public function getCategories()
    {
        $dataKey = 'categories';
        if (!$this->hasData($dataKey)) {
            $collection = $this->categoryHelper->getStoreCategories(true, true);
            $items = $collection->getItemsByColumnValue('level', $this->getLevel() + 1);
            $this->setData($dataKey, $items);
        }

        return $this->getData($dataKey);
    }

    /**
     * @return int
     */
    public function getLevel(): int
    {
        return $this->level;
    }

    /**
     * @param int $level
     */
    public function setLevel($level): void
    {
        $this->level = $level;
    }

    /**
     * @param $category
     * @return bool
     */
    public function categoryHasChildren($category): bool
    {
        return $category->hasChildren();
    }

    /**
     * @param $category
     * @return string
     * @throws LocalizedException
     */
    public function getChildCategoryHtml($category): string
    {
        $html = '';
        if ($category->hasChildren()) {
            $block = $this->getLayout()->getBlock('limesoda.category.nav');
            $block->setData('child_categories', $category->getChildrenCategories());
            $block->setLevel((int) $category->getData('level'));
            $html = $block->toHtml();
        }
        return $html;
    }

    /**
     * @param $category
     * @return bool
     */
    public function isCurrentCategory($category): bool
    {
        $currentCategory = $this->registry->registry('current_category');
        if ($currentCategory) {
            return ($currentCategory->getId() === $category->getId());
        }
        return false;
    }

    /**
     * @param $category
     * @return bool
     */
    public function hasCurrentCategory($category): bool
    {
        $currentCategory = $this->registry->registry('current_category');
        if ($currentCategory) {
            return in_array($category->getId(), $currentCategory->getPathIds(), true);
        }
        return false;
    }
}
