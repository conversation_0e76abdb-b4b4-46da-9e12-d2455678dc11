<?php
namespace LimeSoda\CategoryNav\Test\Unit;

use Magento\Framework\TestFramework\Unit\Helper\ObjectManager;
use PHPUnit\Framework\TestCase;

class ClassResolutionTest extends TestCase
{
    /**
     * @var ObjectManager
     */
    protected $objectManager;

    protected function setUp(): void
    {
        $this->objectManager = new ObjectManager($this);
    }

    protected function tearDown(): void
    {
        $this->objectManager  = null;
    }

    public function testClassNameResolution()
    {
        $classes = [
            'LimeSoda\CategoryNav\Block\Catalog\Category\Nav',
        ];

        foreach ($classes as $class) {
            $object = $this->objectManager->getObject($class);
            $this->assertTrue(is_object($object));
        }
    }
}
