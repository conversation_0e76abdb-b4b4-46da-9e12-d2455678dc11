<?php

namespace LimeSoda\CategoryNav\Test\Unit\Block\Catalog\Category;

use Magento\Backend\Block\Template\Context;
use Magento\Catalog\Model\ResourceModel\Category\Collection as categoryCollection;
use Magento\Framework\TestFramework\Unit\Helper\ObjectManager;
use Magento\Store\Model\StoreManagerInterface;
use PHPUnit\Framework\TestCase;
use Magento\Framework\DataObject;
use Magento\Framework\Registry;
use Magento\Catalog\Helper\Category;
use Magento\Framework\ObjectManagerInterface;
use Magento\Framework\View\TemplateEngine\Php as PhpTemplateEngine;
use Magento\Framework\View\LayoutInterface;
use LimeSoda\CategoryNav\Block\Catalog\Category\Nav as LsNavigation;

class NavTest extends TestCase
{
    protected $objectManager;

    protected $registry;

    protected $categoryHelper;

    protected $context;

    protected function setUp(): void
    {
        $this->objectManager = new ObjectManager($this);
        $this->registry = $this->objectManager->getObject(Registry::class);
        $this->categoryHelper = $this->objectManager->getObject(Category::class);
        $this->context = $this->objectManager->getObject(Context::class);
    }

    protected function tearDown(): void
    {
        $this->objectManager = null;
    }

    /**
     * @dataProvider getCategoryCollectionCases
     *
     * @todo currently deactivated as the bug in the test couldnt be found and seems not very helpfull limited to one level
     * @todo it tests only one level menu. The solution has to be found how to test toHtml function.
     * Unittest implements already multiple levels generation.
     * (see \LimeSoda\CategoryNav\Block\Catalog\Category\getChildCategoryHtml)
     *
     * @param $data
     * @param $expected
     * @throws \Exception

    public function testTemplateRenders($data, $expected)
    {
        $categories = $data['categories'];
        $currentCategory = $data['current_category'];

        if ($currentCategory !== null) {
            $currentCategoryID = $currentCategory;
            $currentCategory = new DataObject();
            $currentCategory->setId($currentCategoryID);
        }

        $this->registry->unregister('current_category');
        $this->registry->register('current_category', $currentCategory);

        $block = new LsNavigation($this->context, $this->categoryHelper, $this->registry);

        $layoutMock = $this->createMock(LayoutInterface::class);

        $block->setLayout($layoutMock);
        $block->setData('categories', $categories);
        $block->setData('child_categories', $categories);

        $block->setTemplate(dirname(__DIR__, 5) . '/view/frontend/templates/category/nav.phtml');

        $layoutMock->expects($this->any())
            ->method('getBlock')
            ->with('limesoda.category.nav')
            ->willReturn($block);

        $objectManagerMock = $this->createMock(ObjectManagerInterface::class);
        $phpEngine = new PhpTemplateEngine($objectManagerMock);

        $blockOutput = $phpEngine->render($block, $block->getTemplate());

        $this->assertRegExp($expected, $blockOutput);
    }
    */

    /**
     * @return array
     */
    public function getCategoryCollectionCases(): array
    {
        return [
            'check_first_level_navigation' => [
                'data' => [
                    'current_category' => 2,
                    'categories' => $this->getCategories($recursionLimit = 1),
                ],
                'expectation' => '/sidenav-level-1/'
            ],
            'check_current_category_class' => [
                'data' => [
                    'current_category' => 2,
                    'categories' => $this->getCategories($recursionLimit = 1),
                ],
                'expectation' => '/sidenav-link current/'
            ]
        ];

    }

    /**
     * @param int $recursionLimit
     * @return array
     */
    protected function getCategories(int $recursionLimit): array
    {
        $increment = 0;
        return $this->generateCategories($increment, --$recursionLimit, 0);
    }

    /**
     * @param $increment
     * @param $recursionLimit
     * @param int $level
     * @return array
     */
    protected function generateCategories(&$increment, $recursionLimit, $level): array
    {
        $categoriesCollection = [];

        for ($i = 0; $i < 3; $i++) {
            $dataObject = new DataObject();

            $id = $increment;

            $children = $recursionLimit === $level ? [] : $this->generateCategories($increment, $recursionLimit, $level + 1);

            $data = [
                'level' => $level,
                'id' => $id,
                'url' => 'test_url' . $id,
                'name' => 'name' . $id,
                'children_categories' => $children
            ];

            if (count($children) > 0) {
                $data['children'] = $children;
            }

            $dataObject->setData($data);

            $increment++;

            $categoriesCollection[] = $dataObject;
        }

        return $categoriesCollection;
    }

}