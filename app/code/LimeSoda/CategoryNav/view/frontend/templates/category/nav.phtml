<?php
$categories = $block->getCategories();
if ($block->getData('child_categories')) :
    $categories = $block->getData('child_categories');
else : ?>
    <span class="sidebar-nav-title">
        <?= $block->escapeHtml(__('Categories')) ?>
    </span>
<?php endif; ?>

<ul class="sidenav-level-<?= (int)$block->getLevel() ?>">
    <?php foreach ($categories as $category) :
        $block->setLevel((int) $category->getData('level') - 1);
        $currentClass = $block->isCurrentCategory($category) ? ' current' : ($block->hasCurrentCategory($category) ? ' active' : '');
        ?>
        <li class="sidenav-link<?= $currentClass ?>">
            <a href="<?= $block->escapeUrl($category->getUrl()) ?>">
                <?= $block->escapeHtml($category->getData('name')) ?>
            </a>
            <?php if ($block->categoryHasChildren($category)) {
                echo $this->getChildCategoryHtml($category);
            } ?>
        </li>
    <?php endforeach; ?>
</ul>
