3.0.1 (18.05.2021)

Fixed:

* Unittest ClassResolutionTest failed. Fixed.

3.0.0 (17.05.2021)

Added:

* Added class "active" to parent nodes with currently selected child category

Improved:

* Replaced plugin to remove default category filter with default config setting `layered_navigation > display_category`
* Reformated code
* Removed duplicate code

2.1.6 (18.01.2021)
-----

Fixed:

* Support Phpunit 9.0

2.1.5 (11.01.2021)
-----

Added / Improved:

* Magento 2.4.* support

2.1.0 (03.07.2018)
-----

Fixed:

* Removed missing layout reference that belongs to ls base theme
* Css class changes uppercase "Sidebar-mobile-filter" to "sidebar-mobile-filter"

2.0.0 (28.02.2018)
-----

Added / Improved:

* Compatibility with PHPUnit 6 (needed for Magento 2.2)
