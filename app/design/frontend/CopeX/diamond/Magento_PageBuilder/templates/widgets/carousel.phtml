<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */

$gliderUrl = $block->getViewFileUrl('Magento_PageBuilder::js/glider.min.js');
?>
<script>
    'use strict';

    (() => {
        const initCarousels = (elements) => {
            if (!Glider) {
                return;
            }

            const initProductCarousel = (carousel) => {
                const gliderElement = carousel.querySelector('[data-role="glider-content"]');

                if (!gliderElement) {
                    return;
                }

                const gliderDots = carousel.querySelector('.glider-dots');
                const gliderPrev = carousel.querySelector('.glider-prev');
                const gliderNext = carousel.querySelector('.glider-next');

                // Check if carousel is within a PageBuilder column layout
                const isInColumn = carousel.closest('[data-content-type="column"]') !== null;

                // Define responsive settings for full-width carousels
                const fullWidthResponsive = [
                    {
                        breakpoint: 640,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 2,
                        }
                    },
                    {
                        breakpoint: 1024,
                        settings: {
                            slidesToShow: 3,
                            slidesToScroll: 3,
                        }
                    },
                    {
                        breakpoint: 1280,
                        settings: {
                            slidesToShow: 4,
                            slidesToScroll: 4,
                        }
                    },
                ];

                // Define responsive settings for column layout carousels (max 2 slides)
                const columnResponsive = [
                    {
                        breakpoint: 640,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 2,
                        }
                    },
                    {
                        breakpoint: 1024,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 2,
                        }
                    },
                    {
                        breakpoint: 1280,
                        settings: {
                            slidesToShow: 2,
                            slidesToScroll: 2,
                        }
                    },
                ];

                const glider = new Glider(gliderElement, {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    scrollLock: true,
                    draggable: true,
                    dragVelocity: 2.5,
                    dots: gliderDots,
                    arrows: {
                        prev: gliderPrev,
                        next: gliderNext,
                    },
                    responsive: isInColumn ? columnResponsive : fullWidthResponsive,
                });

                carousel.classList.remove('overflow-x-scroll');
                gliderElement.classList.remove('overflow-x-scroll');
                gliderElement.classList.remove('overflow-y-hidden');
                gliderPrev.classList.remove('hidden');
                gliderNext.classList.remove('hidden');

                if (carousel.dataset.autoplay !== 'false') {
                    gliderAutoplay(
                        glider,
                        carousel.dataset.autoplaySpeed,
                        carousel.dataset.infiniteLoop
                    );
                }
                window.addEventListener('page-builder-tab-activate', event => {
                    const tab = event.detail.tab;
                    if (tab && tab.contains(gliderElement)) {
                        event.detail.nextTick.then(() => {
                            requestAnimationFrame(() => glider.refresh(true))
                        });
                    }
                });
            };

            const initSliderCarousel = (slider) => {
                slider.innerHTML = `<div data-role="glider-content">${slider.innerHTML}</div>`;
                slider.classList.add('glider-contain');

                slider.insertAdjacentHTML(
                    'beforeend',
                    '<?= $escaper->escapeJs($block->getBlockHtml('pagebuilder.carousel.nav')) ?>'
                );

                const gliderElement = slider.querySelector('[data-role="glider-content"]');
                const gliderDots = slider.querySelector('.glider-dots');
                const gliderPrev = slider.querySelector('.glider-prev');
                const gliderNext = slider.querySelector('.glider-next');

                const glider = new Glider(gliderElement, {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    scrollLock: true,
                    scrollLockDelay: 250,
                    draggable: true,
                    dragVelocity: 2.5,
                    dots: gliderDots,
                    arrows: {
                        prev: gliderPrev,
                        next: gliderNext,
                    },
                });

                slider.classList.add('glider-initialized');
                if (slider.dataset.showArrows === 'true') {
                    gliderPrev.classList.remove('hidden');
                    gliderNext.classList.remove('hidden');
                }

                if (slider.dataset.autoplay !== 'false') {
                    gliderAutoplay(
                        glider,
                        slider.dataset.autoplaySpeed,
                        slider.dataset.infiniteLoop
                    );
                }
            };

            const gliderAutoplay = (glider, milliseconds, loop) => {
                const pagesCount = glider.track.childElementCount;
                let slideTimeout = null;
                let nextIndex = 1;
                let paused = false;

                const slide = () => {
                    slideTimeout = setTimeout(
                        () => {
                            if (loop && nextIndex >= pagesCount) {
                                nextIndex = 0;
                            }
                            glider.scrollItem(nextIndex);
                        },
                        parseInt(milliseconds)
                    );
                };

                glider.ele.addEventListener('glider-animated', () => {
                    nextIndex = glider.slide + glider.opt.slidesToScroll;
                    window.clearInterval(slideTimeout);
                    if (!paused && (loop || nextIndex < pagesCount)) {
                        slide();
                    }
                });

                const pause = () => {
                    if (!paused) {
                        clearInterval(slideTimeout);
                        paused = true;
                    }
                };

                const unpause = () => {
                    if (paused) {
                        slide();
                        paused = false;
                    }
                };

                glider.ele.parentElement.addEventListener('mouseover', pause, {passive: true});
                glider.ele.parentElement.addEventListener('touchstart', pause, {passive: true});
                glider.ele.parentElement.addEventListener('mouseout', unpause, {passive: true});
                glider.ele.parentElement.addEventListener('touchend', unpause, {passive: true});

                slide();
            };

            elements.forEach(element => {
                if (element.dataset.contentType === 'products') {
                    initProductCarousel(element);
                }
                if (element.dataset.contentType === 'slider') {
                    initSliderCarousel(element);
                }
            });
        };

        window.addEventListener('DOMContentLoaded', () => {
            const carouselElements = document.querySelectorAll(
                `[data-content-type="products"][data-appearance="carousel"],
                [data-content-type="slider"]`
            );

            if (carouselElements.length > 0) {
                const script = document.createElement('script');
                script.type = 'text/javascript';

                script.addEventListener('load', () => {
                    initCarousels(carouselElements);
                });

                script.src = '<?= $escaper->escapeJs($escaper->escapeUrl($gliderUrl)) ?>';
                document.head.appendChild(script);
            }
        });
    })();
</script>
