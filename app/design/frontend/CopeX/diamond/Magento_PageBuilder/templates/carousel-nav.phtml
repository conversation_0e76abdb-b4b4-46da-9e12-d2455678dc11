<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsSolid $heroicons */
$heroicons = $viewModels->require(HeroiconsSolid::class);
?>

<div class="carousel-nav flex items-center justify-center flex-1 p-4">
    <button
        aria-label="<?= $escaper->escapeHtmlAttr(__('Previous')) ?>"
        title="<?= $escaper->escapeHtmlAttr(__('Previous')) ?>"
        class="glider-prev w-12 h-16 text-diamond-blue bg-white bg-opacity-90 hover:bg-opacity-100 rounded-r-full outline-none focus:outline-none absolute top-1/2 -translate-y-1/2 left-0 z-40 flex items-center justify-center transition-all duration-200 hover:shadow-lg pl-1"
    >
        <?= $heroicons->chevronLeftHtml('', 20, 20, ['aria-hidden' => true]); ?>
    </button>
    <div role="tablist" class="glider-dots select-none flex flex-wrap mx-1 justify-center p-0 focus:outline-none"></div>
    <button
        aria-label="<?= $escaper->escapeHtmlAttr(__('Next')) ?>"
        title="<?= $escaper->escapeHtmlAttr(__('Next')) ?>"
        class="glider-next w-12 h-16 text-diamond-blue bg-white bg-opacity-90 hover:bg-opacity-100 rounded-l-full outline-none focus:outline-none absolute top-1/2 -translate-y-1/2 right-0 z-40 flex items-center justify-center transition-all duration-200 hover:shadow-lg pr-1"
    >
        <?= $heroicons->chevronRightHtml('', 20, 20, ['aria-hidden' => true]); ?>
    </button>
</div>
