[data-content-type="accordion-item"] {
    @apply border-t;
}

[data-content-type="accordion"] [data-collapsible="true"]:after{
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="%23404542" class="w-6 h-6"> <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" /> </svg>');
}

[data-content-type="accordion"] [data-collapsible="true"]:hover:after{
    transform: translateX(-0.25rem);
}

[data-content-type="accordion"] [aria-expanded="true"] [data-collapsible="true"]:after { transform: rotate(-180deg); }