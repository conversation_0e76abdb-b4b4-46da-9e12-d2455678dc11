#category-view-container {
    @apply mx-auto flex px-0 py-4 flex-col items-center;
}

.category-description {
    @apply max-w-4xl;
}

.toolbar-products {
    .modes-mode {
        @apply w-6 h-6;

        span {
            @apply sr-only;
        }

        &.mode-grid {
            background-image: url('data:image/svg+xml;utf8,<svg  stroke="currentColor" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.5 3.5H10.5V10.5H3.5V3.5Z"/> <path d="M3.5 13.5H10.5V20.5H3.5V13.5Z"/> <path d="M13.5 3.5H20.5V10.5H13.5V3.5Z"/> <path d="M13.5 13.5H20.5V20.5H13.5V13.5Z"/> </svg>');
        }

        &.mode-list {
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" /></svg>');
        }
    }
}
