const { spacing } = require('tailwindcss/defaultTheme');
const colors = require('tailwindcss/colors');
const hyvaModules = require('@hyva-themes/hyva-modules');

const myColors = {
    primary: {
        lighter: '#6192b5',
        "DEFAULT": '#00467e',
        darker: '#023157',
    },
    secondary: {
        lighter: '#d63939',
        "DEFAULT": '#b62525',
        darker: '#8b1c1c',
    },
    tertiary: {
        lighter: '#ffd032',
        "DEFAULT": '#ffe0a1',
        darker: '#e6c029',
    },
    quaternary: {
        lighter: '#b2d4bb',
        "DEFAULT": '#175d3b',
        darker: '#0f4129',
    },
    quinary: {
        lighter: '#c2bcd7',
        "DEFAULT": '#6a3686',
        darker: '#4a2660',
    },
    gray: {
        lightest: '#ecedee',
        lighter: '#bdc1c4',
        "DEFAULT": '#424241',
        darker: '#2a2a29',
    },
    stars: {
        empty: '#cbd5e0',
        "DEFAULT": '#e8b723'
    },
    availability: {
        available: '#175d3b'
    },
    blue: {
        50: '#f0f7ff',
        100: '#e0efff',
        200: '#b9dfff',
        300: '#7cc8ff',
        400: '#36b0ff',
        500: '#0ea5e9',
        600: '#00467e',
        700: '#023157',
        800: '#032a4a',
        900: '#09243e',
    },
    // Diamond Aircraft specific colors matching the original CSS
    diamond: {
        blue: '#00467e',
        'blue-dark': '#023157',
        'blue-light': '#6192b5',
        red: '#b62525',
        'yellow-dark': '#ffd032',
        'yellow-light': '#ffe0a1',
        'green-dark': '#175d3b',
        'green-light': '#b2d4bb',
        'purple-dark': '#6a3686',
        'purple-light': '#c2bcd7',
        'gray-lightest': '#ecedee',
        'gray-light': '#bdc1c4',
        'gray': '#424241',
    }
};

module.exports = hyvaModules.mergeTailwindConfig({
    theme: {
        extend: {
            screens: {
                'sm': '640px',
                // => @media (min-width: 640px) { ... }
                'md': '768px',
                // => @media (min-width: 768px) { ... }
                'lg': '1024px',
                // => @media (min-width: 1024px) { ... }
                'xl': '1280px',
                // => @media (min-width: 1280px) { ... }
                '2xl': '1770px',
                // => @media (min-width: 1770px) { ... }
            },
            colors: {
                ...myColors,
                background: {
                    lighter: myColors.tertiary.lighter,
                    "DEFAULT": myColors.tertiary.DEFAULT,
                    darker: myColors.tertiary.darker,
                }
            },
            textColor: {
                orange: colors.orange,
                primary: {
                    lighter: myColors.gray.lighter,
                    "DEFAULT": myColors.gray.DEFAULT,
                    darker: myColors.gray.darker,
                },
                secondary: {
                    lighter: myColors.primary.lighter,
                    "DEFAULT": myColors.primary.DEFAULT,
                    darker: myColors.primary.darker,
                },
                mysecondary: {
                    lighter: myColors.secondary.lighter,
                    "DEFAULT": myColors.secondary.DEFAULT,
                    darker: myColors.secondary.darker,
                },
            },
            backgroundColor: {
                shades: myColors.gray,
                container: {
                    lighter: '#ffffff',
                    "DEFAULT": '#fafafa',
                    darker: '#f5f5f5',
                }
            },
            borderColor: {
                primary: {
                    lighter: myColors.gray.lighter,
                    "DEFAULT": myColors.gray,
                    darker: myColors.gray.darker,
                },
                secondary: {
                    lighter: myColors.primary.lighter,
                    "DEFAULT": myColors.primary,
                    darker: myColors.primary.darker,
                },
                container: {
                    lighter: '#f5f5f5',
                    "DEFAULT": '#e7e7e7',
                    darker: '#b6b6b6',
                }
            },
            minWidth: {
                8: spacing["8"],
                20: spacing["20"],
                40: spacing["40"],
                48: spacing["48"],
                60: spacing["60"],
            },
            minHeight: {
                14: spacing["14"],
                a11y: '44px',
                20: spacing["20"],
                40: spacing["40"],
                48: spacing["48"],
                'screen-25': '25vh',
                'screen-50': '50vh',
                'screen-75': '75vh',
            },
            maxHeight: {
                '0': '0',
                'screen-25': '25vh',
                'screen-50': '50vh',
                'screen-75': '75vh',
            },
            container: {
                center: true,
                padding: '1.5rem'
            },
            animation: {
                'spin-reverse': 'spin-reverse 1s linear infinite'
            },
            keyframes: {
                'spin-reverse': {
                    from: {
                        transform: 'rotate(360deg)'
                    },
                }
            }
        },
    },
    plugins: [require('@tailwindcss/forms'), require('@tailwindcss/typography')],
    content: [
        // this theme's phtml files
        '../../**/*.phtml',
        '../../*/layout/*.xml',
        '../../*/page_layout/override/base/*.xml',
        // Green theme in  Vendor (if this is a child-theme)
        '../../../../../../../vendor/copex/theme-hyva-green/**/*.phtml',
        '../../../../../../../vendor/copex/theme-hyva-green/*/layout/*.xml',
        // parent theme in Vendor (if this is a child-theme)
        '../../../../../../../vendor/hyva-themes/magento2-default-theme/**/*.phtml',
        '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/layout/*.xml',
        '../../../../../../../vendor/hyva-themes/magento2-default-theme/*/page_layout/override/base/*.xml',
        // app/code phtml files (if need tailwind classes from app/code modules)
        '../../../../../../../app/code/**/*.phtml',
    ],
    safelist: ['type','transition-all','duration-1000','pointer-events-none',
        'gap-x-2', 'gap-x-4', 'gap-x-6', 'gap-x-8','transform','h-16','w-16'
    ]
})
