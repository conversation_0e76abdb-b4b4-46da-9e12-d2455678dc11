.account-nav ul li a, .account-nav ul li strong {
    @apply flex justify-between text-secondary py-1
}

.account-nav ul li a:hover {
    @apply text-black
}

.account-nav ul li strong {
    @apply underline text-black font-normal
}

.actions-toolbar {
    @apply mt-6 border-t border-container-darker pt-4 flex justify-between flex-row-reverse items-center
}


@layer components {
    .actions-toolbar .primary button {
        @apply btn btn-primary;
    }
}

.actions-toolbar a.back {
    @apply text-secondary-darker underline
}

.customer-address-form {
    .page-main {
        .page-title {
            @apply text-center w-full mb-4
        }
        .message {
            @apply mt-6;
        }
        .message ~ .message {
            @apply mt-0;
        }
        @apply bg-white rounded max-w-screen-lg mx-auto pt-4
    }
}
