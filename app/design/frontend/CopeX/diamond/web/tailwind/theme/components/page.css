.page-main {
    @apply text-primary;
}

.table-row-items > div.table-row-item {

    @apply bg-container-darker;

    &:nth-child(2n + 1) {
        @apply bg-container-lighter;
    }
}


.cms-page-view .column.main, [data-content-type='row'] {
    a:not([class$="button-primary"]) {
        @apply underline;
    }
}

[data-content-type="banner"] > a[data-element='link'] {
    @apply no-underline;
}


form .field.choice.gdpr-js-content, fieldset .field.choice.gdpr-js-content {
    align-items: start;
}