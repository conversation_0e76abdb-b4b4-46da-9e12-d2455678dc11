<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="80" viewBox="0 0 300 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Main Diamond Aircraft Logo for Header -->
  <defs>
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00467e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6192b5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="yellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffd032;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffb800;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main Diamond Shape -->
  <path d="M40 15 L65 40 L40 65 L15 40 Z" fill="url(#mainGradient)" stroke="#00467e" stroke-width="2"/>
  
  <!-- Inner Diamond Detail -->
  <path d="M40 25 L55 40 L40 55 L25 40 Z" fill="none" stroke="#ffd032" stroke-width="1.5" opacity="0.7"/>
  
  <!-- Aircraft Wing Design -->
  <path d="M30 35 L55 35 L62 40 L55 45 L30 45 L23 40 Z" fill="url(#yellowGradient)" opacity="0.9"/>
  <path d="M35 38 L50 38 L52 40 L50 42 L35 42 L33 40 Z" fill="#ffffff" opacity="0.8"/>
  
  <!-- Text: DIAMOND -->
  <text x="85" y="35" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#00467e" letter-spacing="1px">DIAMOND</text>
  
  <!-- Text: PILOT SHOP -->
  <text x="85" y="55" font-family="Arial, sans-serif" font-size="14" font-weight="normal" fill="#6192b5" letter-spacing="2px">PILOT SHOP</text>
  
  <!-- Decorative Aviation Elements -->
  <g transform="translate(250, 20)">
    <!-- Propeller -->
    <circle cx="15" cy="15" r="12" fill="none" stroke="#6192b5" stroke-width="1" opacity="0.4"/>
    <line x1="15" y1="5" x2="15" y2="25" stroke="#00467e" stroke-width="2" opacity="0.6"/>
    <line x1="5" y1="15" x2="25" y2="15" stroke="#00467e" stroke-width="2" opacity="0.6"/>
    <circle cx="15" cy="15" r="3" fill="#ffd032"/>
  </g>
  
  <!-- Flight Path Lines -->
  <path d="M270 50 Q275 45 280 50 Q285 55 290 50" fill="none" stroke="#6192b5" stroke-width="1.5" opacity="0.5"/>
  <path d="M272 55 Q277 50 282 55 Q287 60 292 55" fill="none" stroke="#6192b5" stroke-width="1" opacity="0.3"/>
</svg>
