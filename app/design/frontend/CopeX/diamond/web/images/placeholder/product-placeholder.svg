<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Product Placeholder Image -->
  <defs>
    <linearGradient id="placeholderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="400" fill="url(#placeholderGradient)"/>
  
  <!-- Border -->
  <rect x="1" y="1" width="398" height="398" fill="none" stroke="#cbd5e1" stroke-width="2" stroke-dasharray="10,5"/>
  
  <!-- Diamond Aircraft Logo -->
  <g transform="translate(150, 150)">
    <path d="M50 20 L80 50 L50 80 L20 50 Z" fill="#00467e" opacity="0.3"/>
    <path d="M50 30 L70 50 L50 70 L30 50 Z" fill="none" stroke="#6192b5" stroke-width="2" opacity="0.5"/>
    <path d="M40 45 L65 45 L70 50 L65 55 L40 55 L35 50 Z" fill="#ffd032" opacity="0.4"/>
  </g>
  
  <!-- Text -->
  <text x="200" y="280" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#64748b" text-anchor="middle">Diamond Aircraft</text>
  <text x="200" y="305" font-family="Arial, sans-serif" font-size="14" fill="#94a3b8" text-anchor="middle">Product Image</text>
  <text x="200" y="325" font-family="Arial, sans-serif" font-size="12" fill="#cbd5e1" text-anchor="middle">400 × 400</text>
  
  <!-- Decorative Elements -->
  <g opacity="0.2">
    <circle cx="80" cy="80" r="4" fill="#6192b5"/>
    <circle cx="320" cy="80" r="4" fill="#ffd032"/>
    <circle cx="80" cy="320" r="4" fill="#ffd032"/>
    <circle cx="320" cy="320" r="4" fill="#6192b5"/>
  </g>
</svg>
