<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Hero Image Placeholder -->
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00467e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#6192b5;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#87ceeb;stop-opacity:0.6" />
    </linearGradient>
    <radialGradient id="sunGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#ffd032;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#ffb800;stop-opacity:0.4" />
    </radialGradient>
  </defs>
  
  <!-- Sky Background -->
  <rect width="1920" height="1080" fill="url(#heroGradient)"/>
  
  <!-- Sun -->
  <circle cx="1600" cy="200" r="120" fill="url(#sunGradient)"/>
  
  <!-- Clouds -->
  <g opacity="0.4" fill="#ffffff">
    <ellipse cx="300" cy="200" rx="80" ry="40"/>
    <ellipse cx="350" cy="180" rx="60" ry="30"/>
    <ellipse cx="400" cy="200" rx="70" ry="35"/>
    
    <ellipse cx="800" cy="150" rx="100" ry="50"/>
    <ellipse cx="860" cy="130" rx="80" ry="40"/>
    <ellipse cx="920" cy="150" rx="90" ry="45"/>
    
    <ellipse cx="1200" cy="300" rx="120" ry="60"/>
    <ellipse cx="1280" cy="280" rx="100" ry="50"/>
    <ellipse cx="1360" cy="300" rx="110" ry="55"/>
  </g>
  
  <!-- Aircraft Silhouette -->
  <g transform="translate(960, 400)" opacity="0.8">
    <!-- Main Body -->
    <ellipse cx="0" cy="0" rx="200" ry="30" fill="#ffffff"/>
    <!-- Wings -->
    <ellipse cx="0" cy="0" rx="250" ry="15" fill="#ffd032" opacity="0.9"/>
    <!-- Cockpit -->
    <ellipse cx="80" cy="0" rx="40" ry="20" fill="#ffffff" opacity="0.7"/>
    <!-- Propeller -->
    <circle cx="200" cy="0" r="15" fill="#ffffff"/>
    <line x1="185" y1="0" x2="215" y2="0" stroke="#ffd032" stroke-width="4"/>
    <line x1="200" y1="-15" x2="200" y2="15" stroke="#ffd032" stroke-width="4"/>
  </g>
  
  <!-- Mountains/Horizon -->
  <g opacity="0.3">
    <path d="M0 800 L300 600 L600 700 L900 500 L1200 650 L1500 550 L1800 600 L1920 580 L1920 1080 L0 1080 Z" fill="#2d3748"/>
    <path d="M0 850 L200 750 L500 800 L800 650 L1100 750 L1400 700 L1700 720 L1920 700 L1920 1080 L0 1080 Z" fill="#4a5568"/>
  </g>
  
  <!-- Ground -->
  <rect x="0" y="900" width="1920" height="180" fill="#2d3748" opacity="0.2"/>
  
  <!-- Runway -->
  <rect x="0" y="950" width="1920" height="40" fill="#1a202c" opacity="0.5"/>
  <g opacity="0.7">
    <rect x="100" y="965" width="80" height="10" fill="#ffffff"/>
    <rect x="300" y="965" width="80" height="10" fill="#ffffff"/>
    <rect x="500" y="965" width="80" height="10" fill="#ffffff"/>
    <rect x="700" y="965" width="80" height="10" fill="#ffffff"/>
    <rect x="900" y="965" width="80" height="10" fill="#ffffff"/>
    <rect x="1100" y="965" width="80" height="10" fill="#ffffff"/>
    <rect x="1300" y="965" width="80" height="10" fill="#ffffff"/>
    <rect x="1500" y="965" width="80" height="10" fill="#ffffff"/>
    <rect x="1700" y="965" width="80" height="10" fill="#ffffff"/>
  </g>
  
  <!-- Diamond Aircraft Logo Overlay -->
  <g transform="translate(100, 100)" opacity="0.6">
    <path d="M50 20 L80 50 L50 80 L20 50 Z" fill="#ffffff"/>
    <path d="M50 30 L70 50 L50 70 L30 50 Z" fill="none" stroke="#ffd032" stroke-width="3"/>
    <text x="100" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">DIAMOND</text>
    <text x="100" y="65" font-family="Arial, sans-serif" font-size="16" fill="#ffd032">AIRCRAFT</text>
  </g>
  
  <!-- Placeholder Text -->
  <g transform="translate(960, 540)" opacity="0.8">
    <text x="0" y="0" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#ffffff" text-anchor="middle">#WeWearDiamondAircraft</text>
    <text x="0" y="50" font-family="Arial, sans-serif" font-size="24" fill="#ffffff" text-anchor="middle" opacity="0.9">Hero Image Placeholder</text>
    <text x="0" y="80" font-family="Arial, sans-serif" font-size="18" fill="#ffd032" text-anchor="middle">1920 × 1080</text>
  </g>
</svg>
