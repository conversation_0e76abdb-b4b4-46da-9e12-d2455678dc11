/**
 * Diamond Aircraft Homepage Styles
 * Additional CSS for elements that need custom styling beyond Tailwind
 */

/* Hero Slider Enhancements */
.hero-slider {
    min-height: 100vh;
    position: relative;
}

.hero-slider .slide-content {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* Product Cards Hover Effects */
.product-showcase .product-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-showcase .product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Category Cards Parallax Effect */
.categories-showcase .category-card {
    overflow: hidden;
    position: relative;
}

.categories-showcase .category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 70, 126, 0.8) 0%, rgba(97, 146, 181, 0.6) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.categories-showcase .category-card:hover::before {
    opacity: 1;
}

.categories-showcase .category-card .category-content {
    position: relative;
    z-index: 2;
}

/* Brand Story Section Animations */
.brand-story {
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
}

/* Newsletter Section */
.newsletter {
    background-image:
        radial-gradient(circle at 20% 80%, rgba(0, 70, 126, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 208, 50, 0.1) 0%, transparent 50%);
}

/* Footer Enhancements */
.footer {
    position: relative;
}

.footer-main {
    background-image:
        linear-gradient(135deg, rgba(249, 250, 251, 0.8) 0%, rgba(243, 244, 246, 0.9) 100%);
}

.footer-corporate {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-slider {
        min-height: 70vh;
    }

    .hero-slider h1 {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .product-showcase .product-card:hover {
        transform: none;
    }

    .categories-showcase .category-card:hover {
        transform: none;
    }
}

/* Loading States */
.product-image-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.focus-visible:focus {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
}

/* Button Enhancements */
.btn-primary {
    background: linear-gradient(135deg, #00467e 0%, #0066cc 100%);
    box-shadow: 0 4px 14px 0 rgba(0, 70, 126, 0.39);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #003a6b 0%, #0052a3 100%);
    box-shadow: 0 6px 20px 0 rgba(0, 70, 126, 0.5);
    transform: translateY(-2px);
}

.btn-secondary {
    background: linear-gradient(135deg, #ffd032 0%, #ffb800 100%);
    color: #1f2937;
    box-shadow: 0 4px 14px 0 rgba(255, 208, 50, 0.39);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #ffb800 0%, #ff9500 100%);
    box-shadow: 0 6px 20px 0 rgba(255, 208, 50, 0.5);
    transform: translateY(-2px);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #00467e;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #003a6b;
}

/* Print Styles */
@media print {
    .hero-slider,
    .newsletter,
    .footer-main,
    .footer-corporate {
        display: none;
    }

    .product-showcase,
    .categories-showcase,
    .brand-story {
        break-inside: avoid;
        page-break-inside: avoid;
    }
}
