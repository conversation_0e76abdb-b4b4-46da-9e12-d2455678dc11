# Diamond Aircraft Hyvä Theme Conversion

## Overview
This document outlines the conversion of the Diamond Aircraft Luma theme to Hyvä theme, focusing on recreating the visual design and layout from the Live Home Page using **only Tailwind CSS classes** in phtml template files.

## Approach
Instead of using custom CSS files, this conversion applies Tailwind CSS classes directly to the phtml template files, following Hyvä best practices for maintainability and performance.

## Changes Made

### 1. Color Scheme Update (`tailwind.config.js`)
Updated the color palette to match Diamond Aircraft branding:
- **Primary Blue**: `#00467e` (main brand color)
- **Primary Blue Darker**: `#023157` (darker variant)
- **Primary Blue Lighter**: `#6192b5` (lighter variant)
- **Secondary Red**: `#b62525` (accent color)
- **Tertiary Yellow**: `#ffd032` / `#ffe0a1` (highlight colors)
- **Gray Scale**: Updated to match original design

### 2. Template Files Created with Tailwind Classes

#### Header Template (`Magento_Theme/templates/html/header.phtml`)
- **Transparent Header**: Uses conditional classes based on homepage detection
- **Logo Styling**: Responsive sizing with color variations for transparent/normal states
- **Navigation Elements**: Proper spacing and hover effects using Tailwind utilities
- **Responsive Behavior**: Mobile-first approach with responsive breakpoints
- **Info Banner**: Blue notification bar on homepage only

#### Logo Template (`Magento_Theme/templates/html/header/logo.phtml`)
- **Responsive Logo**: Auto-sizing with proper aspect ratios
- **Color Variations**: White logo on transparent header, blue on normal header
- **Hover Effects**: Smooth opacity transitions

#### Desktop Navigation (`Magento_Theme/templates/html/header/menu/desktop.phtml`)
- **Horizontal Menu**: Centered navigation with proper spacing
- **Dropdown Menus**: Smooth animations with shadow and rounded corners
- **Hover Effects**: Border and color transitions
- **Active States**: Visual indicators for current page

#### Mobile Navigation (`Magento_Theme/templates/html/header/menu/mobile.phtml`)
- **Full-Screen Overlay**: Clean mobile menu experience
- **Slide Animations**: Smooth transitions between menu levels
- **Touch-Friendly**: Proper touch targets and spacing
- **Hierarchical Navigation**: Clear parent-child relationships

#### Product List Item (`Magento_Catalog/templates/product/list/item.phtml`)
- **Card Design**: Clean product cards with hover effects
- **Image Handling**: Responsive images with overlay actions
- **Price Display**: Prominent pricing with proper typography
- **Stock Status**: Visual indicators with color coding
- **Action Buttons**: Styled add-to-cart and wishlist buttons
- **Sale Badges**: Positioned sale indicators

### 3. Custom SVG Icons Created

#### Diamond Aircraft Specific Icons
Created custom SVG icons in `Hyva_Theme/web/svg/diamond/`:
- `aircraft.svg` - Diamond aircraft silhouette
- `pilot.svg` - Pilot with headset icon
- `propeller.svg` - Aircraft propeller icon
- `runway.svg` - Airport runway icon
- `headset.svg` - Aviation headset icon
- `compass.svg` - Navigation compass icon
- `language.svg` - Language/globe icon
- `support.svg` - Customer support icon
- `shipping.svg` - Shipping/delivery icon

#### Custom Icons ViewModel
- Created `Diamond\Theme\ViewModel\DiamondIcons` class
- Extends Hyvä's `SvgIcons` base class
- Configured to use `Hyva_Theme::svg/diamond` path prefix
- Usage: `$diamondIcons->aircraftHtml('w-6 h-6 text-primary')`

#### Icon Corrections Made
- Fixed `xMarkHtml()` → `xHtml()` (uses x.svg from Lucide)
- Fixed `barsThreeHtml()` → `menuHtml()` (uses menu.svg from Lucide)
- All other icons (search, shopping-cart, heart, scale, chevron-*) are available in Lucide set

### 4. Color Scheme Fixes Applied

#### Updated Diamond Aircraft Colors
- `bg-diamond-blue` / `text-diamond-blue` - Primary blue (#00467e)
- `bg-diamond-blue-dark` / `text-diamond-blue-dark` - Darker blue (#023157)
- `bg-secondary` / `text-secondary` - Diamond red (#b62525)
- `bg-diamond-yellow-dark` / `text-diamond-yellow-dark` - Yellow (#ffd032)
- `bg-diamond-yellow-light` / `text-diamond-yellow-light` - Light yellow (#ffe0a1)

#### Product Carousel Implementation
- **Horizontal Scrolling**: Products now display in horizontal carousels like the original
- **Slider Controls**: Left/right navigation buttons with proper styling
- **Responsive Breakpoints**: 1 item (mobile), 2 items (tablet), 3-5 items (desktop)
- **Smooth Transitions**: CSS transforms with Alpine.js for smooth sliding

### 5. Tailwind Classes Used

#### Layout Classes
- `max-w-screen-2xl mx-auto px-4` - Container widths
- `flex items-center justify-between` - Header layout
- `grid grid-cols-*` - Product grid layouts
- `absolute inset-0` - Overlay positioning
- `relative z-*` - Layering and stacking

#### Interactive Classes
- `hover:bg-*` / `hover:text-*` - Hover state changes
- `transition-colors duration-200` - Smooth color transitions
- `transform hover:scale-105` - Image hover effects
- `opacity-0 group-hover:opacity-100` - Reveal animations
- `rounded-lg shadow-sm hover:shadow-lg` - Card styling

#### Responsive Classes
- `hidden lg:flex` - Desktop-only elements
- `lg:hidden` - Mobile-only elements
- `md:flex-row` - Responsive flex direction
- `sm:w-auto` - Responsive widths

## Key Features Implemented

### Visual Design Elements
- ✅ Diamond Aircraft blue color scheme (#00467e) applied via Tailwind classes
- ✅ Transparent header overlay on homepage with conditional styling
- ✅ Product cards with hover effects and proper spacing
- ✅ Responsive design for all screen sizes using Tailwind breakpoints
- ✅ Proper typography hierarchy with Tailwind text utilities
- ✅ Sale badges and product status indicators with color coding

### Interactive Elements
- ✅ Smooth hover transitions using Tailwind transition utilities
- ✅ Mobile-friendly navigation with full-screen overlay
- ✅ Dropdown menus with smooth animations and proper positioning
- ✅ Product image hover effects with transform utilities
- ✅ Button hover states with color and shadow changes
- ✅ Focus states for accessibility

### Responsive Behavior
- ✅ Mobile header adjustments with responsive classes
- ✅ Mobile navigation menu with slide animations
- ✅ Flexible product grid using CSS Grid utilities
- ✅ Responsive typography scaling with text-* classes

## Build Instructions

### 1. Install Dependencies
```bash
cd app/design/frontend/CopeX/diamond/web/tailwind
npm install
```

### 2. Build CSS
```bash
# Development build
npm run build

# Production build
npm run build-prod

# Watch mode for development
npm run watch
```

### 3. Clear Magento Cache
```bash
bin/magento cache:clean
bin/magento cache:flush
```

### 4. Deploy Static Content
```bash
bin/magento setup:static-content:deploy -f
```

## Testing Checklist

### Homepage
- [ ] Hero slider displays correctly with overlay text
- [ ] Info banner appears at top with blue background
- [ ] Product lists show with proper grid layout
- [ ] Product hover effects work smoothly
- [ ] CTA buttons have proper styling and hover states

### Header
- [ ] Transparent header on homepage
- [ ] Logo displays correctly (blue on white, white on transparent)
- [ ] Navigation menu works on desktop and mobile
- [ ] Search, language switcher, login, cart icons function
- [ ] Mobile menu toggle works properly

### Navigation
- [ ] Main navigation links have hover effects
- [ ] Dropdown menus appear with smooth animations
- [ ] Mobile navigation is collapsible and functional
- [ ] Breadcrumbs display correctly on inner pages

### Responsive Design
- [ ] Layout works on mobile (320px+)
- [ ] Layout works on tablet (768px+)
- [ ] Layout works on desktop (1024px+)
- [ ] Layout works on large screens (1440px+)

### Colors and Typography
- [ ] Blue color scheme matches original design
- [ ] Typography hierarchy is consistent
- [ ] Link colors and hover states work
- [ ] Button styling matches design requirements

### Custom Icons
- [ ] Diamond Aircraft icons render correctly
- [ ] Custom icons have proper colors and sizing
- [ ] Icons work with Tailwind utility classes

## Using Custom Diamond Aircraft Icons

In any phtml template, you can use the custom Diamond Aircraft icons:

```php
<?php
use Diamond\Theme\ViewModel\DiamondIcons;

/** @var DiamondIcons $diamondIcons */
$diamondIcons = $viewModels->require(DiamondIcons::class);
?>

<!-- Aircraft icon with primary color -->
<?= $diamondIcons->aircraftHtml('w-6 h-6 text-primary') ?>

<!-- Pilot icon with hover effect -->
<?= $diamondIcons->pilotHtml('w-8 h-8 text-gray-600 hover:text-primary transition-colors duration-200') ?>

<!-- Propeller icon in navigation -->
<?= $diamondIcons->propellerHtml('w-5 h-5 mr-2') ?>
```

**Available Icons:**
- `aircraftHtml()` - Diamond aircraft silhouette
- `pilotHtml()` - Pilot with headset
- `propellerHtml()` - Aircraft propeller
- `runwayHtml()` - Airport runway
- `headsetHtml()` - Aviation headset
- `compassHtml()` - Navigation compass
- `languageHtml()` - Language/globe
- `supportHtml()` - Customer support
- `shippingHtml()` - Shipping/delivery

## File Structure
```
app/design/frontend/CopeX/diamond/
├── web/tailwind/
│   ├── tailwind.config.js (updated colors)
│   └── theme.css (removed custom CSS imports)
├── Magento_Theme/templates/html/
│   ├── header.phtml (Diamond Aircraft header with Tailwind classes)
│   ├── diamond-icons-example.phtml (custom icons showcase)
│   └── header/
│       ├── logo.phtml (responsive logo with color variations)
│       └── menu/
│           ├── desktop.phtml (desktop navigation with dropdowns)
│           └── mobile.phtml (mobile navigation overlay)
├── Magento_Catalog/templates/product/
│   ├── list.phtml (horizontal product carousel)
│   └── list/
│       └── item.phtml (product cards with Diamond Aircraft styling)
├── Magento_Cms/templates/
│   └── product-slider.phtml (reusable product slider component)
├── Hyva_Theme/web/svg/diamond/ (custom SVG icons)
│   ├── aircraft.svg
│   ├── pilot.svg
│   ├── propeller.svg
│   ├── runway.svg
│   ├── headset.svg
│   ├── compass.svg
│   ├── language.svg
│   ├── support.svg
│   └── shipping.svg
└── DIAMOND_THEME_CONVERSION.md (this file)

app/code/Diamond/Theme/
├── ViewModel/
│   └── DiamondIcons.php (custom icons ViewModel)
└── etc/
    ├── di.xml (ViewModel registration)
    └── module.xml (existing module)
```

## Implementation Benefits

### Pure Tailwind Approach
- **No Custom CSS**: All styling is done through Tailwind utility classes
- **Better Performance**: Smaller CSS bundle size through Tailwind's purging
- **Easier Maintenance**: No need to maintain separate CSS files
- **Consistent Design**: All styling follows Tailwind's design system
- **Better Developer Experience**: IntelliSense support for Tailwind classes

### Hyvä Best Practices
- **Template Overrides**: Clean separation of theme customizations
- **Responsive Design**: Mobile-first approach with Tailwind breakpoints
- **Accessibility**: Proper ARIA attributes and focus management
- **Performance**: Optimized for Core Web Vitals
- **Alpine.js Integration**: Smooth JavaScript interactions

## Notes
- The conversion focuses on visual fidelity to the original Diamond Aircraft design
- **Only Tailwind CSS classes** are used - no custom CSS files
- All styling is applied directly in phtml template files
- Responsive design follows mobile-first principles
- Color scheme matches the original blue theme (#00467e primary)
- Template files can be easily customized by modifying Tailwind classes
