<?php
/**
 * Diamond Aircraft Product List Template
 * This template creates horizontal product carousels matching the original design
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Block\Product\ListProduct;
use Magento\Framework\Escaper;

/** @var ListProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

$productCollection = $block->getLoadedProductCollection();
$products = $productCollection->getItems();

if (empty($products)) {
    return;
}

$viewMode = $block->getMode() ?: 'grid';
$image = 'category_page_grid';
$showDescription = false;
$templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;

$pos = $block->getPositioned();
$position = $block->getPosition();
$uniqueId = '_' . uniqid();
?>

<!-- Diamond Aircraft Product List -->
<div class="products wrapper <?= $escaper->escapeHtmlAttr($viewMode) ?> products-<?= $escaper->escapeHtmlAttr($viewMode) ?>"
     x-data="productListSlider<?= $escaper->escapeHtml($uniqueId) ?>()">
    
    <?= $block->getAdditionalHtml() ?>
    
    <?php if ($block->getMode() === 'grid'): ?>
        <!-- Grid View with Horizontal Scrolling -->
        <div class="products list items product-items">
            <!-- Slider Controls -->
            <div class="flex justify-between items-center mb-6">
                <div class="flex space-x-2">
                    <button 
                        @click="prevSlide()"
                        class="bg-white border border-gray-300 rounded-full p-3 hover:bg-gray-50 hover:border-diamond-blue transition-all duration-200 shadow-sm"
                        :disabled="currentSlide === 0"
                        :class="{ 'opacity-50 cursor-not-allowed': currentSlide === 0 }"
                        aria-label="<?= $escaper->escapeHtmlAttr(__('Previous products')) ?>"
                    >
                        <?= $heroicons->chevronLeftHtml('w-5 h-5 text-gray-600', 20, 20) ?>
                    </button>
                    <button 
                        @click="nextSlide()"
                        class="bg-white border border-gray-300 rounded-full p-3 hover:bg-gray-50 hover:border-diamond-blue transition-all duration-200 shadow-sm"
                        :disabled="currentSlide >= maxSlide"
                        :class="{ 'opacity-50 cursor-not-allowed': currentSlide >= maxSlide }"
                        aria-label="<?= $escaper->escapeHtmlAttr(__('Next products')) ?>"
                    >
                        <?= $heroicons->chevronRightHtml('w-5 h-5 text-gray-600', 20, 20) ?>
                    </button>
                </div>
            </div>

            <!-- Products Slider -->
            <div class="overflow-hidden">
                <ol class="products list items product-items flex transition-transform duration-300 ease-in-out"
                    :style="`transform: translateX(-${currentSlide * slideWidth}%)`">
                    <?php foreach ($products as $_product): ?>
                        <li class="item product product-item flex-none w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 px-3">
                            <?= /* @noEscape */ $block->getProductDetailsHtml($_product) ?>
                        </li>
                    <?php endforeach; ?>
                </ol>
            </div>
        </div>
    <?php else: ?>
        <!-- List View (Vertical) -->
        <div class="products list items product-items">
            <ol class="products list items product-items space-y-6">
                <?php foreach ($products as $_product): ?>
                    <li class="item product product-item">
                        <?= /* @noEscape */ $block->getProductDetailsHtml($_product) ?>
                    </li>
                <?php endforeach; ?>
            </ol>
        </div>
    <?php endif; ?>
    
    <?= $block->getPagerHtml() ?>
</div>

<script>
function productListSlider<?= $escaper->escapeHtml($uniqueId) ?>() {
    return {
        currentSlide: 0,
        slideWidth: 20, // 20% for 5 items per view on desktop
        maxSlide: <?= max(0, count($products) - 5) ?>, // Show 5 items at once
        
        init() {
            this.updateSlideWidth();
            window.addEventListener('resize', () => this.updateSlideWidth());
        },
        
        updateSlideWidth() {
            const width = window.innerWidth;
            const productCount = <?= count($products) ?>;
            
            if (width < 640) {
                this.slideWidth = 100; // 1 item on mobile
                this.maxSlide = Math.max(0, productCount - 1);
            } else if (width < 768) {
                this.slideWidth = 50; // 2 items on small tablets
                this.maxSlide = Math.max(0, productCount - 2);
            } else if (width < 1024) {
                this.slideWidth = 33.333; // 3 items on tablets
                this.maxSlide = Math.max(0, productCount - 3);
            } else if (width < 1280) {
                this.slideWidth = 25; // 4 items on small desktop
                this.maxSlide = Math.max(0, productCount - 4);
            } else {
                this.slideWidth = 20; // 5 items on large desktop
                this.maxSlide = Math.max(0, productCount - 5);
            }
            
            // Ensure currentSlide doesn't exceed maxSlide
            if (this.currentSlide > this.maxSlide) {
                this.currentSlide = this.maxSlide;
            }
        },
        
        nextSlide() {
            if (this.currentSlide < this.maxSlide) {
                this.currentSlide++;
            }
        },
        
        prevSlide() {
            if (this.currentSlide > 0) {
                this.currentSlide--;
            }
        }
    }
}
</script>
