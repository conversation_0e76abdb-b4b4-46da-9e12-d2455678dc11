<?php
/**
 * Diamond Aircraft Footer Template
 * Based on the Live Site footer design
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);

// Footer configuration
$storeInfo = [
    'name' => 'Diamond Pilot Shop',
    'address' => [
        'street' => 'Ferdinand-Graf-von-Zeppelin-Straße 1',
        'city' => '2700 Wiener Neustadt',
        'country' => 'Austria'
    ],
    'contact' => [
        'email' => '<EMAIL>',
        'phone' => '+43 2622 26700 1100'
    ],
    'hours' => [
        'weekdays' => 'Monday - Thursday 08:30 - 16:00',
        'friday' => 'Friday 08:30 - 14:30'
    ],
    'flying_directions' => [
        'location' => 'Wiener Neustadt, AUT, LOAN',
        'frequency' => 'Radio Frequency: 122,655 MHz'
    ]
];

$paymentMethods = [
    'visa' => 'Visa',
    'mastercard' => 'Mastercard',
    'paypal' => 'PayPal',
    'apple-pay' => 'Apple Pay',
    'sofort' => 'Sofort',
    'ups' => 'UPS',
    'post' => 'Post'
];

$socialLinks = [
    'facebook' => '#',
    'instagram' => '#',
    'youtube' => '#',
    'twitter' => '#'
];
?>

<!-- Diamond Aircraft Footer -->
<footer class="footer bg-white border-t border-gray-200">

    <!-- Main Footer Content -->
    <div class="footer-main py-16 bg-gray-50">
        <div class="max-w-screen-2xl mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

                <!-- Store Information -->
                <div class="footer-section">
                    <div class="flex items-start mb-4">
                        <div class="flex-shrink-0 mr-4 mt-1">
                            <!-- Location Icon -->
                            <svg class="w-6 h-6 text-diamond-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg text-gray-800 mb-3">
                                <?= $escaper->escapeHtml($storeInfo['name']) ?>
                            </h3>
                            <div class="text-gray-600 space-y-1">
                                <p><?= $escaper->escapeHtml($storeInfo['address']['street']) ?></p>
                                <p><?= $escaper->escapeHtml($storeInfo['address']['city']) ?></p>
                                <p class="mt-3">
                                    <a href="mailto:<?= $escaper->escapeHtmlAttr($storeInfo['contact']['email']) ?>"
                                       class="text-diamond-blue hover:text-diamond-blue-dark transition-colors duration-200">
                                        <?= $escaper->escapeHtml($storeInfo['contact']['email']) ?>
                                    </a>
                                </p>
                                <p>
                                    <a href="tel:<?= $escaper->escapeHtmlAttr(str_replace(' ', '', $storeInfo['contact']['phone'])) ?>"
                                       class="text-diamond-blue hover:text-diamond-blue-dark transition-colors duration-200">
                                        <?= $escaper->escapeHtml($storeInfo['contact']['phone']) ?>
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shop Hours -->
                <div class="footer-section">
                    <h3 class="font-bold text-lg text-gray-800 mb-4">
                        <?= $escaper->escapeHtml(__('Shop Hours')) ?>
                    </h3>
                    <div class="text-gray-600 space-y-2">
                        <p><?= $escaper->escapeHtml($storeInfo['hours']['weekdays']) ?></p>
                        <p><?= $escaper->escapeHtml($storeInfo['hours']['friday']) ?></p>
                    </div>
                </div>

                <!-- Flying Directions -->
                <div class="footer-section">
                    <h3 class="font-bold text-lg text-gray-800 mb-4">
                        <?= $escaper->escapeHtml(__('Flying Directions')) ?>
                    </h3>
                    <div class="text-gray-600 space-y-2">
                        <p><?= $escaper->escapeHtml($storeInfo['flying_directions']['location']) ?></p>
                        <p><?= $escaper->escapeHtml($storeInfo['flying_directions']['frequency']) ?></p>
                    </div>
                </div>

                <!-- Newsletter & Social -->
                <div class="footer-section">
                    <h3 class="font-bold text-lg text-gray-800 mb-4">
                        <?= $escaper->escapeHtml(__('Stay Connected')) ?>
                    </h3>

                    <!-- Newsletter Signup -->
                    <div class="mb-6">
                        <p class="text-gray-600 mb-3"><?= $escaper->escapeHtml(__('Subscribe to our newsletter for updates and exclusive offers.')) ?></p>
                        <form class="flex">
                            <input
                                type="email"
                                placeholder="<?= $escaper->escapeHtmlAttr(__('Your email address')) ?>"
                                class="flex-1 px-4 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-diamond-blue focus:border-transparent"
                                required
                            />
                            <button
                                type="submit"
                                class="px-6 py-2 bg-diamond-blue hover:bg-diamond-blue-dark text-white font-semibold rounded-r-lg transition-colors duration-200"
                            >
                                <?= $escaper->escapeHtml(__('Subscribe')) ?>
                            </button>
                        </form>
                    </div>

                    <!-- Social Links -->
                    <div>
                        <p class="text-gray-600 mb-3"><?= $escaper->escapeHtml(__('Follow us on social media:')) ?></p>
                        <div class="flex space-x-4">
                            <?php foreach ($socialLinks as $platform => $url): ?>
                            <a href="<?= $escaper->escapeUrl($url) ?>"
                               class="p-2 bg-gray-200 hover:bg-diamond-blue text-gray-600 hover:text-white rounded-full transition-all duration-200"
                               aria-label="<?= $escaper->escapeHtmlAttr(__('Follow us on %1', ucfirst($platform))) ?>">
                                <?php if ($platform === 'facebook'): ?>
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
                                <?php elseif ($platform === 'instagram'): ?>
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z"/></svg>
                                <?php elseif ($platform === 'youtube'): ?>
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/></svg>
                                <?php elseif ($platform === 'twitter'): ?>
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>
                                <?php endif; ?>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Methods & Legal -->
    <div class="footer-bottom py-8 bg-white border-t border-gray-200">
        <div class="max-w-screen-2xl mx-auto px-4">
            <div class="flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0">

                <!-- Payment Methods -->
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600 font-medium"><?= $escaper->escapeHtml(__('We accept:')) ?></span>
                    <div class="flex items-center space-x-3">
                        <?php foreach (array_slice($paymentMethods, 0, 6) as $method => $name): ?>
                        <img
                            src="<?= $escaper->escapeUrl($block->getViewFileUrl('images/payment/' . $method . '.png')) ?>"
                            alt="<?= $escaper->escapeHtmlAttr($name) ?>"
                            class="h-8 w-auto opacity-70 hover:opacity-100 transition-opacity duration-200"
                            loading="lazy"
                        />
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Tax Information -->
                <div class="text-center text-sm text-gray-600">
                    <p><?= $escaper->escapeHtml(__('*incl. VAT, excl. shipping.')) ?></p>
                    <p><?= $escaper->escapeHtml(__('Depending on your delivery address, VAT may vary at checkout.')) ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Corporate Footer -->
    <div class="footer-corporate py-6 bg-gray-800 text-white">
        <div class="max-w-screen-2xl mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">

                <!-- Corporate Link -->
                <div class="flex items-center">
                    <a href="https://www.diamondaircraft.com/en"
                       target="_blank"
                       rel="noopener"
                       class="flex items-center hover:opacity-80 transition-opacity duration-200">
                        <img
                            src="<?= $escaper->escapeUrl($block->getViewFileUrl('images/logo_aircraft.svg')) ?>"
                            alt="<?= $escaper->escapeHtmlAttr(__('Diamond Aircraft')) ?>"
                            class="h-8 w-auto mr-3"
                            loading="lazy"
                        />
                        <span class="text-sm"><?= $escaper->escapeHtml(__('to Corporate Website')) ?></span>
                    </a>
                </div>

                <!-- Legal Links & Copyright -->
                <div class="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
                    <div class="flex items-center space-x-4 text-sm">
                        <a href="<?= $escaper->escapeUrl($block->getUrl('imprint')) ?>"
                           class="hover:text-gray-300 transition-colors duration-200">
                            <?= $escaper->escapeHtml(__('Imprint')) ?>
                        </a>
                        <a href="<?= $escaper->escapeUrl($block->getUrl('privacy')) ?>"
                           class="hover:text-gray-300 transition-colors duration-200">
                            <?= $escaper->escapeHtml(__('Privacy')) ?>
                        </a>
                    </div>
                    <div class="text-sm text-gray-400">
                        © <?= date('Y') ?> <?= $escaper->escapeHtml(__('Diamond Aircraft Industries')) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
