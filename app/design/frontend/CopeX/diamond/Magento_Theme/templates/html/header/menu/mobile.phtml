<?php
/**
 * Diamond Aircraft Mobile Navigation Template
 * Based on Hyvä Themes - https://hyva.io
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var Navigation $viewModelNavigation */
$viewModelNavigation = $viewModels->require(Navigation::class, $block);

$uniqueId = '_' . uniqid();

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

?>
<nav
    x-data="initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?>()"
    @load.window="setActiveMenu($root)"
    @keydown.window.escape="closeMenu()"
    @toggle-mobile-menu.window="toggleMenu()"
    class="z-20 order-2 sm:order-1 lg:order-2 navigation lg:hidden"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation')) ?>"
    role="navigation"
>
    <!-- Mobile Menu Overlay -->
    <div
        x-ref="mobileMenuNavLinks"
        class="fixed inset-0 z-50 hidden bg-white"
        :class="{ 'flex': open, 'hidden': !open }"
        :aria-hidden="open ? 'false' : 'true'"
        role="dialog"
        aria-modal="true"
    >
        <!-- Header -->
        <div class="flex items-center justify-between w-full px-4 py-3 bg-diamond-blue text-white">
            <h2 class="text-lg font-bold"><?= $escaper->escapeHtml(__('Menu')) ?></h2>
            <button
                @click="closeMenu()"
                class="rounded-full p-2 hover:bg-white hover:bg-opacity-20 transition-colors duration-200"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Close menu')) ?>"
                type="button"
            >
                <?= $heroicons->xHtml('w-6 h-6', 24, 24, ["aria-hidden" => "true"]) ?>
            </button>
        </div>

        <!-- Menu Content -->
        <div class="flex-1 overflow-y-auto">
            <ul
                class="flex flex-col"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation links')) ?>"
            >
                <?php foreach ($menuItems as $index => $menuItem): ?>
                    <li
                        data-child-id="<?= $escaper->escapeHtmLAttr($index) ?>-main"
                        class="level-0 border-b border-gray-200"
                    >
                        <div
                            class="flex items-center transition-transform duration-300 ease-in-out transform"
                            :class="{
                                '-translate-x-full' : mobilePanelActiveId,
                                'translate-x-0' : !mobilePanelActiveId
                            }"
                        >
                            <a
                                class="flex items-center flex-1 px-6 py-4 text-gray-800 hover:bg-gray-50 hover:text-diamond-blue transition-colors duration-200"
                                href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                            >
                                <span class="font-medium"><?= $escaper->escapeHtml($menuItem['name']) ?></span>
                            </a>
                            <?php if (!empty($menuItem['childData'])): ?>
                                <button
                                    @click="openSubcategory('<?= /* @noEscape */ $index ?>')"
                                    class="flex items-center justify-center w-12 h-12 text-gray-600 hover:text-primary hover:bg-gray-50 transition-colors duration-200"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Open %1 subcategories', $menuItem['name'])) ?>"
                                    aria-haspopup="true"
                                    :aria-expanded="mobilePanelActiveId === '<?= /* @noEscape */ (string) $index ?>'"
                                >
                                    <?= $heroicons->chevronRightHtml('w-5 h-5', 20, 20, ["aria-hidden" => "true"]) ?>
                                </button>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($menuItem['childData'])): ?>
                            <div
                                data-child-id="<?= $escaper->escapeHtmLAttr($index) ?>"
                                class="absolute inset-0 z-10 flex flex-col bg-white"
                                :class="{
                                    'hidden': mobilePanelActiveId !== '<?= /* @noEscape */ (string) $index ?>'
                                }"
                            >
                                <!-- Submenu Header -->
                                <div class="flex items-center px-4 py-3 bg-gray-50 border-b border-gray-200">
                                    <button
                                        type="button"
                                        class="flex items-center text-diamond-blue hover:text-diamond-blue-dark transition-colors duration-200"
                                        @click="backToMainCategories('<?= /* @noEscape */ $index ?>-main')"
                                        aria-label="<?= $escaper->escapeHtmlAttr(__('Back to main categories')) ?>"
                                    >
                                        <?= $heroicons->chevronLeftHtml('w-5 h-5 mr-2', 20, 20, ["aria-hidden" => "true"]); ?>
                                        <span class="font-medium"><?= $escaper->escapeHtml($menuItem['name']) ?></span>
                                    </button>
                                </div>

                                <!-- Submenu Items -->
                                <ul
                                    class="flex-1 overflow-y-auto transition-transform duration-300 ease-in-out translate-x-full transform"
                                    :class="{
                                        'translate-x-full' : mobilePanelActiveId !== '<?= /* @noEscape */ (string) $index ?>',
                                        'translate-x-0' : mobilePanelActiveId === '<?= /* @noEscape */ (string) $index ?>',
                                    }"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Subcategories')) ?>"
                                >
                                    <!-- View All Link -->
                                    <li class="border-b border-gray-200">
                                        <a
                                            href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                            title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                                            class="flex items-center px-6 py-4 text-diamond-blue font-medium hover:bg-gray-50 transition-colors duration-200"
                                        >
                                            <?= $escaper->escapeHtml(__('View All %1', $menuItem['name'])) ?>
                                        </a>
                                    </li>

                                    <?php foreach ($menuItem['childData'] as $subMenuItem): ?>
                                        <li class="border-b border-gray-200">
                                            <a
                                                href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                                title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                                class="flex items-center px-6 py-4 text-gray-700 hover:bg-gray-50 hover:text-diamond-blue transition-colors duration-200"
                                            >
                                                <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
</nav>

<script>
    'use strict';

    const initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            mobilePanelActiveId: null,
            open: false,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('text-primary', 'font-bold');
                    item.closest('li.level-0') &&
                    item.closest('li.level-0').querySelector('a').classList.add('text-primary', 'font-bold');
                });
            },
            toggleMenu() {
                if (this.open) {
                    this.closeMenu();
                } else {
                    this.openMenu();
                }
            },
            openMenu() {
                this.open = true;
                this.$nextTick(() => hyva.trapFocus(this.$refs['mobileMenuNavLinks']));
                // Prevent body scrolling while mobile menu opened
                document.body.style.overflow = 'hidden';
            },
            closeMenu() {
                document.body.style.overflow = '';
                this.open = false;
                this.mobilePanelActiveId = null;
                this.$nextTick(() => hyva.releaseFocus());
            },
            openSubcategory(index) {
                const menuNodeRef = document.querySelector('[data-child-id=' + index + ']');
                this.mobilePanelActiveId = this.mobilePanelActiveId === index ? 0 : index;
                this.$nextTick(() => hyva.trapFocus(menuNodeRef));
            },
            backToMainCategories(index) {
                const menuNodeRef = document.querySelector('[data-child-id=' + index + ']');
                this.mobilePanelActiveId = 0;
                this.$nextTick(() => {
                    hyva.trapFocus(this.$refs['mobileMenuNavLinks']);
                    menuNodeRef.querySelector('a').focus();
                });
            }
        }
    }
</script>
