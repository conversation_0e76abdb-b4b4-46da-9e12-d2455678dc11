<?php
/**
 * Diamond Aircraft Logo Template
 * Based on Hyvä Themes - https://hyva.io
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Logo\LogoSizeResolver;
use Hyva\Theme\ViewModel\Logo\LogoPathResolver;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Framework\Escaper;
use Magento\Theme\Block\Html\Header\Logo;

/** @var Logo $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$storeName = $block->getThemeName() ?: $storeConfig->getStoreConfig('general/store_information/name') ?: __('Diamond Aircraft Store');

/** @var LogoSizeResolver $logoSizeResolver */
$logoSizeResolver = $viewModels->require(LogoSizeResolver::class);
$logoWidth = $logoSizeResolver && $logoSizeResolver->getWidth()
    ? $logoSizeResolver->getWidth()
    : $block->getLogoWidth();
$logoHeight = $logoSizeResolver && $logoSizeResolver->getHeight()
    ? $logoSizeResolver->getHeight()
    : $block->getLogoHeight();

/** @var LogoPathResolver $logoPathResolver */
$logoPathResolver = $block->getData('logoPathResolver');
$logoSrc = $logoPathResolver && method_exists($logoPathResolver, 'getLogoSrc')
    ? $logoPathResolver->getLogoSrc($block->getData('logo_file'))
    : $block->getLogoSrc();

// Check if this is the homepage for transparent header
$isHomepage = $block->getRequest()->getFullActionName() === 'cms_index_index';
?>

<div class="flex items-center">
    <a
        class="flex items-center justify-center text-xl font-bold tracking-wide no-underline hover:no-underline transition-opacity duration-200 hover:opacity-80"
        href="<?= $escaper->escapeUrl($block->getUrl('')) ?>"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Go to Diamond Aircraft Home page')) ?>"
    >
        <?php if ($logoSrc): ?>
            <img
                src="<?= $escaper->escapeUrl($logoSrc) ?>"
                alt="<?= $escaper->escapeHtmlAttr($block->getLogoAlt() ?: $storeName) ?>"
                class="h-12 w-auto <?= $isHomepage ? 'filter brightness-0 invert' : '' ?> transition-all duration-200"
                <?= $logoWidth
                    ? 'width="' . $escaper->escapeHtmlAttr($logoWidth) . '"'
                    : 'width="200"'
                ?>
                <?= $logoHeight
                    ? 'height="' . $escaper->escapeHtmlAttr($logoHeight) . '"'
                    : 'height="48"'
                ?>
                loading="eager"
            />
        <?php else: ?>
            <!-- Fallback text logo with Diamond Aircraft styling -->
            <span class="<?= $isHomepage ? 'text-white' : 'text-diamond-blue' ?> font-bold text-2xl tracking-wide">
                <?= $escaper->escapeHtml($storeName) ?>
            </span>
        <?php endif; ?>
    </a>
</div>
