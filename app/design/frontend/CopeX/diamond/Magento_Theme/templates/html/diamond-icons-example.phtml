<?php
/**
 * Diamond Aircraft Custom Icons Example Template
 * This template demonstrates how to use the custom Diamond Aircraft icons
 */

declare(strict_types=1);

use Diamond\Theme\ViewModel\DiamondIcons;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var DiamondIcons $diamondIcons */
$diamondIcons = $viewModels->require(DiamondIcons::class);
?>

<!-- Diamond Aircraft Custom Icons Example -->
<div class="diamond-icons-example p-8 bg-gray-50">
    <h2 class="text-2xl font-bold text-primary mb-6"><?= $escaper->escapeHtml(__('Diamond Aircraft Custom Icons')) ?></h2>
    
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
        
        <!-- Aircraft Icon -->
        <div class="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="text-primary mb-2">
                <?= $diamondIcons->aircraftHtml('w-8 h-8') ?>
            </div>
            <span class="text-sm text-gray-600 text-center">Aircraft</span>
            <code class="text-xs text-gray-400 mt-1">aircraftHtml()</code>
        </div>
        
        <!-- Pilot Icon -->
        <div class="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="text-primary mb-2">
                <?= $diamondIcons->pilotHtml('w-8 h-8') ?>
            </div>
            <span class="text-sm text-gray-600 text-center">Pilot</span>
            <code class="text-xs text-gray-400 mt-1">pilotHtml()</code>
        </div>
        
        <!-- Propeller Icon -->
        <div class="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="text-primary mb-2">
                <?= $diamondIcons->propellerHtml('w-8 h-8') ?>
            </div>
            <span class="text-sm text-gray-600 text-center">Propeller</span>
            <code class="text-xs text-gray-400 mt-1">propellerHtml()</code>
        </div>
        
        <!-- Runway Icon -->
        <div class="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="text-primary mb-2">
                <?= $diamondIcons->runwayHtml('w-8 h-8') ?>
            </div>
            <span class="text-sm text-gray-600 text-center">Runway</span>
            <code class="text-xs text-gray-400 mt-1">runwayHtml()</code>
        </div>
        
        <!-- Headset Icon -->
        <div class="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="text-primary mb-2">
                <?= $diamondIcons->headsetHtml('w-8 h-8') ?>
            </div>
            <span class="text-sm text-gray-600 text-center">Headset</span>
            <code class="text-xs text-gray-400 mt-1">headsetHtml()</code>
        </div>
        
        <!-- Compass Icon -->
        <div class="flex flex-col items-center p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="text-primary mb-2">
                <?= $diamondIcons->compassHtml('w-8 h-8') ?>
            </div>
            <span class="text-sm text-gray-600 text-center">Compass</span>
            <code class="text-xs text-gray-400 mt-1">compassHtml()</code>
        </div>
        
    </div>
    
    <!-- Usage Example -->
    <div class="mt-8 p-6 bg-white rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-4"><?= $escaper->escapeHtml(__('Usage Example')) ?></h3>
        <div class="bg-gray-100 p-4 rounded-md">
            <code class="text-sm text-gray-700">
                &lt;?php<br>
                /** @var DiamondIcons $diamondIcons */<br>
                $diamondIcons = $viewModels->require(DiamondIcons::class);<br>
                ?&gt;<br><br>
                
                &lt;!-- Render aircraft icon with custom classes --&gt;<br>
                &lt;?= $diamondIcons->aircraftHtml('w-6 h-6 text-primary') ?&gt;<br><br>
                
                &lt;!-- Render pilot icon with hover effect --&gt;<br>
                &lt;?= $diamondIcons->pilotHtml('w-8 h-8 text-gray-600 hover:text-primary transition-colors duration-200') ?&gt;
            </code>
        </div>
    </div>
    
    <!-- Color Variations -->
    <div class="mt-8 p-6 bg-white rounded-lg shadow-sm">
        <h3 class="text-lg font-semibold text-gray-800 mb-4"><?= $escaper->escapeHtml(__('Color Variations')) ?></h3>
        <div class="flex items-center space-x-6">
            <div class="text-primary"><?= $diamondIcons->aircraftHtml('w-8 h-8') ?></div>
            <div class="text-gray-600"><?= $diamondIcons->aircraftHtml('w-8 h-8') ?></div>
            <div class="text-red-500"><?= $diamondIcons->aircraftHtml('w-8 h-8') ?></div>
            <div class="text-green-500"><?= $diamondIcons->aircraftHtml('w-8 h-8') ?></div>
            <div class="text-yellow-500"><?= $diamondIcons->aircraftHtml('w-8 h-8') ?></div>
        </div>
    </div>
</div>
