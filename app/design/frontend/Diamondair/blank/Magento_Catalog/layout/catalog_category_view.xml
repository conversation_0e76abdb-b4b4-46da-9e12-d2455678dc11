<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="head.additional">
            <block class="Magento\Framework\View\Element\Template" name="opengraph.default.image" template="Magento_Theme::html/og-default-image.phtml" />
        </referenceBlock>
        <!-- BEGIN BREADCRUMBS CHANGES -->
        <move element="category.view.container" destination="page.wrapper" after="header.container"/>
        <move element="breadcrumbs" destination="category.view.container" after="category.image"/>
        <move element="page.main.title" destination="category.view.container" after="breadcrumbs"/>

        <!-- END BREADCRUMBS CHANGES -->

        <!-- BEGIN CMS CONTENT (CATEGORY SEO TEXT) CHANGES -->
        <move element="category.cms" destination="content" after="category.products"/>
        <!-- END CMS CONTENT (CATEGORY SEO TEXT) CHANGES -->

    </body>
</page>
