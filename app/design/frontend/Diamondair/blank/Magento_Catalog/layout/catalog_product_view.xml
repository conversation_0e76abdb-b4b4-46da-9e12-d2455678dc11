<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">

    <body>
        <referenceContainer name="product.info.media" htmlClass="product__media product media"/>
        <referenceContainer name="product.info.main" htmlClass="product-info-main product__info-main"/>
        <referenceContainer name="product.info.price" htmlClass="product-info-price product__info-price"/>

        <move element="page.main.title" destination="product.info.main" before="-"/>
        <move element="product.info.sku" destination="product.info.main" after="page.main.title"/>
        <move element="product.info.main" destination="content" after="product.info.media"/>

        <referenceBlock name="product.info.stock.sku" remove="true"/>
        <referenceBlock name="product.info.review" remove="true"/>
        <referenceBlock name="product.info.overview" remove="true"/>
        <referenceBlock name="view.addto.compare" remove="true"/>

        <referenceContainer name="product.info.main">
            <block class="Diamond\Catalog\Block\ProductBadges" name="product.badge"
                   template="Magento_Catalog::product/view/badge.phtml" before="-" />
        </referenceContainer>

        <referenceContainer name="product.info.addto">
            <block class="Magento\Catalog\Block\Product\View" name="additional.information"
                   template="Magento_Catalog::product/view/additional-info.phtml"/>
        </referenceContainer>

    </body>
</page>
