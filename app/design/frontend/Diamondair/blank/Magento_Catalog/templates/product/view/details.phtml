<?php

/** @var \Magento\Catalog\Block\Product\View\Details $block */
?>
<?php if ($detailedInfoGroup = $block->getGroupSortedChildNames('detailed_info', 'getChildHtml')) : ?>
    <div class="product info detailed">
        <?php $layout = $block->getLayout(); ?>
        <div class="product data items">
            <?php foreach ($detailedInfoGroup as $name) : ?>
                <?php
                $html = $layout->renderElement($name);
                if (!trim($html)) {
                    continue;
                }
                $alias = $layout->getElementAlias($name);
                $label = $block->getChildData($alias, 'title');
                ?>
                <div class="data item title"
                     data-role="collapsible" id="tab-label-<?= $block->escapeHtmlAttr($alias) ?>">
                    <a class="data switch"
                       tabindex="-1"
                       data-toggle="trigger"
                       href="#<?= $block->escapeUrl($alias) ?>"
                       id="tab-label-<?= $block->escapeHtmlAttr($alias) ?>-title">
                        <?= /* @noEscape */
                        $label ?>
                    </a>
                </div>
                <div class="data item content"
                     aria-labelledby="tab-label-<?= $block->escapeHtmlAttr($alias) ?>-title"
                     id="<?= $block->escapeHtmlAttr($alias) ?>" data-role="content">
                    <?= /* @noEscape */
                    $html ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>

<script type="text/javascript">
    require(['jquery', 'matchMedia', 'accordion'], function ($, mediaCheck) {
        var detailsTabs = $('.product.data.items');
        var descriptionTab = $('#tab-label-description');
        // Making all the tabs collapsed except the first one
        mediaCheck({
            exit: function () {
                detailsTabs.tabs({
                    openedState: "active",
                    collapsible: true,
                    active: 0
                });
            }
        });
    })
</script>
