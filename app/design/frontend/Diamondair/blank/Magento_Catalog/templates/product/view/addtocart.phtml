<?php
/** @var $block \Magento\Catalog\Block\Product\View */
?>
<?php $_product = $block->getProduct(); ?>
<?php $buttonTitle = __('Add to Cart'); ?>
<?php if ($_product->isSaleable()) : ?>
    <div class="box-tocart">
        <div class="fieldset box-tocart__fieldset">
            <?php if ($block->shouldRenderQuantity()) : ?>
                <div class="field qty box-tocart__qty">
                    <div class="control box-tocart__control">
                        <input type="number"
                               name="qty"
                               id="qty"
                               min="0"
                               value="<?= $block->getProductDefaultQty() * 1 ?>"
                               title="<?= $block->escapeHtmlAttr(__('Qty')) ?>"
                               class="input-text qty box-tocart__input-qty"
                               data-validate="<?= $block->escapeHtml(json_encode($block->getQuantityValidators())) ?>"
                        />
                    </div>
                    <label class="label box-tocart__label"
                           for="qty"><span><?= $block->escapeHtml(__('Qty')) ?></span></label>
                </div>
            <?php endif; ?>
            <div class="actions box-tocart__actions">
                <button type="submit"
                        title="<?= $block->escapeHtmlAttr($buttonTitle) ?>"
                        class="action primary tocart box-tocart__action-btn"
                        id="product-addtocart-button" disabled>
                    <span class="i-cart"><?= $block->escapeHtml($buttonTitle) ?></span>
                </button>
                <?= $block->getChildHtml('', true) ?>
            </div>
        </div>
    </div>
<?php endif; ?>
<script type="text/x-magento-init">
    {
        "#product_addtocart_form": {
            "Magento_Catalog/js/validate-product": {}
        }
    }


</script>
