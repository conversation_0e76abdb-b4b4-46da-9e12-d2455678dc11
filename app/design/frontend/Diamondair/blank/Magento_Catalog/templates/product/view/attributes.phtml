<?php
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis

/**
 * Product additional attributes template
 *
 * @var $block \Magento\Catalog\Block\Product\View\Attributes
 */
?>
<?php
$_helper = $this->helper(Magento\Catalog\Helper\Output::class);
$_product = $block->getProduct();
?>
<?php if ($_additional = $block->getAdditionalData()) : ?>
    <div class="additional-attributes-wrapper table-wrapper additional-attribute-table">
        <table class="data table additional-attributes additional-attribute-table__data"
               id="product-attribute-specs-table">
            <tbody class="additional-attribute-table__tbody">
            <?php foreach ($_additional as $_data) : ?>
                <tr class="additional-attribute-table__tr">
                    <th class="col label additional-attribute-table__col-label"
                        scope="row"><?= $block->escapeHtml($_data['label']) ?></th>
                    <td class="col data additional-attribute-table__col-data"
                        data-th="<?= $block->escapeHtmlAttr($_data['label']) ?>"><?= /* @noEscape */
                        $_helper->productAttribute($_product, $_data['value'], $_data['code']) ?></td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php endif; ?>
