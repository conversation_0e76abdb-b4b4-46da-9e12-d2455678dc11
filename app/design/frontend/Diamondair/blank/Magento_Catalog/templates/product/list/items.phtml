<?php
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Generic.WhiteSpace.ScopeIndent.Incorrect

/* @var $block \Magento\Catalog\Block\Product\AbstractProduct */
?>

<?php
switch ($type = $block->getType()) {

    case 'related-rule':
        if ($exist = $block->hasItems()) {
            $type = 'related';
            $class = $type;

            $image = 'related_products_list';
            $title = __('Related Products');
            $items = $block->getAllItems();
            $limit = $block->getPositionLimit();
            $shuffle = (int)$block->isShuffled();
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showAddTo = true;
            $showCart = false;
            $templateType = null;
            $description = false;
        }
        break;

    case 'related':
        /** @var \Magento\Catalog\Block\Product\ProductList\Related $block */
        if ($exist = $block->getItems()->getSize()) {
            $type = 'related';
            $class = $type;

            $image = 'related_products_list';
            $title = __('Related Products');
            $items = $block->getItems();
            $limit = 0;
            $shuffle = 0;
            $canItemsAddToCart = $block->canItemsAddToCart();

            $showAddTo = true;
            $showCart = false;
            $templateType = null;
            $description = false;
        }
        break;

    case 'upsell-rule':
        if ($exist = $block->hasItems()) {
            $type = 'upsell';
            $class = $type;

            $image = 'upsell_products_list';
            $title = __('We found other products you might like!');
            $items = $block->getAllItems();
            $limit = $block->getPositionLimit();
            $shuffle = (int)$block->isShuffled();

            $showAddTo = false;
            $showCart = false;
            $templateType = null;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'upsell':
        /** @var \Magento\Catalog\Block\Product\ProductList\Upsell $block */
        if ($exist = count($block->getItemCollection()->getItems())) {
            $type = 'upsell';
            $class = $type;

            $image = 'upsell_products_list';
            $title = __('We found other products you might like!');
            $items = $block->getItemCollection()->getItems();
            $limit = $block->getItemLimit('upsell');
            $shuffle = 0;

            $showAddTo = false;
            $showCart = false;
            $templateType = null;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'crosssell-rule':
        /** @var \Magento\Catalog\Block\Product\ProductList\Crosssell $block */
        if ($exist = $block->hasItems()) {
            $type = 'crosssell';
            $class = $type;

            $image = 'cart_cross_sell_products';
            $title = __('More Choices');
            $items = $block->getItemCollection();

            $showAddTo = true;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'crosssell':
        /** @var \Magento\Catalog\Block\Product\ProductList\Crosssell $block */
        if ($exist = count($block->getItems())) {
            $type = 'crosssell';
            $class = $type;

            $image = 'cart_cross_sell_products';
            $title = __('More Choices');
            $items = $block->getItems();

            $showAddTo = true;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = false;
            $canItemsAddToCart = false;
        }
        break;

    case 'new':
        if ($exist = $block->getProductCollection()) {
            $type = 'new';
            $mode = 'grid';
            $type = $type . ' ' . $mode;

            $class = 'widget' . ' ' . $type;

            $image = 'new_products_content_widget_grid';
            $title = __('New Products');
            $items = $exist;

            $showAddTo = true;
            $showCart = true;
            $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
            $description = ($mode == 'list') ? true : false;
            $canItemsAddToCart = false;
        }
        break;

    default:
        $exist = null;
}
?>

<?php if ($exist) : ?>

<?php if ($type == 'related' || $type == 'upsell') : ?>
<?php if ($type == 'related') : ?>
<div class="block <?= $block->escapeHtmlAttr($class) ?> product-list"
     data-mage-init='{"relatedProducts":{"relatedCheckbox":".related.checkbox"}}'
     data-limit="<?= $block->escapeHtmlAttr($limit) ?>" data-shuffle="<?= /* @noEscape */
$shuffle ?>">
    <?php else : ?>
    <div class="block <?= $block->escapeHtmlAttr($class) ?> product-list" data-mage-init='{"upsellProducts":{}}'
         data-limit="<?= $block->escapeHtmlAttr($limit) ?>" data-shuffle="<?= /* @noEscape */
    $shuffle ?>">
        <?php endif; ?>
        <?php else : ?>
        <div class="block <?= $block->escapeHtmlAttr($class) ?> product-list">
            <?php endif; ?>
            <div
                class="block-title title product-list__heading product-list__heading-<?= $block->escapeHtmlAttr($class) ?>">
                <h3 id="block-<?= $block->escapeHtmlAttr($class) ?>-heading" class="product-list__header" role="heading"
                    aria-level="2"><?= $block->escapeHtml($title) ?></h3>
            </div>
            <div class="block-content content"
                 aria-labelledby="block-<?= $block->escapeHtmlAttr($class) ?>-heading product-list__related-content">
                <?php if ($type == 'related' && $canItemsAddToCart) : ?>
                    <h4 class="block-actions product-list__block-actions">
                        <?= $block->escapeHtml(__('Check items to add to the cart or')) ?>
                        <button type="button" class="action select product-list__action-select" role="button">
                            <span><?= $block->escapeHtml(__('select all')) ?></span></button>
                    </h4>
                <?php endif; ?>
                <div class="product-list__row">
                    <div class="product-list__slider ads-slider">
                        <div class="product-list__controls slider-controls" aria-label="Carousel Navigation">
                            <button class="slider-controls__button slider-controls__button-left" data-controls="prev"
                                    tabindex="0" aria-controls="product-list"><span
                                    class="slider-controls__left i-chevron-left" aria-label="slide left"></span></button>
                            <button class="slider-controls__button slider-controls__button-right" data-controls="next"
                                    tabindex="0" aria-controls="product-list"><span
                                    class="slider-controls__right i-chevron-right" aria-label="slide right"></span></button>
                        </div>
                        <div
                            class="products wrapper grid products-grid products-<?= $block->escapeHtmlAttr($type) ?> product-list__content">
                            <ol class="product-list__list slider-list">
                                <?php foreach ($items as $_item) : ?>
                                    <?php $available = ''; ?>
                                    <?php if (!$_item->isComposite() && $_item->isSaleable() && $type == 'related') : ?>
                                        <?php if (!$_item->getRequiredOptions()) : ?>
                                            <?php $available = 'related-available'; ?>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    <?php if ($type == 'related' || $type == 'upsell') : ?>
                                        <li class="item product product-item product-list__item" style="display: none;">
                                    <?php else : ?>
                                        <li class="item product product-item product-list__item">
                                    <?php endif; ?>
                                    <?php
                                    $darkColor = $_item->getDataByKey('parent_category') !== null ? '--c-category-dark: ' . $_item->getDataByKey('parent_category')->getData('ls_category_text') . ';' : '';
                                    $lightColor = $_item->getDataByKey('parent_category') !== null ? '--c-category-light: ' . $_item->getDataByKey('parent_category')->getData('ls_category_text2') . ';' : '';
                                    $saleColor = $_item->getDataByKey('is_product_sale') ? '--c-category-light: var(--c-sale);' : $lightColor;
                                    ?>
                                    <div class="product-list__info product-item-info <?= /* @noEscape */
                                    $available ?>" style="<?= $darkColor ?><?= $saleColor ?> ">
                                        <?php $productBadgesBlock = $this->getLayout()->createBlock('Diamond\Catalog\Block\ProductBadges')->setTemplate('Magento_Catalog::product/view/badge.phtml'); ?>
                                        <?= $productBadgesBlock->setProduct($_item)->toHtml() ?>
                                        <?= /* @noEscape */
                                        '<!-- ' . $image . '-->' ?>
                                        <a href="<?= $block->escapeUrl($block->getProductUrl($_item)) ?>"
                                           class="product photo product-item-photo product-list__photo">
                                            <?= $block->getImage($_item, $image)->toHtml() ?>
                                        </a>
                                        <?php if ($canItemsAddToCart && !$_item->isComposite() && $_item->isSaleable() && $type == 'related') : ?>
                                            <?php if (!$_item->getRequiredOptions()) : ?>
                                                <div
                                                    class="field choice related related-choice product-list__field-choice">
                                                    <input type="checkbox"
                                                           class="checkbox related related-checkbox product-list__choice-checkbox"
                                                           id="related-checkbox<?= $block->escapeHtmlAttr($_item->getId()) ?>"
                                                           name="related_products[]"
                                                           value="<?= $block->escapeHtmlAttr($_item->getId()) ?>"/>
                                                    <label class="label product-list__choice-label"
                                                           for="related-checkbox<?= $block->escapeHtmlAttr($_item->getId()) ?>"><span><?= $block->escapeHtml(__('Add to Cart')) ?></span></label>
                                                    <div class="border"></div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                        <div class="product details product-item-details product-list__details">
                                            <strong class="product name product-item-name product-list__name"><a
                                                    class="product-item-link product-list__link"
                                                    title="<?= $block->escapeHtml($_item->getName()) ?>"
                                                    href="<?= $block->escapeUrl($block->getProductUrl($_item)) ?>">
                                                    <?= $block->escapeHtml($_item->getName()) ?></a>
                                            </strong>

                                            <div class="product-list__price">
                                                <?= /* @noEscape */
                                                $block->getProductPrice($_item) ?>
                                            </div>

                                            <?php if ($templateType) : ?>
                                                <?= $block->getReviewsSummaryHtml($_item, $templateType) ?>
                                            <?php endif; ?>

                                            <div class="product-list__stock-status">
                                                <?php if ($_item->getIsSalable()): ?>
                                                    <div class="stock available product__available"
                                                         title="<?php /* @escapeNotVerified */
                                                         echo __('Availability') ?>">
                                                    <span><?php /* @escapeNotVerified */
                                                        echo __('In stock') ?></span>
                                                    </div>
                                                <?php else: ?>
                                                    <div
                                                        class="stock unavailable product-delivery-standard product__delivery-standard">
                                                <span><?php /* @escapeNotVerified */
                                                    echo __('Out of stock') ?></span></div>
                                                <?php endif; ?>
                                            </div>

                                            <div class="product-list__swatches">
                                                <?php $swatchBlock = $this->getLayout()->createBlock("Magento\Swatches\Block\Product\Renderer\Listing\Configurable")->setTemplate("Diamond_Swatches::product/renderer.phtml"); ?>
                                                <?= $swatchBlock->setProduct($_item)->toHtml(); ?>
                                            </div>
                                        </div>
                                    </div>
                                    </li>
                                <?php endforeach ?>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
