<?php
// phpcs:disable Magento2.Templates.ThisInTemplate
?>
<?php /** @var  $block \Magento\Sales\Block\Order\View */ ?>
<div class="order-details-items ordered">
    <?php $_order = $block->getOrder() ?>

    <div class="order-title">
        <strong><?= $block->escapeHtml(__('Items Ordered')) ?></strong>
        <?php if (!empty($_order->getTracksCollection()->getItems())) : ?>
            <?= $block->getChildHtml('tracking-info-link') ?>
        <?php endif; ?>
    </div>

    <?= $block->getChildHtml('order_items') ?>

    <?php if ($this->helper(\Magento\GiftMessage\Helper\Message::class)->isMessagesAllowed('order', $_order)
        && $_order->getGiftMessageId()
    ) : ?>
        <div class="block block-order-details-gift-message">
            <div class="block-title"><strong><?= $block->escapeHtml(__('Gift Message for This Order')) ?></strong></div>
            <?php
            $_giftMessage = $this->helper(\Magento\GiftMessage\Helper\Message::class)->getGiftMessageForEntity($_order);
            ?>
            <div class="block-content">
                <dl class="item-options">
                    <dt class="item-sender"><strong
                            class="label"><?= $block->escapeHtml(__('From')) ?></strong><?= $block->escapeHtml($_giftMessage->getSender()) ?>
                    </dt>
                    <dt class="item-recipient"><strong
                            class="label"><?= $block->escapeHtml(__('To')) ?></strong><?= $block->escapeHtml($_giftMessage->getRecipient()) ?>
                    </dt>
                    <dd class="item-message">
                        <?= /* @noEscape */
                        $this->helper(\Magento\GiftMessage\Helper\Message::class)->getEscapedGiftMessage($_order) ?>
                    </dd>
                </dl>
            </div>
        </div>
    <?php endif; ?>

    <div class="actions-toolbar">
        <div class="primary print-order-link">
            <a href="<?= $block->escapeUrl($block->getBackUrl()) ?>" class="action primary">
                <span><?= __('Print Order'); ?></span>
            </a>
        </div>
    </div>
</div>
