<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="head.additional">
            <block class="Magento\Framework\View\Element\Template" name="opengraph.default.image" template="Magento_Theme::html/og-default-image.phtml" />
        </referenceBlock>
        <referenceBlock name="page.main.title" remove="true"/>
        <!--
          BEGIN BEFORE FOOTER
        -->
        <referenceContainer name="page.bottom.container">

            <block name="seo.text.homepage.wrapper" class="Magento\Framework\View\Element\Template"
                   template="Magento_Theme::html/homepage/seo-text.phtml">
                <block class="Magento\Cms\Block\Block" name="seo.text.homepage">
                    <arguments>
                        <argument name="block_id" xsi:type="string">seo_text_homepage</argument>
                    </arguments>
                </block>
            </block>
        </referenceContainer>
        <!--
            END BEFORE FOOTER
        -->
    </body>
</page>
