<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="head.additional">
            <block class="Magento\Framework\View\Element\Template" name="opengraph.default.image" template="Magento_Theme::html/og-default-image.phtml" />
        </referenceBlock>
        <referenceContainer name='content'>
            <container
                name='cms-page'
                as='cms-page'
                htmlTag='div'
                htmlClass='cms-page'
            />
        </referenceContainer>
        <move element='cms_page' destination='cms-page'/>
    </body>
</page>
