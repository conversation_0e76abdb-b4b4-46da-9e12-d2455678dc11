<?xml version="1.0"?>
<page layout="2columns-left" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="sidebar.main.account_nav">
            <block class="Magento\Framework\View\Element\Template"
                   name="customer.account.toggle"
                   before="-"
                   template="Magento_Customer::account/toggle_menu.phtml"
            />
            <arguments>
                <argument name="block_title" translate="true" xsi:type="string">My Account</argument>
                <argument name="block_css" xsi:type="string">block-collapsible-nav account-nav</argument>
            </arguments>
            <referenceBlock name="customer_account_navigation">
                <block class="Magento\Framework\View\Element\Template"
                       name="customer.account.toggle.close"
                       before="-"
                       template="Magento_Customer::account/toggle_menu_close.phtml"
                />
                <arguments>
                    <argument name="css_class" xsi:type="string">nav items</argument>
                </arguments>
            </referenceBlock>
        </referenceBlock>
        <move element="page.main.title" destination="content" before="-"/>
        <referenceBlock name="customer-account-navigation-my-credit-cards-link" remove="true" />
        <referenceBlock name="customer-account-navigation-billing-agreements-link" remove="true"/>
    </body>
</page>
