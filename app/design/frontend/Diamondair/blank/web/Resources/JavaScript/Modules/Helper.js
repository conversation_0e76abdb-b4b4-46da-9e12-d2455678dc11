export const throttle = (func, interval) => {
    var timeout;
    return function() {
        var context = this,
            args = arguments;
        var later = function() {
            timeout = false;
        };
        if (!timeout) {
            func.apply(context, args);
            timeout = true;
            setTimeout(later, interval);
        }
    };
};

export const useMedia = (query, listener) => {
    // eslint-disable-next-line no-console
    if (!listener || typeof listener !== 'function') console.error('Must be a function');

    const matches = window.matchMedia(query).matches,
        media = window.matchMedia(query);

    media.addListener(listener);
    listener(media);

    return matches;
};

export const debounce = (callback, interval) => {
    let debounceTimeoutId;

    return function(...args) {
        clearTimeout(debounceTimeoutId);
        debounceTimeoutId = setTimeout(() => callback.apply(this, args), interval);
    };
};

export const polyfill = (function() {
    if (!Element.prototype.matches) {
        Element.prototype.matches = Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;
    }

    if (!Element.prototype.closest) {
        Element.prototype.closest = function(s) {
            var el = this;

            do {
                if (el.matches(s)) return el;
                el = el.parentElement || el.parentNode;
            } while (el !== null && el.nodeType === 1);
            return null;
        };
    }
})();

/**
 *
 * @param {string} url
 * @param {string} itemUrl
 */
export const activeUrl = (url, itemUrl) => {
    const urlToArray = url.split('/');
    return urlToArray.includes(itemUrl);
};

/**
 * get current path
 */
export const getUrlSegments = () => {
    const urlPath = window.location.pathname.replace('.html', '').split('/');
    const langNotExist = urlPath[1].length > 2 || urlPath[1].length === 0;
    const lang = langNotExist ? 'en' : urlPath[1];
    const url = langNotExist ? urlPath.join('/') : urlPath.slice(2).join('/');
    return {
        lang,
        url
    };
};
