import { getUrlSegments } from './Helper';

const text = {
    en: '<p>You are using an old browser version of Internet Explorer. For best user experience within our Diamond Pilot Shop please use a current <strong class="text--primary">browser</strong> like <a href="https://www.google.com/intl/de/chrome/" target="_blank" rel="nofollow">Google Chrome</a>, <a href="https://www.mozilla.org/firefox/new/" target="_blank" rel="nofollow">Mozilla Firefox</a> or <a href="https://www.microsoft.com/de-at/windows/microsoft-edge" target="_blank" rel="nofollow">Microsoft Edge</a>.</p>',
    de: '<p>Unser Webshop ist nicht mehr für die Darstellung mit Internet Explorer 11 optimiert. Bitte wechseln Sie zu einem aktuellen <strong class="text--primary">Browser</strong> wie <a href="https://www.google.com/intl/de/chrome/" target="_blank" rel="nofollow">Google Chrome</a>, <a href="https://www.mozilla.org/firefox/new/" target="_blank" rel="nofollow">Mozilla Firefox</a> oder <a href="https://www.microsoft.com/de-at/windows/microsoft-edge" target="_blank" rel="nofollow">Microsoft Edge</a>.</p>'
};

const IESupportContent = (selector, lang) => {
    const content = `<div class="${selector}">\n${text[lang]}\n<button><span class="i-burger_close"></span></button>\n</div><div class="${selector}__overlay"></div>`;
    const body = document.body;
    body.insertAdjacentHTML('beforeend', content);

};

const IESupportContentListener = (selector) => {
    const contentContainer = document.querySelector(`.${selector}`);
    const contentOverlay = document.querySelector(`.${selector}__overlay`);
    const contentButton = contentContainer.querySelector('button');
    if (!contentContainer) return;
    console.log('content', contentContainer);
    contentButton.addEventListener('click', removeIESupportContent(contentContainer, contentOverlay));
};

const removeIESupportContent = (contentContainer, contentOverlay) => () => {
    document.body.removeChild(contentContainer);
    document.body.removeChild(contentOverlay);
    sessionStorage.setItem('skipIESupportHint', 'true');
};

const IESupport = () => {
    const isIE = window.navigator.userAgent.indexOf('Trident/');
    const skipIESupportHint = sessionStorage.getItem('skipIESupportHint');
    if (isIE === -1 || skipIESupportHint) return;
    const {lang} = getUrlSegments();
    IESupportContent('ie-notification', lang);
    IESupportContentListener('ie-notification');
};

export default IESupport;
