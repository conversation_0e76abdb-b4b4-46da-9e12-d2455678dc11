const lazyLoader = (options) => {

    const lazyElements = [].slice.call(document.querySelectorAll('.lazyload'));

    const preloadImage = (target, src = null, srcset = null) => {

        if (src) {
            target.src = src;
            target.setAttribute('src', src);

        }
        if (srcset) {
            target.setAttribute('srcset', srcset);
        }
        target.classList.remove('lazyload');
        target.classList.add('lazyloaded');

    };

    const preloadBackgroundImage = (target, url) => {
        target.style.backgroundImage = `url(${url.trim()})`;
        target.classList.remove('lazyload');
        target.classList.add('lazyloaded');
    };

    const checkAttributeType = (target) => {
        const src = target.getAttribute('data-src');
        const srcset = target.getAttribute('data-srcset');
        const url = target.getAttribute('data-background');

        if (src || srcset) {
            preloadImage(target, src, srcset);
        }

        if (url) {
            preloadBackgroundImage (target, url);
        }
    };

    if ('IntersectionObserver' in window) {

        const imgOptions = {
            threshold: 0,
            rootMargin: '50px 0px 0px 50px',
            ...options
        };

        const imgObserve = (entries, imgObserver) => {
            entries.forEach(entry => {
                if (!entry.isIntersecting) {
                    return;
                } else {
                    checkAttributeType(entry.target);
                    imgObserver.unobserve(entry.target);
                }
            });
        };

        const imgObserver = new IntersectionObserver(imgObserve, imgOptions);
        lazyElements.forEach(el => imgObserver.observe(el));

    } else {
        lazyElements.forEach(lazyElement => checkAttributeType(lazyElement));
    }

};

function lazyLoad(options) {
    lazyLoader(options);
    var container = document.querySelector('body');

    var observer = new MutationObserver(function(mutations){
        // Do something here

        const lazyElements = mutations.filter(item => {
            return item.addedNodes.length > 0 && item.target.querySelector('.lazyload');
        });

        if (lazyElements.length === 0) return;

        lazyLoader(options);

    });

    observer.observe(container, {
        childList: true,
        subtree: true
    });
}

export default lazyLoad;
