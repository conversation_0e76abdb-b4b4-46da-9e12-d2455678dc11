
const ENTER_KEY_CODE = 13,
    NEXT_ARROW_KEY_CODE = 39,
    PREV_ARROW_KEY_CODE = 37,
    TAB_KEY_CODE = 9,
    ESCAPE_KEY_CODE = 27;

export const getCurrent = (arr, str) => {
    return arr
        .map((_, i) => i)
        .filter(e => arr[e] === str)[0];
};

const initLightBox = (selector, parentSelector = null, captionSelector, closeIcon = 'i-schlieen') => {

    let lightBoxes = [].slice.call(document.querySelectorAll(selector));

    if (!lightBoxes) {
        return false;
    }

    let images = [],
        currentImage = 0,
        focusedElBeforeOpen,
        focusableEl,
        docFrag = document.createDocumentFragment(),
        html = document.querySelector('html'),
        body = document.querySelector('body');

    //  CLOSE lightBox AND FOCUS THE LAST ACTIVE Element
    const closeLightBox = (event) => {
            const container = body.querySelector('.lightbox-container');

            body.removeChild(container);
            html.classList.remove('lightbox-html--overflow');
            if ( focusedElBeforeOpen ) {
                focusedElBeforeOpen.focus();
            }
        },

        //  GET CAPTION
        caption = (item) => {
            return captionSelector.match(/[#.]/) && item.parentNode.querySelector(captionSelector) ? item.parentNode.querySelector(captionSelector).textContent : item.getAttribute('title');
        },

        //  ADD CURRENT IMAGES TO THE STATE ( let  images = [])
        getImages = (group, items, current) => {
            if (group) {
                currentImage = getCurrent(
                    items,
                    current
                );

                images = items.map(item => ({
                    href: item.href,
                    caption: caption(item) ? caption(item) : ''
                }));
            }
            else {
                images[0] = {
                    href: items.href,
                    caption: caption(items) ? caption(items) : ''
                };
            }

        },

        // HANDLE SLIDES
        slide = (event, direction) => {
            const img = body.querySelector('.lightbox-image img'),
                caption = body.querySelector('.lightbox-image figcaption'),
                newCurrentNext = (currentImage + 1 + images.length) % images.length,
                newCurrentPrev = (currentImage - 1 + images.length) % images.length;

            if ((event.target.closest('.lightbox__arrows__btn--left') && event.type === 'click') || direction === 'prev') {
                currentImage = newCurrentPrev;
                img.src = images[newCurrentPrev].href;
                caption.textContent = images[newCurrentPrev].caption;
            }

            if ((event.target.closest('.lightbox__arrows__btn--right') && event.type === 'click') || direction === 'next') {
                currentImage = newCurrentNext;
                img.src = images[newCurrentNext].href;
                caption.textContent = images[newCurrentNext].caption;
            }
        },

        lightBoxTemplate = (images, group, currentImage) => {

            let lightboxContainer = document.createElement('div'),
                lightboxOverlay = document.createElement('div'),
                lightboxContent = document.createElement('div'),
                lightboxImage = document.createElement('div');

            lightboxContainer.className = 'lightbox-container';

            lightboxOverlay.className = 'lightbox-overlay';
            lightboxContent.className = 'lightbox-content';

            lightboxContent.innerHTML = '<button tabindex="0" type="button" aria-label="Close" class="lightbox-close ' + closeIcon + '"></button>';

            lightboxImage.className = 'lightbox-image';
            lightboxImage.innerHTML = `<img src="${images[currentImage].href}" alt="${images[currentImage].caption}"><figcaption class="image-caption">${images[currentImage].caption}</figcaption>`;

            if (group) {
                let lightboxArrows = document.createElement('div');
                lightboxArrows.className = 'lightbox__arrows';
                lightboxArrows.innerHTML = `<button tabindex="0" class="lightbox__arrows__btn lightbox__arrows__btn--left  i-angle-left"><span
            class="sr-only">Previous</span></button>
            <button tabindex="0" class="lightbox__arrows__btn lightbox__arrows__btn--right i-angle-right"><span
            class="sr-only">Next</span></button>
          `;
                lightboxContent.appendChild(lightboxArrows);
                lightboxArrows.addEventListener('click', slide);

            }

            lightboxContent.appendChild(lightboxImage);
            lightboxContainer.appendChild(lightboxOverlay);
            lightboxContainer.appendChild(lightboxContent);

            return {
                lightboxContainer,
                lightboxOverlay
            };
        },

        // CREATE lightboxes
        createLightBox = (group) => {

            const {lightboxContainer,lightboxOverlay} = lightBoxTemplate(images, group, currentImage);

            docFrag.appendChild(lightboxContainer);

            body.appendChild(docFrag);

            lightboxOverlay.addEventListener('click', closeLightBox);
            lightboxContainer.addEventListener('keydown', handleKeyEvents);

            body.querySelector('.lightbox-close').addEventListener('click', closeLightBox);

            if (group) {
                body.querySelector('.lightbox__arrows__btn--right').focus();

            }
            else {
                body.querySelector('.lightbox-close').focus();

            }
            focusableEl = [].slice.call(lightboxContainer.querySelectorAll('[tabindex="0"]'));
        },

        // HANDLE KEY EVENTS
        handleKeyEvents = (event) => {
            const firstFocusableEl = focusableEl[0],
                lastFocusableEl = focusableEl[focusableEl.length - 1];

            switch (event.keyCode) {
                case TAB_KEY_CODE:
                    if ( event.shiftKey ) {
                        if (document.activeElement === firstFocusableEl) {
                            lastFocusableEl.focus();
                            event.preventDefault();
                            break;
                        }
                    }
                    else /* tab */ {
                        if (document.activeElement === lastFocusableEl) {
                            firstFocusableEl.focus();
                            event.preventDefault();
                            break;
                        }
                    }
                    return;

                case PREV_ARROW_KEY_CODE:
                    event.preventDefault();
                    return slide(event, 'prev');

                case NEXT_ARROW_KEY_CODE:
                    event.preventDefault();
                    return slide(event, 'next');

                case ESCAPE_KEY_CODE:
                    return closeLightBox();
                default:
                    return;
            }
        },

        // CHECK IF IS GROUPED AND THE IMAGES
        getState = (item) => {
            const isGrouped = item.hasAttribute('lightbox-group') || parentSelector && item.closest(parentSelector) && [].slice.call(item.closest(parentSelector).querySelectorAll(`${selector}`)).length > 1 || item.parentNode.querySelectorAll('a').length > 1,
                selectImages = item.hasAttribute('lightbox-group') ? [].slice.call(item.closest(`#${item.getAttribute('lightbox-group') }`).querySelectorAll(selector)) : item.parentNode.parentNode.querySelectorAll(selector) > 1 ? [].slice.call(item.parentNode.parentNode.querySelectorAll(selector)) : parentSelector && isGrouped ? [].slice.call(item.closest(parentSelector).querySelectorAll(`${selector}`)) : item;

            return {
                isGrouped,
                selectImages
            };
        },

        // INITIAL FUNCTION FOR EACH CLICK
        openLightBox = (event) => {
            event.preventDefault();
            if (event.type !== 'click' && event.keyCode !== ENTER_KEY_CODE) {return;}
            focusedElBeforeOpen = event.currentTarget;
            images = [];
            currentImage = 0;
            focusableEl = null;

            const { isGrouped, selectImages } = getState(event.currentTarget);

            getImages(isGrouped, selectImages, event.currentTarget);
            createLightBox(isGrouped);
            html.classList.add('lightbox-html--overflow');

        },

        // REMOVE FROM GARBAGE COLLECTION
        destroy = () => {
            lightBoxes.map((lightbox) => {
                lightbox.removeEventListener('click', openLightBox);
            });
        },

        init = () => {
            destroy();
            lightBoxes.map((lightbox) => {
                lightbox.addEventListener('click', openLightBox);
            });
        };

    init();
};

export default initLightBox;
