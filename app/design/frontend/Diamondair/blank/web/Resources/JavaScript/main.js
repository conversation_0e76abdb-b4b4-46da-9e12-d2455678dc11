require('es6-promise').polyfill();
import 'core-js/features/array/includes'; // <- at the top of your entry point
import 'core-js/features/array/from'; // <- at the top of your entry point
import 'core-js/features/symbol/iterator';

import Toggle from './Modules/Toggle';
import lazyLoad from './Modules/lazyLoad';
import {productStartSlider} from './Modules/themeSliders';
import SidebarNavigation from './Modules/SidebarNavigation';
import HeaderTransparent from './Modules/HeaderTransparent';
import {scrollToTop} from './Modules/scrollToTop';
import IESupport from './Modules/IESupport';

IESupport();
SidebarNavigation();
window.addEventListener('load', () => {
    const productSlider = productStartSlider('.product-slider', {
        mouseDrag: true,
        items: 2,
        controlsContainer: '.product-slider .product-list__controls',
        responsive: {
            640: {
                items: 3
            },
            1024: {
                items: 4
            }
        }
    });

    const productListFull = productStartSlider('.product-slider--full', {
        mouseDrag: true,
        items: 2,
        responsive: {
            640: {
                items: 3
            },
            1024: {
                items: 4
            }
        }
    });

    const productListHalf = productStartSlider('.product-slider--half', {
        mouseDrag: true,
        items: 2
    });

    const mainSlider = productStartSlider('.slider-main', {
        mouseDrag: true,
        items: 1,
        nav: true,
        navAsThumbnails: true,
        controls: false,
        autoplay: true,
        autoplayTimeout: 4000
    });

    const adsSlider = productStartSlider('.ads-slider', {
        mouseDrag: true,
        items: 2,
        responsive: {
            640: {
                items: 3
            },
            1024: {
                items: 4
            }
        }
    });
});

Toggle({
    onHover: true,
    onnHoverMediaQuery: '(max-width: 768px)',
});

lazyLoad();
HeaderTransparent();
scrollToTop();

