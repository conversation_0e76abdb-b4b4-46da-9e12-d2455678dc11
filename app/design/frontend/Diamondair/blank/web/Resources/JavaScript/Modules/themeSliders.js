import {tns} from 'tiny-slider/src/tiny-slider.js';

export const productStartSlider = (selector, config) => {

    let sliders = [].slice.call(document.querySelectorAll(selector ? selector : '[tns-slider]'));

    if (!sliders && sliders.length === 0) return;

    sliders.forEach(slider => {

        let arrowsContainer = slider.querySelector('.slider-controls') ? slider.parentElement.querySelector('.slider-controls') : false,
            items = slider.getAttribute('items-per-slide') ? slider.getAttribute('items-per-slide') : 1,
            gutter = slider.getAttribute('gutter-width') ? slider.getAttribute('gutter-width') : 0;

        const slide = tns({
            container: slider.querySelector('.slider-list'),
            controlsContainer: arrowsContainer,
            loop: true,
            arrowKeys: true,
            nav: false,
            gutter: gutter,
            items: items,
            lazyload: true,
            lazyloadSelector: '.lazyload',
            ...config
        });

        if (arrowsContainer) {
            setTimeout(() => {
                arrowsContainer.querySelector('[data-controls="prev"]').setAttribute('tabindex', '0');
                arrowsContainer.querySelector('[data-controls="next"]').setAttribute('tabindex', '0');
            }, 1000);
        }
    });
};
