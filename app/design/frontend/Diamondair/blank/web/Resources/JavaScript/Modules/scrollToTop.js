import {debounce} from './Helper';

export const scrollToTop = () => {

    const scrollToTopButton = document.querySelector('.to-the-top-button');
    if (!scrollToTopButton) return;

    const classToggleByScroll = (event) => {
        let top = window.pageYOffset;

        if (top >= 300) {
            scrollToTopButton.classList.add('to-the-top-button--show');
        } else {
            scrollToTopButton.classList.remove('to-the-top-button--show');
        }
    };
    const debounceToTheTop = debounce(classToggleByScroll, 100);

    window.addEventListener('scroll', debounceToTheTop);

    const clickToTopButton = () => {
        scrollToTopButton.addEventListener('click', function () {
            window.scrollTo({top: 0, behavior: 'smooth'});
        });
    };

    clickToTopButton();
};
