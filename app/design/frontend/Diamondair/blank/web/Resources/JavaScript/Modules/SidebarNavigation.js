import { activeUrl, getUrlSegments } from './Helper';

const SidebarNavigation = () => {
    const sideBarNavElements = [...document.querySelectorAll('.sidebar-nav__parent')];

    if (sideBarNavElements.length === 0) return;
    const {lang, url} = getUrlSegments();
    const getActive = sideBarNavElements.filter(item => {
        const text = item.textContent;
        const trimText = text.trim().toLowerCase().replace(' & ', '-').replace(/\s/g, '-');
        const isActive = activeUrl(url, trimText);
        if (isActive) {
            return item;
        }
    });

    if (getActive.length > 0) {
        getActive[0].closest('.sidebar-nav__category').classList.add('is--active');
    }

};

export default SidebarNavigation;
