/**
 * @type {Function} - Set css class if slider after header Element exist
 */
const HeaderTransparent = () => {
    /**
     * Select Slider if directly after the Header
     * @type {HTMLElement}
     */
    const headerSlider = document.querySelector('.header + .slider-main');

    if (!headerSlider) return;

    /**
     * Select body Element and add the Class header-transparent
     * @type {HTMLElement}
     */
    const body = document.querySelector('body');
    const header = document.querySelector('.header');

    body.classList.add('header-transparent');
    header.classList.add('header-transparent');

};

export default HeaderTransparent;
