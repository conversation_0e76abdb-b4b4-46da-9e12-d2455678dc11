@checkbox__color: @c-base;

@checkbox__before-gap: 24px;
@checkbox__before-width: @checkbox__before-gap;
@checkbox__before-height: @checkbox__before-width;
@checkbox__after-height: @checkbox__before-gap - 2px;
@checkbox__after-width: @checkbox__before-gap - 2px;

@checkbox__border: 1px solid @c-gray-light;
@checkbox__border-radius: 3px;
@checkbox__border-color-focus: var(--c-gray-light);
@checkbox__border-color-checked: var(--c-gray-light);
@checkbox__border-color-disabled: var(--c-gray);

@checkbox__background: var(--c-white);
@checkbox__background-active: var(--c-gray-lightest);
@checkbox__background-checked: var(--c-gray-lightest);
@checkbox__background-disabled: var(--c-gray);

.field.choice:not(.review-field-rating) {
    display: block;
    padding-left: @checkbox__before-gap;
    position: relative;

    .checkbox, .radio, .required-entry {
        left: 0;
        opacity: 0;
        position: absolute;
        top: 0;
        z-index: -1;

        &:checked ~ .label {
            padding-left: 0.938rem;

            &::before {
                background-color: @checkbox__background-checked;
                border-color: @checkbox__border-color-checked;
            }

            &::after {
                background: @checkbox__background-checked !important;
                border-radius: @checkbox__border-radius;
                color: var(--c-blue-dark);
                content: @i-check;
                display: inline-block;
                font-family: @icons__font-name-custom;
                font-size: var(--fs-xss);
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                font-weight: normal;
                line-height: normal;
                overflow: hidden;
                padding: 0.188rem;
                speak: none;
                text-align: center;
                vertical-align: center;
            }
        }

        &:disabled ~ .label {
            color: var(--c-gray);

            &::before {
                background-color: @checkbox__background-disabled;
                border-color: @checkbox__border-color-disabled;
            }

            &::after {
                color: var(--c-gray);
            }
        }

        &:active ~ .label::before {
            background-color: @checkbox__background-active;
        }
    }
}


.field.choice:not(.review-field-rating) {

    .label {
        padding-left: 0.938rem;

        &::before {
            background-color: @checkbox__background;
            border: @checkbox__border;
            border-radius: @checkbox__border-radius;
            content: '';
            display: block;
            height: @checkbox__before-height;
            left: -1px;
            position: absolute;
            top: 4px;
            user-select: none;
            width: @checkbox__before-width;
        }

        &::after {
            color: @checkbox__color;
            height: @checkbox__after-height;
            left: 0;
            position: absolute;
            text-align: center;
            top: 5px;
            width: @checkbox__after-width;
        }
    }
}


.checkbox {
    display: block;
    padding-left: @checkbox__before-gap;
    position: relative;

    &__field {
        left: 0;
        opacity: 0;
        position: absolute;
        top: 0;
        z-index: -1;

        &:checked ~ .label {
            padding-left: 0.938rem;

            &::before {
                background-color: @checkbox__background-checked;
                border-color: @checkbox__border-color-checked;
            }

            &::after {
                background: @checkbox__background-checked !important;
                border-radius: @checkbox__border-radius;
                color: var(--c-blue-dark);
                content: @i-check;
                display: inline-block;
                font-family: @icons__font-name-custom;
                font-size: var(--fs-xss);
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
                font-weight: normal;
                line-height: normal;
                overflow: hidden;
                padding: 0.188rem;
                speak: none;
                text-align: center;
                vertical-align: center;
            }
        }

        &:disabled ~ .label {
            color: var(--c-gray);
            padding-left: 0.938rem;

            &::before {
                background-color: @checkbox__background-disabled;
                border-color: @checkbox__border-color-disabled;
            }

            &::after {
                color: var(--c-gray);
            }
        }

        &:focus ~ .label::before {

        }

        &:active ~ .label::before {
            background-color: @checkbox__background-active;
        }
    }
}


.checkbox {

    &__label {
        padding-left: 0.938rem;

        &::before {
            background-color: @checkbox__background;
            border: @checkbox__border;
            border-radius: @checkbox__border-radius;
            content: '';
            display: block;
            height: @checkbox__before-height;
            left: 0;
            position: absolute;
            top: 5px;
            user-select: none;
            width: @checkbox__before-width;
        }

        &::after {
            color: @checkbox__color;
            height: @checkbox__before-height;
            left: 0;
            position: absolute;
            text-align: center;
            top: 5px;
            width: @checkbox__before-width;
        }
    }
}
