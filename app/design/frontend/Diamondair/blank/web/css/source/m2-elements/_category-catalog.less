.product-items {
    display: grid;
    grid-column-gap: 15px;
    grid-row-gap: @xl;
    grid-template-columns: repeat(auto-fit, minmax(275px, 1fr));
    padding: 0.313rem;

    @media screen and (max-width: @screen__mmax) {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }

    @media screen and (max-width: @screen__xs) {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .product-list {
        &__info {
            max-width: 100%;
            @media screen and (max-width: @screen__mmax) {
                width: auto;
            }
        }

        &__item {
            --c-gray-light: rgba(189, 193, 196, 0.7);
            position: relative;
            padding-bottom: 0.938rem;

            &:after {
                background: var(--c-gray-light);
                bottom: 75px;
                content: '';
                display: flex;
                justify-content: center;
                position: absolute;
                right: -16px;
                top: 27px;
                width: 1px;
                z-index: -1;
            }

            &:hover {
                background: var(--c-white);

                &:after {
                    display: none;
                }
            }
        }
    }
}

.category-product__list {
    border-bottom: 1px solid var(--c-gray-light);
    margin-bottom: @lg;
    overflow: hidden;
    padding-bottom: @sm;
    padding-top: @base;
}

.product-image {
    &-photo {
        bottom: 0;
        display: block;
        height: auto;
        left: 0;
        margin: auto;
        max-width: 100%;
        position: relative;
        right: 0;
        top: 0;
    }

    &-wrapper {
        display: block;
        height: initial;
        overflow: hidden;
        position: relative;
        z-index: 1;
    }
}

.catalogsearch-result-index {
    .columns {
        position: relative;
    }

    div.toolbar-products:not(.is--active) {
        margin-top: @sm;
    }
}
