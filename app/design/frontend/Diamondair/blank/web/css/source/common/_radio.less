@radio__color: @c-white;
@radio__font-size: @font-size__base;

@radio__before-gap: 2rem;
@radio__before-width: @radio__before-gap - 1rem;
@radio__before-height: @radio__before-width;

@radio__border: 4px solid @c-white;
@radio__border-radius: 50%;
@radio__border-color-focus: @c-white;
@radio__border-color-checked: @c-white;

@radio__background: @c-base;
@radio__background-active: darken(@radio__background, 5%);
@radio__background-checked: @c-base;

.step-title {

    [type='radio']:checked,
    [type='radio']:not(:checked) {
        left: -9999px;
        position: absolute;
    }

    [type='radio']:checked + label,
    [type='radio']:not(:checked) + label {
        color: @radio__font-size;
        cursor: pointer;
        display: inline-block;
        line-height: @line-height__base;
        padding-left: @radio__before-gap;
        position: relative;
    }

    [type='radio']:checked + label:before,
    [type='radio']:not(:checked) + label:before {
        background: @radio__color;
        border: @radio__border;
        border-radius: 100%;
        content: '';
        height: 1.125rem;
        left: 0;
        position: absolute;
        top: 0;
        width: 18px;
    }

    [type='radio']:checked + label:after,
    [type='radio']:not(:checked) + label:after {
        background: @radio__background-active;
        border-radius: @radio__border-radius;
        content: '';
        height: 0.75rem;
        left: 0.188rem;
        position: absolute;
        top: 0.188rem;
        transition: all 0.2s ease;
        width: 0.75rem;
    }

    [type='radio']:not(:checked) + label:after {
        opacity: 0;
        transform: scale(0);
    }

    [type='radio']:checked + label:after {
        opacity: 1;
        transform: scale(1);
    }
}


.radio {
    display: block;
    padding-left: @radio__before-gap;
    position: relative;

    &__field {
        opacity: 0;
        position: absolute;
        z-index: -1;

        &:checked ~ .radio__label {

            &::before {
                background-color: @radio__background-checked;
                border-color: @radio__border-color-checked;
            }

            &::after {
                &:extend(.i-circle-thin:before all);
            }
        }

        &:focus ~ .radio__label::before {
            &:extend(.box-shadow--focus all);
        }

        &:active ~ .radio__label::before {
            background-color: @radio__background-active;
        }
    }
}

.radio {

    &__label {
        margin-bottom: @sm;

        &::before {
            background-color: @radio__background;
            border: @radio__border;
            border-radius: @radio__border-radius;
            content: '';
            display: block;
            height: @radio__before-height;
            left: 0;
            position: absolute;
            top: 0;
            user-select: none;
            width: @radio__before-width;
        }

        &::after {
            &:extend([class*='i-']:before all);

            align-items: center;
            color: @radio__color;
            display: flex;
            font-size: @radio__font-size;
            height: @radio__before-height;
            justify-content: center;
            left: 0;
            position: absolute;
            text-align: center;
            top: 0;
            width: @radio__before-width;
        }
    }
}
