// ----------------------------------------
// links
@c-link: var(--c-base);
@c-link-hover: var(--c-primary);
@td-link: none;
@td-link-hover: none;

.link {
    color: @c-link;
    text-decoration: @td-link;
    transition: none;

    &:hover {
        color: @c-link-hover;
        text-decoration: @td-link-hover;
    }


    &__icon {
        &:extend(.d-flex);
        &:extend(.align-items-center);

        [class^='i-'] {

            &:not(:only-child) {
                &:first-child {
                    margin-right: @base;
                }
            }
        }
    }

    &__text {
        white-space: nowrap;
    }
}

// can be used to remove decoration
.link-reset {
    border-bottom: none;
    text-decoration: none;

    &:hover {
        border-bottom: none;
        text-decoration: none;
    }
}

a {
    &:extend(.link);

    &:hover {
        &:extend(.link:hover);
    }
}


.link-arrow {
    font-size: var(--fs-xs);
    font-weight: var(--fw-normal);
    text-transform: uppercase;

    &:before {
        &:extend([class^='i-']:before all);
        &:extend(.i-chevron-right:before all);

        font-size: 0.5rem;
        margin-left: @xs;
        position: relative;
        top: -1px;
    }

    &:hover,
    &:focus {
        background-color: rgba(0, 0, 0, 0.05);
    }
}
