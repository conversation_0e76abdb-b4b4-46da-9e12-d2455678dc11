// ---------------------------------------
// Buttons
// Main Button Classes are .btn-primary and .btn-secondary

// general
@btn-color-disabled: var(--c-gray-lightest);
@btn-font-size: var(--fs-xs);

// primary
@btn-primary-color: var(--c-white);
@btn-primary-color-hover: var(--c-white);
@btn-primary-bg: var(--c-primary);
@btn-primary-bg-hover: var(--c-blue-darkest);
@btn-primary-border: var(--c-primary);
@btn-primary-border-hover: var(--c-base);

// secondary
@btn-secondary-color: var(--c-white);
@btn-secondary-color-hover: var(--c-white);
@btn-secondary-bg: var(--c-secondary);
@btn-secondary-bg-hover: var(--c-base);
@btn-secondary-border: var(--c-secondary);
@btn-secondary-border-hover: var(--c-base);

// secondary
@btn-transparent-color: var(--c-white);
@btn-transparent-color-hover: var(--c-white);
@btn-transparent-bg: transparent;
@btn-transparent-bg-hover: var(--c-base);
@btn-transparent-border: var(--c-white);
@btn-transparent-border-hover: var(--c-base);

.btn {
    background-image: none; // Reset unusual Firefox-on-Android default style; see https://github.com/necolas/normalize.css/issues/214
    border: 1px solid transparent;
    border-radius: 3px;
    cursor: pointer;
    font-size: @btn-font-size;
    font-weight: var(--fw-bold);
    padding: 0.875rem @teaser-md;
    text-align: center;
    text-decoration: none;
    text-transform: uppercase;
    touch-action: manipulation;
    transition: border-color 0.25s ease-in-out, color 0.25s ease-in-out, background-color 0.25s ease-in-out;
    vertical-align: middle;
    z-index: 0 !important;
}

// variants
.btn--primary {
    .button-variant(@btn-primary-color, @btn-primary-color-hover, @btn-primary-bg, @btn-primary-bg-hover, @btn-primary-border, @btn-primary-border-hover);
}

.btn--secondary {
    .button-variant(@btn-secondary-color, @btn-secondary-color-hover, @btn-secondary-bg, @btn-secondary-bg-hover, @btn-secondary-border, @btn-secondary-border-hover);
}

.btn--transparent {
    .button-variant(@btn-transparent-color, @btn-transparent-color-hover, @btn-transparent-bg, @btn-transparent-bg-hover, @btn-transparent-border, @btn-transparent-border-hover);
}

// Specificity overrides
input[type='submit'],
input[type='reset'],
input[type='button'] {
    &.btn--block {
        width: 100%;
    }
}

button.action {
    &:extend(.btn all);
}

// if you want to have basic buttons
// simply extend .btn-primary or .btn-secondary
// for variatns in sizes additionally extend .btn--xl etc.

a.secondary, button.secondary, .btn-secondary {
    &:extend(.btn all);
    &:extend(.btn--secondary all);
}


a.primary, button.primary, .btn-primary {
    &:extend(.btn all);
    &:extend(.btn--primary all);
}

.btn--s {
    padding: @base @teaser-md;
    font-size: var(--fs-xss);
}

// Transparent
.btn-transparent {
    &:extend(.btn all);
    &:extend(.btn--transparent all);
    &:extend(.btn--s all);
}
