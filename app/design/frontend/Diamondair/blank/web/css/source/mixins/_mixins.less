// Button variants
//
// Easily pump out default styles, as well as :hover, :focus, :active,
// and disabled options for all buttons

.button-variant(@color, @color-hover, @background, @background-hover, @border, @border-hover) {
    background-color: @background;
    border-color: @border;
    color: @color;
    position: relative;
    text-transform: uppercase;
    z-index: 1;

    &:focus {
        background-color: @background-hover;
        border-color: @border-hover;
        color: @color-hover;
    }

    &:hover {
        background-color: @background-hover;
        border-color: @border-hover;
        color: @color-hover;
    }

    &:active {
        background-color: @background-hover;
        border-color: @border-hover;
        color: @color-hover;
    }

    &.disabled,
    &[disabled],
    fieldset[disabled] & {
        &:hover,
        &:focus,
        &.focus {
            color: @color-hover;
        }
    }

    .badge {
        background-color: @color;
        color: @background-hover;
    }
}

.container(@width) {
    margin-left: auto;
    margin-right: auto;
    max-width: @width;
    padding-left: @layout-indent__width;
    padding-right: @layout-indent__width;
}

.container-lg {
    .container(@layout__l-max-width);
}

.container-md {
    .container(@layout__m-max-width);
}

.select-arrow {
    position: relative;

    &:after {
        align-items: center;
        color: var(--c-blue-dark);
        content: @icon-up;
        display: flex;
        font-family: @icons-blank-theme;
        font-size: var(--fs-m);
        font-weight: var(--fw-bold);
        height: 55px;
        pointer-events: none;
        position: absolute;
        right: 10px;
        top: 0;
        transform: rotate(180deg);
    }
}

.box-shadow-primary {
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.8);
}

.box-shadow-secondary {
    box-shadow: 2px 2px 3px 1px rgba(0, 0, 0, 0.4);
}

.heading-title-logo {
    background: url(../images/Logos/diamond-mini.svg) top center no-repeat;
    background-size: 50px 40px;
    padding-top: 3.75rem;
}

.slider-border {
    --c-gray-light: rgba(189, 193, 196, 0.7);

    position: relative;

    &:after {
        background: var(--c-gray-light);
        bottom: 75px;
        content: '';
        display: flex;
        justify-content: center;
        position: absolute;
        right: -2px;
        z-index: -1;
        top: 27px;
        width: 1px;
    }
}

.slider-last-border-hide {
    margin-left: -0.313rem !important;
    margin-right: -0.313rem !important;
}

.toggle-arrow {
    position: relative;
    width: 100%;

    &:after {
        color: var(--c-blue-dark);
        content: @icon-up;
        font-family: @icons__font-name;
        font-size: var(--fs-m);
        font-weight: var(--fw-bold);
        position: absolute;
        right: -3px;
        top: -12px;
    }

    &.active {
        &:after {
            content: @icon-down;
        }
    }
}

.button-actions {
    .button-variant(@c-blue-dark, @c-white, @c-white, @c-blue-dark, @c-blue-darkest, @c-blue-darkest);

    border: 1px solid;
    border-radius: 3px;
    display: inline-block;
    font-weight: var(--fw-bold);
    min-height: 54px;
    padding: 0.875rem 2rem !important;
    transition: border-color 0.25s ease-in-out, color 0.25s ease-in-out, background-color 0.25s ease-in-out;
    vertical-align: middle;
}

.action-link {
    color: var(--c-blue-dark);
    font-size: var(--fs-xs);
    font-weight: var(--fw-bold);
    letter-spacing: 1px;
}

.table-responsive {
    border-collapse: collapse;
    margin: 0;
    padding: 0;
    width: 100%;
    table-layout: fixed;
    border: 0;

    caption {
        font-size: var(--fs-base);
        margin: .5rem 0 .75rem;
        @media screen and (max-width: @screen__l) {
            font-size: var(--fs-xs);
        }
    }

    @media screen and (max-width: @screen__l) {
        thead {
            border: none;
            clip: rect(0 0 0 0);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            width: 1px;
        }
    }

    thead {
        border-bottom: 1px solid var(--c-gray-light);
        @media screen and (max-width: @screen__l) {
            border-bottom: 0;
        }
    }

    tr {
        background-color: #f8f8f8;
        border-bottom: 1px solid var(--c-gray-light);
        padding: .35rem;
        @media screen and (max-width: @screen__l) {
            display: block;
            margin-bottom: .625rem;
            border-bottom: 0;
        }

        &:last-child {
            border-bottom: 0;
        }
    }

    td, th {
        padding: .625rem;
    }

    th {
        text-transform: uppercase;
    }

    @media screen and (max-width: @screen__l) {
        td {
            border-bottom: 1px solid var(--c-gray-light);
            display: block;
            font-size: var(--fs-xss);
            text-align: right;

            &:before {
                content: attr(data-th) ': ';
                float: left;
                font-weight: var(--fw-bold);
                text-transform: uppercase;
            }

            &:last-child {
                border-bottom: 0;
            }
        }

        td, th {
            border: none;
        }
    }
}
