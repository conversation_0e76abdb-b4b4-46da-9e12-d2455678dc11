.footer-after {
    --fs-xs: 0.938rem;
    --fs-l: 2.125rem;
    background: var(--c-gray-lightest);

    &__container {
        .container(@screen__xl);
        padding: 1.563rem 1.25rem;
        @media screen and (min-width: @screen__xs) {
            padding: @lg @base;
            display: grid;
            grid-gap: 1rem;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }

        [data-content-type="html"] > div {
            @media screen and (min-width: @screen__xs) {
                padding: 0;
            }

            &:last-child {
                padding-bottom: 0;
            }
        }
    }

    p {
        margin-bottom: @s;
        font-size: var(--fs-xs);
    }

    &__shop-info-address {
        display: grid;
        grid-template-columns: 1fr 11fr;
    }

    &__icon-image {
        display: flex;
        justify-content: center;
        font-size: var(--fs-l);

        .i-marker {
            display: inline-flex;
            margin-left: -0.625rem;
        }
    }

    &__shop-contact {
        @media screen and (min-width: @screen__s) {
            padding-top: 1.875rem;
        }
    }

    &__price-infos {
        padding-top: 2.188rem;
        @media screen and (min-width: @screen__s) {
            padding-top: 1.875rem;
        }
        @media screen and (min-width: @screen__lg) {
            display: flex;
            justify-content: flex-end;
        }
    }

    &__shop-contact {
        padding-left: 1.563rem;
        @media screen and (min-width: @screen__xs) {
            padding-left: 0;
        }
    }

    &__directions {
        padding-left: 1.563rem;
        @media screen and (min-width: @screen__xs) {
            padding-left: 0;
        }
    }
}
