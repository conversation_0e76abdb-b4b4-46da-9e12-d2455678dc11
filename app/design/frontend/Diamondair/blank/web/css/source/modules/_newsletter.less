.newsletter {

    &__label {
        width: 100%;
    }

    input {
        font-size: var(--fs-xs);
        font-weight: var(--fw-bold);
        color: var(--c-gray);
    }

    input::placeholder { /* Firefox, Chrome, Opera */
        font-weight: var(--fw-bold);
        font-size: var(--fs-xs);
        color: var(--c-white) !important;
        opacity: 1;

        .newsletter-banner & {
            color: var(--c-gray) !important;
        }
    }

    input::-moz-placeholder { /* Firefox, Chrome, Opera */
        font-weight: var(--fw-bold);
        font-size: var(--fs-xs);
        color: var(--c-white) !important;
        opacity: 1;

        .newsletter-banner & {
            color: var(--c-gray) !important;
        }
    }

    input:-moz-placeholder { /* Firefox, Chrome, Opera */
        font-weight: var(--fw-bold);
        font-size: var(--fs-xs);
        color: var(--c-white) !important;
        opacity: 1;

        .newsletter-banner & {
            color: var(--c-gray) !important;
        }
    }

    input:-ms-input-placeholder { /* Internet Explorer 10-11 */
        font-weight: var(--fw-bold);
        font-size: var(--fs-xs);
        color: var(--c-white) !important;
        opacity: 1;

        .newsletter-banner & {
            color: var(--c-gray) !important;
        }
    }

    input::-ms-input-placeholder { /* Microsoft Edge */
        font-weight: var(--fw-bold);
        font-size: var(--fs-xs);
        color: var(--c-white) !important;
        opacity: 1;

        .newsletter-banner & {
            color: var(--c-gray) !important;
        }
    }

    &__actions {
        width: 100%;
    }

    &__input {
        border: 0;
        border-radius: 0;
        background: rgba(221, 223, 225, 0.35); // #DDDFE1 with opacity 0.35
        color: var(--c-gray);
    }

    &__actions-subscribe.primary.action {
        background: transparent;
        border-color: #fff;
        border-radius: 5px;
        padding: 10px;
        width: 100%;
        font-size: var(--fs-xss);

        &:hover {
            background: var(--c-base);
            border: var(--c-base);
        }
    }

    #newsletter-error {
        font-weight: var(--fw-bold);
        display: table-row;
    }
}
