.breadcrumbs {
    .container(@screen__xl);
    min-height: 23px;
    position: relative;
    z-index: 2;

    .items {
        display: flex;
        flex-wrap: wrap;
        margin: 0.313rem 0;
        padding-left: 0;

        .item {
            display: flex;
            font-size: var(--fs-xs);

            > a {
                color: var(--c-blue-dark);
            }

            &:after {
                content: '/';
                margin-left: 0.25rem;
            }

            &:after {
                margin: 0 0.438rem;
            }

            &:last-child {
                margin-right: 0;

                &:hover {
                    background: none;
                }

                &:after {
                    content: '';
                }
            }

            strong {
                font-weight: var(--fw-normal);
                color: var(--c-base);
            }
        }
    }
}

