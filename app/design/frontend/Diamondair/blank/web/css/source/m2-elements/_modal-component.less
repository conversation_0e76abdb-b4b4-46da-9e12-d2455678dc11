.modal-popup.confirm {
    .modal-content {
        @media screen and (max-width: @screen__s) {
            padding-right: @base !important;
            padding-left: @base !important;
        }
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        flex-wrap: wrap;
        @media screen and (max-width: @screen__s) {
            padding-right: @base !important;
            padding-left: @base !important;
        }

        .action-dismiss {
            @media screen and (max-width: 460px) {
                margin-bottom: @base;
            }
        }

        .action-accept {
            margin-left: @base;
        }
    }
}


.uc-embedding-container{
    z-index: 1;
}

.uc-embedding-container .uc-embedding-wrapper > span{
    font-size:10.5px;
}

.uc-embedding-container .uc-embedding-wrapper .uc-embedding-accept{
    background: #6192b5;
    color: #fafafa;
}

