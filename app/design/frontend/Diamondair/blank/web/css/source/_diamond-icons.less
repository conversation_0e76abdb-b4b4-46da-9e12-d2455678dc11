@font-face {
    font-family: 'diamond-icons';
    font-style: normal;
    font-weight: normal;
    src:
        url('../fonts/diamond-icons/diamond-icons.woff2') format('woff2'),
        url('../fonts/diamond-icons/diamond-icons.woff') format('woff');
}

[class^='i-'], [class*=' i-'] {
    &:before {
        display: inline-block;
        font-family: 'diamond-icons';
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-style: normal;
        font-variant: normal;
        font-weight: normal;
        line-height: 1em;
        margin-left: 0;
        margin-right: 0;
        speak: none;
        text-align: center;
        text-decoration: inherit;
        text-transform: none;
        width: auto;
    }
}


.i-burger_close {
    &:before {
        content: '\e800';
    }
}
.i-burger {
    &:before {
        content: '\e801';
    }
}
.i-cart {
    &:before {
        content: '\e802';
    }
}
.i-check {
    &:before {
        content: '\e803';
    }
}
.i-chevron-cirlce {
    &:before {
        content: '\e804';
    }
}
.i-chevron-down {
    &:before {
        content: '\e805';
    }
}
.i-chevron-left {
    &:before {
        content: '\e806';
    }
}
.i-chevron-right {
    &:before {
        content: '\e807';
    }
}
.i-chevron-up {
    &:before {
        content: '\e808';
    }
}
.i-dot {
    &:before {
        content: '\e809';
    }
}
.i-edit {
    &:before {
        content: '\e80a';
    }
}
.i-facebook {
    &:before {
        content: '\e80b';
    }
}
.i-favourite {
    &:before {
        content: '\e80c';
    }
}
.i-help {
    &:before {
        content: '\e80d';
    }
}
.i-instagram {
    &:before {
        content: '\e80e';
    }
}
.i-login {
    &:before {
        content: '\e80f';
    }
}
.i-marker {
    &:before {
        content: '\e810';
    }
}
.i-search {
    &:before {
        content: '\e811';
    }
}
.i-top {
    &:before {
        content: '\e812';
    }
}
.i-trash {
    &:before {
        content: '\e813';
    }
}
.i-video {
    &:before {
        content: '\e814';
    }
}
.i-youtube {
    &:before {
        content: '\e815';
    }
}
.i-twitter {
    &:before {
        content: '\f099';
    }
}
.i-circle-thin {
    &:before {
        content: '\f1db';
    }
}
