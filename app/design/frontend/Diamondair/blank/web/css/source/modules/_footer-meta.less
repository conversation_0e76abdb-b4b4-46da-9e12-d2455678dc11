.meta-footer {
    --fs-xs: 0.938rem;
    padding: @padding-primary-mobile;

    &__container {
        .container(@screen__xl);
        @media screen and (min-width: @screen__m) {
            display: flex;
            justify-content: space-between;
        }
    }

    &__shop-name {
        display: flex;
        align-items: center;
        padding: @padding-primary-mobile;
        @media screen and (min-width: @screen__m) {
            padding: 0;
        }

        a {
            display: flex;
            align-items: center;
        }
    }

    &__corporate-website {
        font-size: var(--fs-xs);
        padding-left: @s;
    }

    &__copyright-subnav-container {
        display: block;
        @media screen and (min-width: @screen__lg) {
            display: flex;
            align-items: baseline;
        }
    }

    &__links {
        display: block;
        margin: 0;
        @media screen and (min-width: @screen__m) {
            display: inline-flex;
        }
    }

    &__link {

        a {
            font-size: var(--fs-xs);
        }

        &:after {
            @media screen and (min-width: @screen__m) {
                content: "|";
                padding: 0 @xs;
            }
            @media screen and (min-width: @screen__lg) {
                padding: 0 @s;
            }
        }
    }

    &__copyright-text {
        font-size: var(--fs-xs);
        padding-top: 1.5rem;
        @media screen and (min-width: @screen__s) {
            padding-top: 0;
        }
    }
}
