.layered-filter {

    @media screen and (min-width: @screen__m) {
        padding-top: 165px;
    }

    .i-sr-icon-filter {
        bottom: -1px;
        position: relative;
    }


    &__toggle {
        align-items: center;
        background-color: var(--c-white);
        border: 0;
        border: 1px solid @c-primary;
        cursor: pointer;
        display: flex;
        font-size: var(--fs-base);
        justify-content: flex-start;
        margin-bottom: @md;
        padding: @sm 0 @sm @sm;
        position: relative;
        text-align: left;
        width: calc(~'50% - 10px');

        &:focus {
            background-color: var(--c-primary);
            color: var(--c-white);
            outline: 1px solid rgba(0, 0, 0, 0);
        }

        @media screen and (min-width: @screen__m) {
            display: none;
        }
    }

    &__close {
        background-color: transparent;
        border: 0;
        cursor: pointer;
        line-height: var(--lh-s);
        padding: @s;
        position: absolute;
        right: 0;
        text-align: right;
        top: 0;
        z-index: 21;
        width: 34px;
        height: 34px;

        @media screen and (min-width: @screen__m) {
            display: none;
        }
    }

    &__dropdown {
        position: relative;


        @media screen and (max-width: @screen__mmax) {
            background-color: var(--c-white);
            bottom: 0;
            left: 0;
            overflow: hidden;
            padding-top: 40px;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 20;

            &:not(.is--active) {
                display: none;
            }
        }


        @media screen and (min-width: @screen__xl) {
            padding-right: 2rem;
        }
    }

    &__content {
        padding-bottom: 0.375rem;


        @media screen and (max-width: @screen__mmax) {
            height: 100%;
            overflow: auto;
            padding-left: @base;
            padding-right: @base;
        }
        @media screen and (min-width: @screen__m) {

        }
    }

    &__subtitle {

        @media screen and (max-width: @screen__mmax) {
            border-bottom: 1px solid var(--c-base);
            display: block;
            margin-bottom: @s;
            padding-bottom: @s;
        }

        @media screen and (min-width: @screen__m) {
            border-bottom: 1px solid var(--c-gray-light);
            display: block;
            margin-bottom: @md;
            padding-bottom: @sm;
        }
    }

    &__button {

        &-link {
            align-items: center;
            background: transparent;
            border: 0;
            border-bottom: 1px solid var(--c-gray-light);
            cursor: pointer;
            display: flex;
            padding: @base @lg @base 0;
            position: relative;
            text-align: left;
            width: 100%;

            &.is--active {
                border-color: transparent;

                .layered-filter__button-icon {
                    transform: rotate(90deg);
                }
            }

            &:focus {
                color: var(--c-primary);
                outline: 1px solid rgba(0, 0, 0, 0);
            }
        }
        &-icon {
            color: var(--c-primary);
            font-size: var(--fs-base);
            position: absolute;
            right: 0;
            top: 0.825rem;
            transform: rotate(-90deg);
            transition: transform 0.2s ease-in;
        }
    }

    &__options {
        background-color: white;
        transition: height 0.25s ease;

        .swatch-layered {
            padding: @base 0 @base 0;
        }

        &.is--collapsing {
            height: 0;
            overflow: hidden;
            position: relative;
            transition: height 0.25s ease;
        }

        .field {
            margin: 0;
        }
        .item {

            a {
                align-items: center;
                display: flex;
                font-size: var(--fs-xs);
                padding: 0.1rem 0;
            }
        }

        .items {
            margin-bottom: @xs;
        }

        label {
            padding-left: @sm;

            &::before {
                background-color: @checkbox__background;
                border: @checkbox__border;
                border-radius: 0;
                content: '';
                display: block;
                height: 6px;
                left: 0;
                position: absolute;
                top: 7px;
                user-select: none;
                width: 6px;
            }

            &::after {
                color: @checkbox__color;
                height: 6px;
                left: 0;
                position: absolute;
                text-align: center;
                top: 7px;
                width: 6px;
            }
        }

        + label {
            padding-left: @sm;
        }

        label {
            font-size: var(--fs-xs);
            font-weight: var(--fw-semibold);
        }

        span.count {
            color: var(--c-category-light);
            font-size: var(--fs-xss);
            padding-left: @xs;
            white-space: nowrap;

            &:before {
                content: '(';
                display: inline-flex;
            }

            &:after {
                content: ')';
                display: inline-flex;
            }
        }

        span.filter-count-label {
            display: none;
        }

        &:not(.is--active) {
            display: none;
        }

        &-item {
            &:last-child {
                .layered-filter__button-link {
                    border-color: transparent;
                }
            }
        }
    }

    &__items {
        margin-bottom: 0;
        margin-left: 0;
        margin-top: 0;
        padding-left: 0;
    }

    &__item {
        border-bottom: 1px solid var(--c-gray-light);
        display: flex;
        flex-wrap: wrap;
        font-size: var(--fs-xs);
        margin-left: @lg;
        padding: @base @lg @base 0;

        &:last-child {
            border-color: transparent;
        }
    }

    &__count {
        font-size: var(--fs-xss);
        white-space: nowrap;

        &:before,
        &:after {
            display: inline-block;
        }

        &:before {
            content: '(';
        }

        &:after {
            content: ')';
        }
    }

    &__label {
        word-break: break-word;
    }

    &__item {

    }
}


.filter-options {
    font-size: var(--fs-xs);
}

.filter-current {
    padding-bottom: @xl;
    position: relative;

    @media screen and (min-width: @screen__m) {

    }

    @media screen and (min-width: @screen__xl) {
        padding-right: 2rem;
    }

    &__actions {
        padding-bottom: 0;
    }

    &__link-icon {
        color: var(--c-primary);
        position: absolute;
        right: 0;
        transform: rotate(-90deg);
    }

    &__clear {
        &:extend(.btn all);
        &:extend(.btn--primary all);

        display: block;
        font-size: var(--fs-xs);
        line-height: 1.25;
        margin-bottom: 0.5rem;
        margin-right: 0.5rem;
        padding: 0.125rem 0.125rem 0 0.25rem;


        @media screen and (max-width: @screen__mmax) {
            padding: @xs @base;
            text-align: center;
        }
    }

    &__value {
        font-size: var(--fs-xs);
    }

    &__label {
        display: none;

        &:after {
            content: ': ';
            display: inline-block;
            margin-right: 0.2rem;
        }
    }

    &__subtitle {
        border-bottom: 1px solid var(--c-gray-light);
        display: block;
        font-weight: var(--fw-bold);
        margin-bottom: @md;
        padding-bottom: @sm;

        &:before {
            font-size: var(--fs-xs);
            order: 1;
            position: absolute;
            right: 0.5rem;
            top: 1rem;
        }

        &.is--active {
            border-top: 0;

            .filter-current__link-icon {
                transform: rotate(90deg);
            }
        }

        &:after {
            color: @c-gray;
            content: '(' attr(data-count) ')';
            display: inline-block;
            font-size: @font-size__xs;
            margin-left: 0.3rem;
        }
    }

    &__remove,
    &__previous {

        span {
            display: inline-block;
            font-size: 0.75rem;
            margin-left: 0.5rem;
            margin-right: 0.15rem;
            padding: 0;
            position: relative;
            top: -1px;
            z-index: inherit;
        }
    }
    &__item {
        margin-bottom: 0.5rem;
        margin-left: 0;
        margin-right: 0;
        padding: 0.25rem;
    }
    &__items {
        border-bottom: 0;
        display: flex;
        flex-direction: column;
        padding: 0;
    }

    &__dropdown {

        @media screen and (max-width: @screen__mmax) {
            transition: height 0.25s ease;


            &:not(.is--active) {
                display: none;
            }

            &.is--active {
            }

            &.is--collapsing {
                height: 0;
                overflow: hidden;
                position: relative;
                transition: height 0.25s ease;
            }
        }
    }
}

@media screen and (max-width: @screen__mmax) {
    .is--overlay {
        overflow: hidden;
    }
}

