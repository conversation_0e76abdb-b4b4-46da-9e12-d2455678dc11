// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Email extend styles
//  _____________________________________________

//  This file should be used for theme-specific styles for emails that extend or modify the styles in _email.less
//  This will allow you to edit email styles without copying and editing the _email.less file

//  Importing fonts from an external CSS file, rather than embedding @font-face declarations inside the <style> tag,
//  as the latter will cause font rendering issues if the web fonts are inaccessible.

@import url("@{baseUrl}css/email-fonts.css");

.header {
    background-color: @email__background-color;
    padding: @email-body__padding;
}

.footer {
    background-color: @email__background-color;
    padding: @email-body__padding;

    table {
        width: 100%;

        td {
            padding-bottom: @email-body__padding;
            width: 33%;

            p {
                margin-bottom: 0;

                &.phone {
                    font-size: @font-size__l;

                    a {
                        color: inherit;
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__xs) {
    .header,
    .main-content,
    .footer {
        padding: @email-body__padding @indent__s !important;
    }

    .footer {
        td {
            display: block;
            width: auto !important;
        }
    }
}

.header, .main-content, .footer{
    h1 {
        font-size: 16px; /* mso outlook fallback */
        font-size: 1rem; /* rem not supported by mso outlook */
    }
}


.email-summary{
    h1 {
        margin-top: @indent__m;
    }
}

.order-details{
    h3 {
        font-size: 16px; /* mso outlook fallback */
        font-size: 1rem; /* rem not supported by mso outlook */
    }
}

.email-information{
    .method-info{
        dl,dt{
            margin-top: 0px;
            margin-bottom: 0px;
        }
        dd{
            margin-top: 10px;
            margin-left: 0px;
        }
    }
    .item-info{
        p{
            margin-top: 0px;
        }
    }
}


