body.account {
    .account-nav {
        position: relative;
        border: 1px solid #E1E0E0;
        background-color: var(--c-white);
        box-shadow: 1px 7px 10px 0 rgba(0, 0, 0, 0.09);
        margin-bottom: @md;
        @media screen and (min-width: @screen__m) {
            margin-bottom: 0;
        }

        .items {
            padding-top: @md;
        }

        .item {
            > strong, > a {
                padding: @s @md;
                font-size: var(--fs-xs);
            }

            > a {
                &:extend(.link-primary all);
            }

            &.current {
                > strong, > a {
                    padding: @s 0.938rem;
                }

                border-left: 4px solid var(--c-blue-dark);
            }
        }

        &__toggle {
            background-color: transparent;
            border: 0;
            border-bottom: 1px solid var(--c-gray-lightest);
            cursor: pointer;
            display: block;
            font-weight: var(--fw-normal);
            font-size: var(--fs-xs);
            padding: @s @md;
            position: relative;
            text-align: left;
            width: 100%;

            &:before {
                &:extend([class^='i-']:before);
                &:extend(.i-burger:before);
                order: 1;
                position: absolute;
                right: 5px;
                top: 10px;
            }

            &.is--active {
                &:after {
                    display: none;
                }
            }

            @media screen and (min-width: @screen__m) {
                display: none;
            }
        }

        &__close {
            border: 0;
            cursor: pointer;
            padding-right: 0.313rem;
            position: absolute;
            right: 0;
            top: 0;
            width: 100%;
            z-index: 1;
            background: var(--c-white);
            text-align: right;
            height: 40px;

            @media screen and (min-width: @screen__m) {
                display: none;
            }
        }
    }

    .nav.items {

        @media screen and (max-width: @screen__mmax) {

            &:not(.is--active) {
                display: none;
            }
        }
        @media screen and (max-width: @screen__l) {
            padding: 0;
        }
    }
}
