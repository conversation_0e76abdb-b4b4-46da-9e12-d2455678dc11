.logo-mobile {
    &__svg {
        display: inline-block;
        height: 30px;
        width: 39px;
        @media screen and (min-width: @screen__m) {
            display: none;
        }
    }

    &__cls-icon {
        fill: var(--c-primary);

        .header-transparent & {
            fill: var(--c-white);
        }
    }
}

.logo-main {
    flex-shrink: 0;
    grid-area: logo;
    margin-right: auto;
    overflow: hidden;
    padding-top: @s;
    position: relative;
    z-index: 1;


    &__link {
        display: inline-block;
    }

    &__svg {
        display: none;
        height: 30px;
        width: 149px;

        @media screen and (min-width: @screen__l) {
            height: 42px;
            width: 205px;
        }

        @media screen and (min-width: @screen__m) {
            display: inline-block;
        }
    }

    &__cls-3 {
        fill: var(--c-base);

        .header-transparent & {
            fill: var(--c-white);
        }
    }

    &__cls-icon {
        fill: var(--c-primary);

        .header-transparent & {
            fill: var(--c-white);
        }
    }

    @media screen and (min-width: @screen__l) {
        padding-top: 0;
    }
}

@-moz-document url-prefix() {
    .logo-main {
        @media screen and (max-width: @screen__m) {
            order: 1;
        }
    }
}

