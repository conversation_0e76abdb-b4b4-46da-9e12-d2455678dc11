// Fotorama Rewrites
.fotorama {
    text-align: center;

    &__caption {
        display: none;
    }

    &__nav {
        &--thumbs {
            @media screen and (min-width: @screen__m) {
                left: -50px;
                top: 5px;
            }
        }
    }

    &__thumb {
        border-radius: 50%;

        &:hover {
            border: 2px solid @c-blue-dark;
        }
    }

    &__nav__frame--thumb.fotorama__active {

        img {
            left: 50%;
            top: 0 !important;
            transform: translate(-50%, 0) scale(0.7) !important;
            transition: all 0.1s;
            width: auto !important;
        }
    }

    &__thumb-border {
        border: none !important;
        border-radius: 50%;
        background: none !important;
        transform: initial !important;
        transition: initial !important;
        z-index: initial !important;
    }

    &__active {
        .fotorama__thumb {
            border: 2px solid @c-blue-dark;
        }

        .fotorama__dot {
            background-color: var(--c-blue-dark) !important;
            border-color: var(--c-blue-dark) !important;
        }
    }

    &__nav {
        &--dots {
            .fotorama__nav__frame {
                height: 30px;
                width: 17px;
                padding: 0 0.938rem;
            }
        }
    }

    &__dot {
        background-color: var(--c-blue-light) !important;
        border-color: var(--c-blue-light) !important;
    }

    &__stage {
        max-height: 580px !important;
    }

    &__fullscreen {
        .fotorama__stage {
            max-height: initial !important;
        }
    }
}
