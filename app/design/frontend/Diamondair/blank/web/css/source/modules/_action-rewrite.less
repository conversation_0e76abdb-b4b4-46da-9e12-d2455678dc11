.action-edit {
    background: none !important;
    border: none !important;
    cursor: pointer !important;
    outline: inherit !important;

    &:before {
        content: @i-edit !important;
        font-family: @icons__font-name-custom !important;
        color: var(--c-blue-dark) !important;
        font-size: var(--fs-s) !important;
        line-height: 1.563rem;
    }

    &:hover {
        &:before {
            color: var(--c-blue-light) !important;
        }
    }
}

.action-toggle {
    &:after {
        color: var(--c-blue-dark) !important;
        font-size: var(--fs-m) !important;
        font-weight: var(--fw-bold) !important;
    }

    &:hover {
        &:after {
            color: var(--c-blue-light) !important;
        }
    }
}

.action-delete {
    background: none !important;
    border: none !important;
    cursor: pointer !important;
    outline: inherit !important;

    &:before {
        color: var(--c-blue-dark) !important;
        font-size: var(--fs-s) !important;
        line-height: 1.563rem !important;
    }

    &:hover {
        &:before {
            color: var(--c-blue-light) !important;
        }
    }
}

.field-tooltip-action {
    &:before {
        content: @i-help !important;
        font-family: @icons__font-name-custom !important;
        color: var(--c-blue-dark) !important;
    }

    &:hover {
        &:before {
            color: var(--c-blue-light) !important;
        }
    }
}

.cart-summary .block > .title, .paypal-review-discount .block > .title {
    &:after {
        color: var(--c-blue-dark) !important;
        font-size: var(--fs-m) !important;
        font-weight: var(--fw-bold) !important;
    }
}

.action.action-towishlist {
    background: none !important;
    color: var(--c-primary) !important;
    border: none !important;
    display: block;

    span {
        .sr--only();
    }

    svg {
        display: none;
    }

    &:before {
        color: inherit;
        content: @icon-wishlist-full;
        display: inline-block;
        font-family: @icons__font-name;
        font-size: 3.5rem;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        font-weight: normal;
        line-height: 1.563rem;
        overflow: hidden;
        speak: none;
        text-align: center;
        vertical-align: top;
    }

    &:after {
        background: none;
        position: static;
    }

    &:hover, &:focus {
        bottom: 0;
        left: 0;
        box-shadow: none !important;

        &:before {
            color: var(--c-blue-light);
        }
    }
}

.action.update {
    body:not(.multishipping-checkout-addresses) & {
        border: none;
        background: none;

        &:before {
            content: @icon-update;
            display: inline-block;
            font-family: @icons__font-name;
            font-size: 3.5rem;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-weight: normal;
            line-height: 2.188rem;
            overflow: hidden;
            speak: none;
            text-align: center;
            vertical-align: top;
            color: var(--c-blue-dark);
        }

        &:after {
            background: none;
            position: static;
        }

        span {
            &:extend(.sr-only all);
        }

        &:hover, &:focus {
            bottom: 0;
            left: 0;

            &:before {
                color: var(--c-blue-light);
            }
        }

        @media screen and (max-width: @screen__m) {
            padding: 0 !important;
            margin: 0 !important;
        }
    }

    .multishipping-checkout-addresses & {
        &:extend(.button-actions all);
    }
}

.action.action-delete {
    background: none;
    @media screen and (max-width: @screen__s) {
        border: none !important;
        background: none !important;
        box-shadow: none !important;
        padding: 0 !important;
        margin: 0;
        span {
            .sr--only();
        }

        &:before {
            color: inherit;
            font-size: 1.5rem;
            overflow: hidden;
            speak: none;
            text-align: center;
            vertical-align: top;
            color: var(--c-blue-dark);
        }

        &:after {
            background: none;
            position: static;
        }

        &:hover, &:focus {
            bottom: 0;
            left: 0;

            &:before {
                color: var(--c-blue-light);
            }
        }
    }
}

.action.remind {
    &:extend(.button-actions all);
    margin-top: @s;
    @media screen and (min-width: @screen__xs) {
        margin-top: 0;
        margin-left: @base;
    }
}

.action.add {
    &:extend(.button-actions all);
}

.action-accept {
    &:extend(.btn all);
    background: var(--c-blue-dark);
    color: var(--c-white);
}

.action-dismiss {
    &:extend(.btn all);
    &:extend(.btn--secondary all);
    background: var(--c-white);
    color: var(--c-blue-dark);
    border-color: var(--c-blue-dark);
}

.multicheckout .actions-toolbar > .secondary .action {
    margin-bottom: 0;
}

.action.clear {
    border: none;
    background: none;

    &:before {
        font-size: 1.5rem;
        overflow: hidden;
        speak: none;
        text-align: center;
        vertical-align: top;
        color: var(--c-blue-dark);
    }

    span {
        &:extend(.sr-only all);
    }

    &:after {
        background: none;
        position: static;
    }

    &:hover, &:focus {
        bottom: 0;
        left: 0;

        &:before {
            color: var(--c-blue-light);
        }
    }

    @media screen and (max-width: @screen__m) {
        padding: 0 !important;
        margin: 0 !important;
    }
}

.action.continue {
    @media screen and (max-width: @screen__s) {
        width: 100%;
        margin-right: 0.625rem !important;
        font-size: var(--fs-xs) !important;
    }
    @media screen and (max-width: @screen__m) {
        margin-bottom: 0 !important;
    }
}

.actions-toolbar {
    display: flex;
    margin: 1rem 0;

    .multishipping-checkout_address-newshipping & {
        justify-content: space-between;
        align-items: baseline;
    }

    .multishipping-checkout-addresses & {
        display: block;
    }

    @media screen and (min-width: @screen__m) {
        height: 73px;
    }
    @media screen and (max-width: @screen__xs) {
        align-items: center;
        display: block;
        .primary {
            margin-bottom: @s;
        }
    }
}

.secondary {
    .action.back {
        &:extend(.button-actions all);
        margin-top: @s;
        margin-right: @base;
        @media screen and (min-width: @screen__xs) {
            margin-top: 0;
        }
    }
}

.action.reload {
    &:extend(.button-actions all);
    margin-top: @s;
    margin-right: @base;
    @media screen and (min-width: @screen__xs) {
        margin-top: 0;
    }
}

.action.login {
    padding: 0.875rem 2rem !important;
}

.secondary {
    .action {
        padding: 0.5rem;
        &:extend(.link-primary all);
    }
}

.action.print {
    padding: 0.5rem;
    &:extend(.link-primary all);
}

.actions-toolbar-account {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    @media screen and (min-width: @screen__s) {
        flex-direction: row;
    }

    button {
        @media screen and (max-width: @screen__s) {
            margin-bottom: 0.5rem;
        }
    }
}

.block-customer-login, .form-create-account, .password-forget {

    &__actions-toolbar {
        &:extend(.actions-toolbar-account all);
    }
}
