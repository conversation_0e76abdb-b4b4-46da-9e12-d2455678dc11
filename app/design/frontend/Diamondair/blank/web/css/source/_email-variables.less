// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Email variable overrides
//  _____________________________________________

//  This file should be used to set theme-specific variables for emails.
//  By default, emails will inherit variables from the Magento UI Library and the _theme.less and _variables.less files
//  in a custom theme. Use this file if you want to set specific variables for emails.
//  For example, you can make the email font, typography, colors, etc unique from the frontend theme.

//  Structure
@email-body__width: 660px;
@email-body__padding: @indent__m;

//  Colors
@c-gray-lightest: #ecedee;
@c-primary: #00467e;
@c-blue-darkest: #023157;

@email__background-color: @c-gray-lightest;
@color-gray95: @c-gray-lightest;
@link__color: @c-primary;
@link__visited__color: @c-primary;
@link__hover__color: @c-primary;
@link__active__color: @c-primary;
@button-primary__background: @c-primary;
@button-primary__hover__background: @c-blue-darkest;
@button-primary__active__background: @c-blue-darkest;

//  Headings
@h3__font-weight: @heading__font-weight__base;
