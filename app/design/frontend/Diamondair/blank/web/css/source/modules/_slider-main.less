.slider-main {
    position: relative;
    z-index: 0;

    [data-action] {
        display: none;
    }

    .tns-nav {
        bottom: 15px;
        left: 50%;
        position: absolute;
        transform: translate(-50%, 0);
        z-index: 11;

        @media screen and (min-width: @screen__l) {
            bottom: 35px;
        }

        button {
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: 10px;
            height: 20px;
            margin: 0 @s;
            padding: 0;
            position: relative;
            width: 20px;

            &:before {
                background-color: var(--c-white);
                border-radius: 10px;
                content: '';
                display: inline-block;
                height: 10px;
                left: 50%;
                position: absolute;
                top: 50%;
                transform: translate(-50%, -50%);
                width: 10px;
            }

            &.tns-nav-active {
                background-color: var(--c-white);
                border: 1px solid var(--c-primary);
                &:before {
                    background-color: var(--c-primary);
                }
            }
        }
    }

    &:before {
        background: url(../images/media_background.png) right top no-repeat;
        background-size: contain;
        content: '';
        display: block;
        height: 100%;
        opacity: 0.7;
        pointer-events: none;
        position: absolute;
        right: 0;
        top: 0;
        width: 75%;
        z-index: 1;
    }

    &__description {
        .container(@layout__m-max-width);

        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        z-index: 1;
    }

    &__content {
        align-items: center;
        display: flex;
        justify-content: center;
        min-height: 375px;
        padding-bottom: 75px;
        padding-top: 75px;
        position: relative;
        width: 100%;


        &:after {
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0) 56%, rgba(0, 0, 0, 0) 100%);
            content: '';
            display: block;
            height: 100%;
            left: 0;
            opacity: 0.7;
            pointer-events: none;
            position: absolute;
            top: 0;
            width: 100%;
            z-index: 0;
        }

        @media screen and (min-width: @screen__l) {
            min-height: 554px;
            padding-bottom: 0;
            padding-top: 150px;
        }

        @media screen and (min-width: @screen__xl) {
            min-height: 764px;
            &:after {
                bottom: 0;
            }
        }
    }

    &__header {
        color: var(--c-white);
        line-height: 1.27;
        margin-bottom: @md;
        text-align: center;
        text-shadow: 0 2px 0 rgba(0, 0, 0, 0.26);
        @media screen and (min-width: @screen__xl) {
            font-size: 3.8125rem;
            margin-bottom: @xl;
        }
    }

    &__image {
        display: block;
        height: 100%;
        object-fit: cover;
        object-position: center center;
        position: absolute;
        top: 0;
        width: 100%;
    }

    &__button {
        letter-spacing: 1px;
        padding: @base @teaser-md;
    }
}

.slider-list:not(.tns-slider) {
    .slider-main__item:nth-child(n+2) {
        display: none;
    }
}
