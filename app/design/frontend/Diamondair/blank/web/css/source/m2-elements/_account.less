body.account {

    .page-main {
        padding-top: 0;
        .container(@layout__l-max-width);
    }

    .page-title span {
        @media screen and (max-width: @screen__mmax) {
            font-size: var(--fs-xs);
        }
    }

    .block-title {
        margin-bottom: 1.563rem;

        strong {
            --fs-s: 1.438rem;

            font-size: var(--fs-s);
            font-weight: var(--fw-normal);
        }
    }

    .box-title {
        margin: 0 0 0.625rem !important;
    }

    a.action {
        &:extend(.action-link);
    }

    .block-dashboard-info, .block-dashboard-addresses, .block-reviews-dashboard, .block-addresses-default {
        margin-bottom: 0;
        @media screen and (min-width: @screen__m) {
            margin-bottom: 2.188rem;
        }
    }

    .block-reviews-dashboard {
        li {
            margin-bottom: @base;

            a {
                color: var(--c-blue-dark);
            }
        }
    }

    .box-tocart {
        @media screen and (max-width: @screen__s) {
            display: flex;
            flex-wrap: wrap;
        }
    }

    .action.order, .action.print {
        display: inline;
        float: none;
        margin-top: 0.5rem;
        padding: 0;
        @media screen and (max-width: @screen__s) {
            margin-bottom: 1rem;
        }
    }

    .action.update {
        &:before {
            font-size: 4rem;
            top: 25%;
        }
    }

    .account .nav {
        margin-top: 0;
        padding-left: 0;
    }

    .action.tooltip {
        display: none;
    }

    @media screen and (min-width: @screen__m) {
        .columns {
            grid-template-columns: minmax(330px, 3fr) minmax(300px, 9fr) !important;
        }
    }

    &.customer-account-index .column.main {
        @media screen and (min-width: @screen__m) {
            width: 100%;
        }
    }

    .sidebar-main {
        padding-top: 0;
    }

    .actions-toolbar {
        height: auto;
    }

    .block-customer-login {

        &__actions-toolbar {
            &:extend(.actions-toolbar-account all);
        }

        &__remind-button {
            @media screen and (min-width: @screen__s) {
                margin-left: @s;
            }
        }
    }

    .action-link {
        color: @c-base;
        font-weight: @font-weight__bold;

        &:hover {
            color: @c-primary;
        }
    }

    .action.back {
        @media screen and (min-width: @screen__xs) {
            margin-left: 0.313rem;
            margin-right: 0;
        }
    }

    .product__details-wrapper {
        margin-top: 0;
    }

    .table-order-items {
        width: 100%;


        .col.sku, .col.subtotal, .col.price, .col.qty {
            @media screen and (max-width: @screen__mmax) {
                display: flex;
                justify-content: space-between;
                font-size: var(--fs-xss);
            }
        }

        .col.qty {
            .items-qty {
                @media screen and (max-width: @screen__mmax) {
                    font-size: var(--fs-xss);
                }
            }
        }
    }

    .order-details-items.ordered {
        .order-title {
            display: none;
        }
    }

    a.action {
        &:extend(.link-primary all);
    }

    .limiter {
        align-items: center;
        display: flex !important;
    }

    .toolbar .limiter-options {
        width: 60px !important;
    }

    .limiter-wrapper {
        &:extend(.select-arrow all);

        position: relative;

        &:after {
            top: -5px;
        }
    }

    .toolbar.bottom {
        margin-top: @base;
    }

    .order-status {
        &:extend(.sr-only);
    }

    #my-orders-table {
        .col.status {
            &:extend(.sr-only);
        }
    }
}

.customer-account-login {
    .block-new-customer, .block-customer-login {
        .block-title strong {
            font-weight: var(--fw-bold);
        }
    }
}
