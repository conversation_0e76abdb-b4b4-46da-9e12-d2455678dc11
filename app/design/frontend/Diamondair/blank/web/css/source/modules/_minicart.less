.mini-cart {
    padding: @xs @s;

    &.minicart-wrapper {
        height: initial !important;
        margin: 0;
        margin-right: 10px;

        @media screen and (min-width: @screen__m) {
            margin-right: 0;
        }

        .counter.qty {
            --fs-xss: 0.625rem;

            background-color: var(--c-red);
            height: 14px;
            left: -2px;
            position: absolute;
            text-align: center;
            top: -1px;
            width: 19px;

            .counter-number {
                color: var(--c-white);
                display: block;
                display: block;
                font-size: var(--fs-xss);
                font-weight: var(--fw-bold);
                margin-top: 2px;
            }

            &.empty {
                display: none;
            }
        }
    }

    &__toggle {
        background: none;
        border: 0 none;
        cursor: pointer;
        display: block;
        flex-shrink: 0;
        line-height: 1;
        overflow: hidden;
        position: relative;

        &-icon {
            font-size: 1.5rem;

            .header-transparent & {
                color: var(--c-white);
            }

            @media screen and (min-width: @screen__l) {
                font-size: 1.725rem;
            }
        }
    }

    &__container {
        background: var(--c-white);
        box-shadow: 1px 4px 21px 0 rgba(0, 0, 0, 0.13);
        color: var(--c-base);
        display: block;
        font-size: 0.9rem;
        margin: 0;
        min-width: inherit;
        opacity: 0;
        position: static;
        position: absolute;
        right: 0;
        top: 50px;
        transform: translate(0, 30px);
        transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
        width: 265px;
        z-index: 999;

        @media screen and (min-width: @screen__m) {
            max-width: 375px;
            width: 375px;
        }

        &:not(.is--active) {
            display: none;
        }

        &.is--show {
            opacity: 1;
            transform: translate(0, 0);
        }

        &.block-minicart {

            .minicart-wrapper & {
                z-index: 3;
            }

            &:before {
                border: 0.5rem solid black;
                border-color: #fff #fff transparent transparent;
                content: '';
                height: 0;
                position: absolute;
                right: 20px;
                top: 0;
                transform: rotate(-45deg);
                transform-origin: 0 0;
                width: 0;
            }
        }

        .block-content {
            padding: 0.938rem 1.438rem;

            @media screen and (max-width: @screen__xs) {
                padding: @s;
            }
        }
    }

    &__bottom-container-subtotal {
        text-align: right;

        &--label {
            display: block;
            padding-bottom: 0.438rem;
        }
    }

    #btn-minicart-close {
        display: none;
    }

    .product-image-wrapper {
        padding: 0 !important;
    }

    &__actions {
        border-bottom: 1px solid #bdc1c4;
        margin-top: 0.938rem;
        margin-bottom: 0.938rem;
        padding-bottom: 1.313rem;
        width: 100%;

        &--button {
            padding: 0.938rem !important;
            width: 100%;
        }
    }

    &__total-wrapper {
        align-items: baseline;
        display: flex;
        font-size: var(--fs-xs);
        justify-content: space-between;

        .subtotal {
            .price {
                font-weight: var(--fw-bold);
            }
        }

        #top-cart-btn-checkout {
            width: 100%;
        }
    }

    &__list {
        margin: 0;
        padding: 0;
    }

    &__item {
        border-bottom: 1px solid #bdc1c4;
        margin-bottom: @base;
        padding-bottom: 1.5rem;
        padding-top: @base;
    }

    &__product {
        display: grid;
        grid-gap: 0.875rem;
        grid-template-columns: 1fr 3fr;
    }

    &__product {

        &-item-name {
            margin: 0;

            &-url {
                color: var(--c-blue-dark);
                font-size: var(--fs-xs);
                font-weight: var(--fw-bold);
            }
        }
    }

    &__product-item-pricing {
        .minicart-price {
            --fs-xs: 1.125rem;

            font-size: var(--fs-xs);
            font-weight: var(--fw-bold);
        }
    }

    &__details-qty {
        align-items: center;
        display: flex;
        flex-direction: row;
        margin-top: 0.813rem;
        position: relative;

        &-label {
            font-size: var(--fs-xs);
            margin-right: 0.625rem;
        }

        &-item-qty {
            border-color: var(--c-gray-light);
            border-width: 1px;
            color: var(--c-gray);
            font-size: var(--fs-xs);
            height: 43px;
            padding: 0;
            padding-left: 0.75rem;
            text-align: center;
            width: 59px;
        }

        &-update-cart-item {
            --fs-xss: 0.625rem;

            border-style: solid;
            border-width: 1px;
            font-size: var(--fs-xss) !important;
            height: 100%;
            padding: 0.625rem !important;
            position: absolute !important;
            right: -10px;
            text-transform: none;
            top: 0;
            transform: translate(100%, 0);
            padding: 0.625rem !important;
            @media screen and (min-width: @screen__m) {
                font-size: var(--fs-xs) !important;
            }
        }
    }

    &__product-actions {
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        margin-top: 2.563rem;
        width: 67px;
        margin-left: @s;

        &-span {
            display: none;
        }

        &-edit, &-delete {
            border: none !important;
            padding: 0 !important;

            &:before {
                --fs-s: 1.563rem;

                color: var(--c-blue-dark);
                display: inline-block;
                font-size: var(--fs-s);
            }

            &:hover {
                border: none;

                &:before {
                    color: var(--c-blue-light);
                }
            }
        }

        &-edit {
            margin-right: 0.938rem;
        }
    }

    &__product-options {
        font-size: var(--fs-xs);

        &-toggle {
            cursor: pointer;
            display: block;
            margin-top: -0.313rem;
            position: relative;

            &:after {
                color: var(--c-blue-dark);
                content: @icon-down;
                display: inline-block;
                font-family: @icons-blank-theme;
                font-size: var(--fs-m);
                font-weight: var(--fw-bold);
                position: relative;
                right: 5px;
                top: 7px;
            }
        }

        &.active {
            > .toggle {
                &:after {
                    content: @icon-up;
                    display: inline-block;
                }
            }
        }

        .list {
            display: flex;
        }

        .content, .label, .values {
            font-size: var(--fs-xs);
        }
    }

    &__product-action-container {
        align-content: space-between;
        align-items: stretch;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-between;
        width: 100%;
    }


    &__bottom-actions {
        display: flex;
        justify-content: center;

        &--viewcart {
            border: none;
            color: var(--c-blue-dark);
            font-size: var(--fs-xs);
            font-weight: var(--fw-bold);
            position: relative;
            text-align: center;
            width: 100%;

            &:hover {
                color: var(--c-blue-light);
            }
        }
    }
}
