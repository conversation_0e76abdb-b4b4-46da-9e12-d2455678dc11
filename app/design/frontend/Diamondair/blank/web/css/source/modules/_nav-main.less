.nav-main {

    @media screen and (min-width: @screen__m) {
        margin-left: @base;
    }

    &__item {

        &-0 {

            &.is--active-category {
                > a {
                    outline: 1px solid rgba(0, 0, 0, 0);
                    &:after {
                        background-color: var(--c-primary);


                        @media screen and (min-width: @screen__m) {
                            .header-transparent & {
                                background-color: var(--c-white);
                            }

                            bottom: 0.5rem;
                            content: '';
                            display: block;
                            height: 3px;
                            left: @s;
                            position: absolute;
                            right: @s;
                        }
                    }
                }
            }

            @media screen and (min-width: @screen__m) {
                &:last-child {
                    .nav-main__link {
                        margin-right: 0;
                        padding-right: 0;
                        &:after {
                            right: 0;
                        }
                    }
                    .nav-main__list-1 {
                        left: auto;
                        right: 0;
                        transform: translate(0, 0);

                        &:before {
                            left: auto;
                            right: 10%;
                        }
                    }
                }
            }
        }

        &-1 {
            &.is--active-category {
                a > {
                    color: var(--c-primary);
                }
            }
        }
    }

    &-1 {
        margin: @xs 0 @xs 0;
    }

    &__link {
        display: inline-block;
        font-weight: var(--fw-normal);
        padding: @s @s;


        &-mobile {
            display: none;
            @media screen and (max-width: 768px) {
                display: block;
            }
        }

        &-icon {
            display: none;
            @media screen and (max-width: 768px) {
                display: block;
                position: absolute;
                right: 10px;
                top: 15px;
                .nav-main__link-0.is--active & {
                    transform: rotate(90deg);
                }
            }
        }

        &-0 {
            background-color: transparent;
            border: 0;
            cursor: pointer;
            padding: @base @s;
            position: relative;

            @media screen and (max-width: @screen__mmax) {
                margin-bottom: 0;
                text-align: left;
                width: 100%;
                &:after {
                    background-color: var(--c-gray-light);
                    bottom: 0;
                    content: '';
                    display: block;
                    height: 1px;
                    left: @s;
                    position: absolute;
                    right: @s;
                }
            }

            @media screen and (min-width: @screen__m) {
                margin: @xs @s;
                padding: @xs @s @sm;
                .header-transparent & {
                    color: var(--c-white);
                }
            }

            &.is--active,
            &:hover,
            &:focus {
                outline: 1px solid rgba(0, 0, 0, 0);
                &:after {
                    background-color: var(--c-primary);


                    @media screen and (min-width: @screen__m) {
                        .header-transparent & {
                            background-color: var(--c-white);
                        }

                        bottom: 0.5rem;
                        content: '';
                        display: block;
                        height: 3px;
                        left: @s;
                        position: absolute;
                        right: @s;
                    }
                }
            }
        }

        &-1 {
            &.is--active,
            &:hover,
            &:focus {
                color: var(--c-primary);
                outline: 1px solid rgba(0, 0, 0, 0);
            }
            @media screen and (max-width: 768px) {
                position: relative;
            }
        }
    }

    &__list {

        &-0 {
            @media screen and (min-width: @screen__m) {
                display: flex;
                flex-wrap: wrap;
                justify-content: flex-end;
                margin-bottom: 0;
            }
        }

        &-1 {
            display: none;
            margin-bottom: 0;
            @media screen and (min-width: @screen__m) {
                .shadow-xs();

                background-color: var(--c-white);
                left: 50%;
                padding: 1.5rem @md;
                position: absolute;
                top: 100%;
                transform: translate(-50%, 0);
                width: 235px;
                z-index: 11;

                &:before {
                    border: 0.5rem solid black;
                    border-color: #fff #fff transparent transparent;
                    box-shadow: 3px -3px 3px 0 rgba(0, 0, 0, 0.05);
                    content: '';
                    height: 0;
                    left: 40%;
                    position: absolute;
                    top: 0;
                    transform: rotate(-45deg);
                    transform-origin: 0 0;
                    width: 0;
                }
            }

            &.is--active {
                display: block;
            }
        }
    }
}
