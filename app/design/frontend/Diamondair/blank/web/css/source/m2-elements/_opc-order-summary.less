.opc-block-summary {
    background: none;
    padding: 1.563rem 1.25rem;
    @media screen and (min-width: @screen__l) {
        border: 1px solid @c-gray-lightest;
        box-shadow: 1px 7px 10px 0 rgba(0, 0, 0, 0.09);
    }

    .table-caption {
        display: none;
    }

    .title {
        --fs-s: 1.438rem;
        font-size: var(--fs-s);
        border-bottom: 0;
        padding-bottom: 0.313rem;
    }

    .items-in-cart {

        > .title {
            padding: 0 0 0.813rem 0.188rem;
            border-bottom: 0;

            strong {
                font-size: var(--fs-xs);
                font-weight: var(--fw-bold);
                color: var(--c-blue-dark);
            }

            &:after {
                font-weight: var(--fw-bold);
                color: var(--c-blue-dark);
                top: 14px;
                right: -3px;
            }
        }

        &.active {
            .content.minicart-items {
                border-top: 1px solid @c-gray-lightest;
            }
        }
    }

    .minicart-items-wrapper {
        max-height: 100%;
    }

    .minicart-items {

        .product {
            &:not(.options) {
                display: grid;
                grid-template-columns: 3fr 9fr;
                grid-gap: 1.25rem;
            }
        }

        .product-item {
            padding-bottom: 0.938rem;
        }

        .product-item-name-block {
            width: 100%;
            display: block;
            padding: 0;

            strong {
                font-weight: var(--fw-normal);
                font-size: var(--fs-xs);
            }
        }

        .details-qty {
            font-size: var(--fs-xs);
            color: var(--c-gray);
            padding: 0.25rem 0;
        }

        .subtotal {
            display: block;
            width: 100%;
            text-align: left;
        }

        .cart-price .price {
            font-weight: var(--fw-bold) !important;
        }

        .options {
            &:extend(.toggle-arrow all);

            .toggle {
                width: 100%;
                display: block;
                font-size: var(--fs-xs);
                font-weight: var(--fw-bold);
                color: var(--c-blue-dark);
            }

            .subtitle {
                &:extend(.sr-only all);
            }

            .item-options {
                font-size: var(--fs-xs);
                margin-top: 0;
            }
        }
    }

    .table-totals {
        font-size: var(--fs-xs);
        width: 100%;
        padding: 1.25rem 0;
        margin-top: 0.625rem;
        display: block;
        border-top: 1px solid @c-gray-lightest;

        tr {
            margin-bottom: 0.938rem;
            font-size: var(--fs-xs);
            @media screen and (min-width: @screen__l) {
                display: flex;
                width: 290px;
                justify-content: space-between;
            }

            &.grand.totals {
                border-top: 1px solid @c-gray-lightest;

                strong {
                    --fs-xs: 1.125rem;
                    font-size: var(--fs-xs);
                    font-weight: var(--fw-bold);
                }
            }
        }
    }
}

.opc-block-shipping-information {
    margin-top: 2.813rem;

    .shipping-information-title {
        --fs-s: 1.438rem;
        font-size: var(--fs-s);
        border-bottom: 1px solid @c-gray-lightest;
        padding-bottom: 0.625rem;

    }

    .shipping-information-content {
        font-size: var(--fs-xs);
    }
}
