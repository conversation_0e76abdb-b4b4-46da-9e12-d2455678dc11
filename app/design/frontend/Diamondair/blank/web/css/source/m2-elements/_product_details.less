.catalog-product-view {
    .column.main {
        display: grid;
        grid-gap: 1.25rem;
        grid-template-areas: 'media media' 'infoblock infoblock' 'additionalinfo additionalinfo' 'related related' 'upsell upsell';
        grid-template-columns: 7fr 5fr;
        @media screen and (min-width: @screen__m) {
            grid-template-areas: 'media infoblock' 'additionalinfo additionalinfo' 'related related' 'upsell upsell';
        }

        .page-title span {
            font-size: var(--fs-m);
        }
    }

    .product {
        &__info-main {
            grid-area: infoblock;
            @media screen and (min-width: @screen__m) {
                padding-top: 2.5rem;
                margin-bottom: 2.5rem;
            }

            .page-title {
                margin-bottom: 0.563rem;
                line-height: var(--lh-l);
            }

            .product.attribute {
                strong {
                    &:extend(.sr--only);
                }
            }

            .delivery_time {
                display: none;
            }

            .attribute.sku {
                font-size: var(--fs-xs);
                color: var(--c-gray);
            }

            .badges {
                margin: @md 0 0.313rem 0;
            }
        }

        &-addto-links {
            display: block;
            width: 100%;
            text-align: left;
            margin-top: 1.875rem;
        }

        &__towishlist {
            font-size: var(--fs-xs);
            font-weight: var(--fw-bold);
            color: var(--c-blue-dark);
            letter-spacing: 0.063rem;

            .i-favourite {
                &:before {
                    font-size: var(--fs-s);
                    padding-right: 0.75rem;
                }
            }

            &:hover {
                color: var(--c-blue-light);

                .i-favourite {
                    &:before {
                        color: var(--c-blue-light);
                    }
                }
            }
        }

        &__delivery-infos {
            display: flex;
            font-size: var(--fs-xss);
            text-transform: uppercase;
            color: var(--c-gray);
            margin-top: 1.188rem;

            .stock {
                &:after {
                    content: "/";
                    display: inline-block;
                    padding: 0 0.625rem;
                }
            }
        }

        &__media {
            position: relative;
            grid-area: media;
            @media screen and (min-width: @screen__m) {
                min-height: 700px;
            }

            &:before {
                content: '';
                background: url(../images/media_background.png) center center no-repeat;
                background-size: cover;
                top: -55px;
                width: 100%;
                left: 0;
                position: absolute;
                height: 100%;
            }
        }
    }
}

.product-options-wrapper {
    .control {
        max-width: 446px;
        &:extend(.select-arrow all);
    }
}

.block.related {
    grid-area: related;
}

.block.upsell {
    grid-area: upsell;
}

.block.crosssell, .block.upsell, .block.related {
    max-height: 570px;
    margin-bottom: 0.938rem;
    width: 100%;

    .tns-item {
        &:extend(.slider-border all);
    }

    .tns-inner {
        &:extend(.slider-last-border-hide);
    }

    .product-list__header {
        &:extend(.heading-title-logo);
        margin-bottom: @base;
        @media screen and (min-width: @screen__m) {
            margin-bottom: 3.75rem;
        }
    }
}
