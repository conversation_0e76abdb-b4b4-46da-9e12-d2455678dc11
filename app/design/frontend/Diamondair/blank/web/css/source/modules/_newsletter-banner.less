.newsletter-banner {
    align-items: center;
    display: flex;
    height: auto;
    justify-content: center;
    margin-bottom: @base;
    margin-top: @base;
    min-height: 400px;
    overflow: hidden;
    position: relative;
    @media screen and (min-width: @screen__m) {
        margin-bottom: @teaser-m;
        margin-top: @teaser-m;
        padding: 0;
    }

    &:after {
        background: linear-gradient(180deg, #000000 0%, rgba(0, 0, 0, 0.58) 55.01%, rgba(0, 0, 0, 0.44) 100%);
        content: '';
        display: block;
        height: 100%;
        left: 0;
        opacity: 0.4;
        pointer-events: none;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: 0;
    }

    &__content-wrapper {
        .container(@screen__xl);
        z-index: 2;
        color: var(--c-white);
    }

    &__image {
        display: block;
        height: 100%;
        left: 0;
        object-fit: cover;
        object-position: center center;
        position: absolute;
        top: 0;
        width: 100%;
    }

    &__content-wrapper {
        position: relative;
        text-align: center;
    }

    &__subtext {
        font-weight: var(--fw-bold);
        margin-bottom: @xs;
        @media screen and (min-width: @screen__m) {
            margin-bottom: @base;
        }
    }

    .newsletter {
        padding-left: @sm;
        padding-right: @sm;
        width: 100%;
        @media screen and (min-width: @screen__m) {
            padding: 0;
            width: auto;
        }

        &__form {
            width: 100%;
            @media screen and (min-width: @screen__m) {
                align-items: baseline;
                display: grid;
                grid-gap: 1rem;
                grid-template-columns: 8fr 4fr;
                max-width: 700px;
            }
        }

        &__content {
            display: flex;
            justify-content: center;
        }

        &__input {
            background: var(--c-white);
            font-weight: var(--fw-normal);
            height: 55px;
        }

        &__actions-subscribe {
            height: 55px;
            max-width: 200px;
            @media screen and (min-width: @screen__m) {
                max-width: 100%;
            }
        }

        input {
            font-weight: var(--fw-normal);
        }

        input::placeholder { /* Firefox, Chrome, Opera */
            font-weight: var(--fw-normal);
        }

        input:-ms-input-placeholder { /* Internet Explorer 10-11 */
            font-weight: var(--fw-normal);
        }

        input::-ms-input-placeholder { /* Microsoft Edge */
            font-weight: var(--fw-normal);
        }
    }
}
