select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-clip: padding-box;
    background-color: transparent;
    border: 1px solid @c-base;
    border-radius: 0;
    color: @c-base;
    display: block;
    height: 2.5rem;
    max-width: 100%;
    padding-left: 0.5rem;
    position: relative;
    text-transform: none;
    vertical-align: middle;
    width: 100%;
}

.field {
    margin: @base 0;


    &.required,
    &._required {
        > .label {
            &:after {
                color: var(--c-error__color);
                content: '*';
                display: inline-block;
                margin-left: 3px;
            }
        }
    }

    &.checkout-agreement {
        > .label {
            &:after {
                content: '';
                margin-left: 0;
            }
        }
    }

    /*&.checkout-agreement.required {
        position: relative;

        &:after {
            color: var(--c-error__color);
            content: '*';
            display: inline-block;
            position: absolute;
            right: 0;
        }
    }*/

    label {
        font-size: @font-size__base;
        font-weight: @font-weight__bold;
        padding-bottom: @base;
    }
}

.select {

    &__field {
        position: relative;

        &::after {
            &:extend([class*='i-']:before all);
            &:extend(.i-chevron-down:before all);

            align-items: center;
            bottom: 0;
            display: flex;
            font-size: 8px;
            padding: 0 15px;
            pointer-events: none;
            position: absolute;
            right: 0;
            top: 0;
        }
    }
}
