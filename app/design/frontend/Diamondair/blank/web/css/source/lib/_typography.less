// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Typography
//  _____________________________________________

.lib-font-face(
@family-name,
@font-path,
@font-weight: normal,
@font-style: normal
) {
    @font-face {
        font-family: @family-name;
        font-style: @font-style;
        font-weight: @font-weight;
        src:
            url('@{font-path}.woff2') format('woff2'),
            url('@{font-path}.woff') format('woff');
    }
}

//  Rem font size
.lib-font-size(@sizeValue) when not (ispercentage(@sizeValue)) and not (@sizeValue = false) and (isunit(@sizeValue, @font-size-unit)) {

}

.lib-font-size(@sizeValue) when not (ispercentage(@sizeValue)) and not (@sizeValue = false) and (isunit(@sizeValue, em)) {

}

.lib-font-size(@sizeValue) when not (ispercentage(@sizeValue)) and not (@sizeValue = false) and not (isunit(@sizeValue, em)) and not (isunit(@sizeValue, @font-size-unit)) {

}


//  Rem line height
.lib-line-height(@heightValue) when not (@heightValue = false) and not (ispercentage(@heightValue)) {

}

.lib-line-height(@heightValue) when (ispercentage(@heightValue)) and not (@heightValue = false) {
}

.lib-wrap-words() {
    overflow-wrap: break-word;
    -ms-word-break: break-all;
    word-break: break-word;
    word-wrap: break-word;
    .lib-hyphens();
}

.lib-text-overflow() {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.lib-text-hide() {
    background-color: transparent;
    border: 0;
    color: transparent;
    font: ~'0/0' a;
    text-shadow: none;
}

//  Optional hyphenation
.lib-hyphens(@mode: auto) {
    -webkit-hyphens: @mode;
    -moz-hyphens: @mode;
    -ms-hyphens: @mode; // IE10+
    hyphens: @mode;
}

.lib-typography(
@_font-size: @font-size__base,
@_color: @text__color,
@_font-family: @font-family__base,
@_font-weight: @font-weight__regular,
@_line-height: @line-height__base,
@_font-style: @font-style__base
) {

}

.lib-list-reset-styles(@_margin: 0, @_padding: 0) {

}

.lib-list-inline() {

}

.lib-link(
@_link-color: @link__color,
@_link-text-decoration: @link__text-decoration,
@_link-color-visited: @link__visited__color,
@_link-text-decoration-visited: @link__visited__text-decoration,
@_link-color-hover: @link__hover__color,
@_link-text-decoration-hover: @link__hover__text-decoration,
@_link-color-active: @link__active__color,
@_link-text-decoration-active: @link__active__text-decoration
) {

}

.lib-heading(@_heading_level: h1) {

}

//
//  Base typography
//  ---------------------------------------------

.lib-typography__base(
@_abbr-border-color: @abbr__border-color,
@_dfn-font-style: @font-style__emphasis,
@_emphasis-font-style: @font-style__emphasis,
@_hr-border-color: @hr__border-color,
@_hr-border-style: @hr__border-style,
@_hr-border-width: @hr__border-width,
@_hr-margin-bottom: @line-height__computed,
@_hr-margin-top: @line-height__computed,
@_mark-background-color: @mark__background-color,
@_mark-color: @mark__color,
@_p-margin-bottom: @p__margin-bottom,
@_p-margin-top: @p__margin-top,
@_root-font-size: @root__font-size,
@_small-font-size: @font-size__s,
@_strong-font-weight: @font-weight__bold,
@_sub-sup-font-size: (@font-size__xs/@font-size__base)*100%
) {

}

//
//  Headings
//  ---------------------------------------------

.lib-typography-headings(
@_heading-small-color: @heading__small-color,
@_heading-small-line-height: @heading__small-line-height,
@_heading-small-size: @heading__small-size
) {

}


//
//  Links
//  ---------------------------------------------

.lib-typography-links() {

}

//
//  Unordered and Ordered lists
//  ---------------------------------------------

.lib-typography-lists(
@_list-margin-bottom: @list__margin-bottom,
@_list-margin-top: @list__margin-top,
@_list-item-margin-bottom: @list-item__margin-bottom,
@_list-item-margin-top: @list-item__margin-top,
@_dl-margin-bottom: @dl__margin-bottom,
@_dl-margin-top: @dl__margin-top,
@_dd-margin-bottom: @dd__margin-bottom,
@_dd-margin-top: @dd__margin-top,
@_dt-font-weight: @dt__font-weight,
@_dt-margin-bottom: @dt__margin-bottom,
@_dt-margin-top: @dt__margin-top
) {

}

//
//  Code (inline and block)
//  ---------------------------------------------

.lib-typography-code(
@_font-family-monospace: @font-family__monospace,
@_code-background-color: @code__background-color,
@_code-color: @code__color,
@_code-font-size: @code__font-size,
@_code-padding: @code__padding,
@_kbd-background-color: @kbd__background-color,
@_kbd-color: @kbd__color,
@_kbd-font-size: @code__font-size,
@_kbd-padding: @code__padding,
@_pre-background-color: @pre__background-color,
@_pre-border-color: @pre__border-color,
@_pre-border-width: @pre__border-width,
@_pre-color: @pre__color,
@_pre-font-size: @code__font-size,
@_pre-line-height: @line-height__base,
@_pre-margin: 0 0 @indent__s,
@_pre-padding: @indent__s
) {

}

//
//  Blockquotes
//  ---------------------------------------------

.lib-typography-blockquote(
@_blockquote-border-color: @blockquote__border-color,
@_blockquote-border-width: @blockquote__border-width,
@_blockquote-font-size: @blockquote__font-size,
@_blockquote-font-style: @blockquote__font-style,
@_blockquote-margin: @blockquote__margin,
@_blockquote-padding: @blockquote__padding,
@_blockquote-small-before-content: @blockquote__content-before,
@_blockquote-small-color: @blockquote-small__color,
@_blockquote-small-font-size: @blockquote-small__font-size,
@_blockquote-small-line-height: @line-height__base,
@_blockquote-cite: @cite__font-style,
@_cite: @cite__font-style
) {

}

//
//  All typography
//  ---------------------------------------------

.lib-typography-all() {
    .lib-typography__base();
    .lib-typography-headings();
    .lib-typography-links();
    .lib-typography-lists();
    .lib-typography-code();
    .lib-typography-blockquote();
}
