.field {

    .block-customer-login & {
        margin-bottom: 2.5rem;
    }

    .label {
        text-transform: uppercase;
        font-size: var(--fs-xs);
        font-weight: var(--fw-normal);
        padding-bottom: 0.625rem;
        letter-spacing: 0.063rem;
    }

    &.choice {
        .label {
            text-transform: none;
        }
    }

    select {
        height: 55px;
        font-size: var(--fs-xs);
        padding-left: 1.25rem;
        border-color: var(--c-gray-light);
    }

    input {
        height: 55px;
        font-size: var(--fs-xs);
        border-color: var(--c-gray-light);
        border-radius: 0;
    }

    &:not(.customer-name-prefix) &:not(.checkout-agreement) {
        &.required, &._required {
            .label {
                &:after {
                    content: '*';
                    display: inline-block;
                }
            }
        }
    }

    .note {
        display: block;

        span {
            font-size: var(--fs-xs);
            color: var(--c-gray);
        }
    }

    textarea {
        min-height: 150px;
    }
}

.fieldset:after {
    margin: 1.25rem 0 0;
    content: attr(data-hasrequired);
    display: block;
    letter-spacing: normal;
    word-spacing: normal;
    font-size: var(--fs-xs);
    color: var(--c-gray);
}
