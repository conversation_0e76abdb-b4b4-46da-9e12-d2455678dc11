///////////////////////////
// FORM RESET
// To set basic styles for input fields

@input-background-color: var(--c-white);
@input-background-color--disabled: var(--c-gray-light);
@input-color: var(--c-base);
@input-placeholder-color: var(--c-gray-light);
@input-border: 1px solid var(--c-base);
@input-border-radius: 5px;
@input-font-size: var(--fs-base);
@input-line-height: var(--lh-base);

input {
    background-clip: padding-box;
    background-color: @input-background-color;
    border: @input-border;
    border-radius: @input-border-radius;
    color: @input-color;
    display: block;
    font-size: @input-font-size;
    line-height: @input-line-height;
    padding: 0.5rem;
    width: 100%;

    &:focus {
        &:extend(.box-shadow--focus all);
    }

    &::-ms-expand { // sass-lint:disable-line no-vendor-prefixes
        background-color: transparent;
        border: 0;
    }

    &::placeholder {
        color: @input-placeholder-color;
    }

    &:disabled,
    &[readonly] {
        background-color: @input-background-color--disabled;
    }
}

select {
    &:extend(input all);

    appearance: none;
    vertical-align: middle;

    &:focus::-ms-value { // sass-lint:disable-line no-vendor-prefixes
        background-color: @input-background-color;
        color: @input-color;
    }

    &::-ms-expand { // sass-lint:disable-line no-vendor-prefixes
        display: none;
    }
}

textarea {
    &:extend(input all);
}

.form {
    &__group {
        margin-bottom: @lg;
    }
}

.control {
    &--inline {
        display: flex;
        > div:not(:last-child) {
            margin-right: @md;
        }
    }
}
