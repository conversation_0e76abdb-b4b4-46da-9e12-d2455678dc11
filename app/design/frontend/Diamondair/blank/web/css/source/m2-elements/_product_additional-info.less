.product {
    &.info.detailed {
        grid-area: additionalinfo;
        padding-top: @base;
        padding-bottom: @base;
        @media screen and (min-width: @screen__m) {
            .container(@layout__m-max-width);
            padding-top: 2.5rem;
            padding-bottom: @md;
            width: 100%;
        }

        .data {

            &.title {
                --fs-xs: 1.125rem;
                font-size: var(--fs-xs);
                padding: 1.25rem 0 1.25rem 0.938rem;
                border-bottom: 1px solid @c-gray-light;
                &:extend(.select-arrow all);

                &:first-child {
                    border: none;
                    font-weight: var(--fw-bold);

                    &:after {
                        display: none;
                    }

                    &:focus {
                        outline: none;
                    }

                    a {
                        &:hover {
                            color: var(--c-base);
                            cursor: initial;
                        }
                    }
                }

                &.active {
                    border-bottom: 0;

                    &:after {
                        transform: rotate(0);
                    }
                }

                &:after {
                    height: 66px;
                }

                &:focus {
                    outline: none;
                }
            }

            &.content {
                padding: 0.938rem;

                ul {
                    list-style-type: disc;
                    margin-left: @md;
                }
            }

            #description {
                display: block !important;
                border-bottom: 1px solid @c-gray-light;
            }
        }
    }

    .additional-attribute-table {
        @media screen and (min-width: @screen__m) {
            margin: 0 0.938rem;
            max-width: 825px;
        }

        &__data {
            display: block;
        }

        &__table-data {
            display: block;
        }

        &__tbody {
            display: grid;
        }

        &__tr {
            display: flex;
            padding: 0.375rem 0;
            font-size: var(--fs-xs);
            @media screen and (min-width: @screen__l) {
                display: grid;
                grid-template-columns: 2fr 10fr;
            }

            &:nth-child(even) {
                background: var(--c-white);
            }

            &:nth-child(odd) {
                background: var(--c-gray-lightest);
            }
        }

        &__col-label {
            font-weight: var(--fw-normal);
            padding-left: 0.625rem;
            display: none !important;
            @media screen and (min-width: @screen__l) {
                display: block !important;
            }
        }

        &__col-data {
            padding: 0.25rem 0.25rem !important;
            @media screen and (min-width: @screen__s) {
                padding-left: 0;
            }

            &:before {
                font-weight: var(--fw-normal) !important;
            }
        }
    }
}
