/////////////////////////////////////
// GENERAL STYLES FOR CONTENT
// standard html elements within the content-wrapper
// sass-lint:disable no-vendor-prefixes

html {
    font-size: 100%;
}

body {
    color: @primary__color;
    font-family: @font-family__sans-serif;
    font-size: @font-size__base;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    font-weight: var(--fw-normal);
    line-height: @line-height__base;
}


.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-bottom: @base;
    margin-top: 0;

    &.ce-headline-center {
        text-align: center;
    }

    &.ce-headline-right {
        text-align: right;
    }
}

h2,
h3,
h4,
h5,
h6 {
    font-family: @font-family__sans-serif;
    line-height: var(--lh-base);
    word-wrap: break-word;
}

.h1,
h1 {
    font-family: @font-family__sans-serif;
    font-size: var(--fs-m);
    font-weight: var(--fw-normal);
    line-height: var(--lh-s);
    margin-bottom: @md;
    word-wrap: break-word;

    @media screen and (min-width: @screen__l) {
        font-size: var(--fs-xl);
    }


    span {
        display: block;
        font-family: @font-family__sans-serif;
        font-size: var(--fs-m);
        font-weight: var(--fw-normal);

        @media screen and (min-width: @screen__l) {
            font-size: var(--fs-xl);
        }
    }
}

h2,
.h2 {
    font-family: @font-family__sans-serif;
    font-size: var(--fs-s);
    font-weight: var(--fw-normal);
    line-height: var(--lh-s);
    margin-bottom: @md;
    word-wrap: break-word;

    @media screen and (min-width: @screen__l) {
        font-size: var(--fs-l);
    }


    span {
        display: block;
        font-family: @font-family__sans-serif;
        font-size: var(--fs-m);
        font-weight: var(--fw-normal);

        @media screen and (min-width: @screen__l) {
            font-size: var(--fs-l);
        }
    }
}

.h3,
h3 {
    font-size: var(--fs-s);
    font-weight: var(--fw-normal);
    @media screen and (min-width: @screen__l) {
        font-size: var(--fs-m);
    }
}

.h4,
h4 {
    font-size: var(--fs-base);
    font-weight: var(--fw-normal);
    margin-bottom: @s;
}

.h5,
h5 {
    font-size: var(--fs-xs);
    font-weight: var(--fw-normal);
}

.paragraph {
    font-size: var(--fs-base);
    line-height: var(--lh-base);
    margin-bottom: @base;
    margin-top: 0;
}

.p,
p {
    &:extend(.paragraph);

    &.text-center {
        text-align: center;
    }

    &.text-justify {
        text-align: justify;
    }

    &.text-right {
        text-align: right;
    }
}

.strong,
strong {
    font-weight: var(--fw-bold);
}

.underline {
    text-decoration: underline;
}

.figure,
figure {
    margin: 0;
}

.img,
img {
    display: block;
    height: auto;
    max-width: 100%;
}

.image-caption {
    color: var(--c-gray-light);
    font-size: var(--fs-xs);
    margin-top: 0.5rem;
}

.blockquote,
blockquote {
    padding-left: 20px;
    position: relative;

    p {
        font-size: @font-size__base;
    }

    &::before {
        background: none;
        bottom: 0;
        content: '';
        left: 0;
        position: absolute;
        top: 0;
        width: 6px;
    }
}

.footnote {
    font-size: @font-size__s;
}

.preformatted {
    &:extend(.p-base);
    &:extend(.mt-md);

    background: var(--c-gray-lightest);
    margin-bottom: @md;
    overflow: scroll;
    padding: @base;
}

.pre,
pre {
    &:extend(.preformatted);
}
