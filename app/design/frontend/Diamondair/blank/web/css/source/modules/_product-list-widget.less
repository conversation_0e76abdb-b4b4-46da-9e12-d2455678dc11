.product-list-widget {
    padding: @lg 0;


    .tns-item {
        &:extend(.slider-border all);
    }

    .tns-inner {
        &:extend(.slider-last-border-hide);
    }

    .slider-controls {

        &__button {
            border: 1px solid var(--c-gray-lightest);
            border-radius: 35px;
            font-size: var(--fs-m);
            height: 62px;
            width: 62px;

            span {
                position: relative;
            }

            &-left {
                left: -25px;

                span {
                    left: 7px;
                }

                @media screen and (min-width: @screen__xl) {
                }
            }

            &-right {
                right: -25px;

                span {
                    right: 7px;
                }

                @media screen and (min-width: @screen__xl) {
                }
            }
        }
    }

    .product-list {


        &--image {
            &-left,
            &-right {

                .product-list__media {
                    margin-bottom: @lg;
                    @media screen and (min-width: @screen__m) {
                        margin-bottom: 0;
                        min-height: 477px;
                        img {
                            display: block;
                            height: 100%;
                            object-fit: cover;
                            object-position: center center;
                            position: absolute;
                            top: 0;
                            width: 100%;
                        }
                    }
                }

                @media screen and (min-width: @screen__m) {
                    align-items: center;
                    display: flex;
                    > * {
                        width: 50%;
                    }
                }
            }

            &-right {
                @media screen and (min-width: @screen__m) {
                    flex-direction: row-reverse;
                }
            }
        }

        &__media {
            position: relative;


            &:after {
                background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
                content: '';
                display: block;
                height: 100%;
                left: 0;
                opacity: 0.7;
                pointer-events: none;
                position: absolute;
                top: 0;
                width: 100%;
                z-index: 0;
            }
        }

        &__figcaption {
            bottom: @xl;
            color: var(--c-white);
            left: @lg;
            position: absolute;
            z-index: 1;

            @media screen and (min-width: @screen__m) {
                font-size: var(--fs-l);
            }
        }

        &__content {
        }

        &__slider {
            overflow-x: hidden;

            .slider-controls__button {
                span {
                    &:before {
                        @media screen and (max-width: @screen__s) {
                            display: inline-flex !important;
                            align-items: center;
                            justify-content: center;
                            width: 100% !important;
                        }
                    }
                }
            }
        }

        &__list {
            display: flex;
        }

        &__header {
            @media screen and (min-width: @screen__l) {
                margin-bottom: @xl;
            }
        }
    }

    &.product-list--image-above {
        .product-list__media {
            margin-bottom: @lg;

            @media screen and (min-width: @screen__l) {
                margin-bottom: @xl;
                min-height: 527px;
                img {
                    display: block;
                    height: 100%;
                    object-fit: cover;
                    object-position: center center;
                    position: absolute;
                    top: 0;
                    width: 100%;
                }
            }
        }
    }
}

.product-list {
    &--no-image {
        @media screen and (min-width: @screen__l) {
            padding-bottom: 3.5rem;
        }
    }
}
