.sidebar-nav {
    @media screen and (max-width: @screen__mmax) {
        display: none;
    }

    @media screen and (min-width: @screen__m) {
        &--current {
            > a {
                color: var(--c-primary);
            }
        }

        &__list {
            font-size: var(--fs-xs);

            > .sidebar-nav__item {
                margin-left: 0;
            }
        }

        &__item {
            &:last-child {
                > .sidebar-nav__link:not(.sidebar-nav__title) {
                    border-color: transparent;
                }
            }
        }

        &__dropdown {
            // background-color: white;
            // margin-bottom: 0;
            // transition: height 0.25s ease;

            // &.is--collapsing {
            //     height: 0;
            //     overflow: hidden;
            //     position: relative;
            //     transition: height 0.25s ease;
            // }

            // &:not(.is--active) {
            //     display: none;
            // }
        }


        &__link {
            align-items: center;
            background: transparent;
            border: 0;
            border-bottom: 1px solid var(--c-gray-light);
            cursor: pointer;
            display: flex;
            padding: @base @lg @base 0;
            position: relative;
            text-align: left;

            // &:not(.sidebar-nav__parent) {
            //     margin-left: 1.675rem;
            // }

            // &.is--active {
            //     border-color: transparent;

            //     .sidebar-nav__link-icon {
            //         transform: rotate(90deg);
            //     }
            // }


            &:focus {
                color: var(--c-primary);
                outline: 1px solid rgba(0, 0, 0, 0);
            }

            &-icon {
                color: var(--c-primary);
                font-size: var(--fs-base);
                position: absolute;
                right: 0;
                top: 0.825rem;
                transform: rotate(-90deg);
                transition: transform 0.2s ease-in;
            }
        }

        &__title {
            --fs-s: 1.4375rem;

            border-bottom: 1px solid var(--c-gray-light);
            display: block;
            font-size: var(--fs-s);
            margin-bottom: @md;
            padding-bottom: @sm;
        }
    }

    &__category {
        &:not(.is--active) {
            display: none;
        }
    }
}
