.category-view {

    &__media {
        align-items: flex-end;
        display: flex;
        justify-content: center;
        min-height: 350px;
        position: relative;
        width: 100%;

        &:after {
            background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) 43%, rgba(0, 0, 0, 0.9) 100%);
            content: '';
            display: block;
            height: 100%;
            left: 0;
            opacity: 0.7;
            pointer-events: none;
            position: absolute;
            top: 0;
            width: 100%;
            z-index: 0;
        }

        @media screen and (min-width: @screen__xl) {
            min-height: 415px;
        }

        ~ .page-title-wrapper {
            display: none;
        }

        ~ .breadcrumbs {
            margin-top: @md;
        }
    }

    &__image {
        display: block;
        height: 100%;
        object-fit: cover;
        object-position: center center;
        position: absolute;
        top: 0;
        width: 100%;
    }

    &__header {
        color: var(--c-white);
        position: relative;
        z-index: 1;
    }

    &__title {
        margin-bottom: 2rem;

        @media screen and (min-width: @screen__xl) {
            font-size: 3.8125rem;
            margin-bottom: 4.375rem;
        }
    }

    .page-title-wrapper {
        margin-top: 2.25rem;
        text-align: center;

        .base {
            @media screen and (min-width: @screen__xl) {
                font-size: 3.8125rem;
            }
        }
    }
}

.category-description {
    .container(@layout__m-max-width);

    font-size: var(--fs-s);
    padding: @xl 15px @xl;
    text-align: center;
}
