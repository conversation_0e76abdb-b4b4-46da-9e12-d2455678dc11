.ie-notification {
    background: @c-white;
    box-shadow: 1px 4px 21px 0 rgba(0, 0, 0, 0.13);
    left: 50%;
    max-width: 447px;
    padding: 40px;
    position: fixed;
    top: 2rem;
    transform: translate(-50%, 0);
    width: 95vw;
    z-index: 105;

    p {
        font-weight: 400;
    }

    a {
        color: @c-primary;
        font-weight: 700;

        &:hover {
            color: @c-base;
        }

        &.text--base {
            color: @c-base;

            &:hover {
                color: @c-primary;
            }
        }
    }


    .text--primary {
        color: @c-primary;
    }

    button {
        background-color: transparent;
        border: 0;
        cursor: pointer;
        position: absolute;
        right: 18px;
        top: 18px;
    }


    @media screen and (min-width: @screen__m) {
        padding: 67px 50px 50px;
        top: 254px;
    }

    &__overlay {
        background: @c-white;
        height: 100%;
        left: 0;
        opacity: 0.65;
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 100;
    }
}

