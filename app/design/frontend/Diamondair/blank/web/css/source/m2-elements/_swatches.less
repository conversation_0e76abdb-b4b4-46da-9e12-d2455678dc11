.swatch-attribute-label {
    font-size: var(--fs-xs);
    font-weight: var(--fw-normal);
    padding-bottom: 0.625rem;
    text-transform: uppercase;
}

.swatch-attribute-selected-option {
    color: var(--c-base);
    font-size: var(--fs-xs);
    font-weight: var(--fw-bold);
    padding-left: 0.313rem;
    text-transform: uppercase;
}

.swatch-option {
    border-radius: 50%;
    height: 21px;
    margin: 0 0.325rem 0 0.325rem;
    min-width: 21px;
    width: 21px;
    border: 1px solid var(--c-gray-light);

    .product__info-main & {
        height: 24px;
        min-width: 24px;
        width: 24px;
    }

    .filter-options & {
        height: 16px;
        min-width: 16px;
        width: 16px;
    }

    .product-list__additional & {
        margin-left: 0.225rem;
        margin-right: 0.225rem;
    }

    &:hover {
        border: 1px solid var(--c-gray-light) !important;
        border-radius: 50%;
    }
}

.swatch-option.selected {
    outline: 0;
    overflow: initial;
    position: relative;

    &:before {
        background: #ffffff;
        border-radius: 20px;
        bottom: -3px;
        content: '';
        display: block;
        left: -3px;
        position: absolute;
        right: -3px;
        top: -3px;
        z-index: -1;
    }

    &:after {
        background: #000000;
        border-radius: 20px;
        bottom: -4px;
        content: '';
        display: block;
        left: -4px;
        position: absolute;
        right: -4px;
        top: -4px;
        z-index: -2;
    }

    .product-list__additional & {

        &:before {
            bottom: -1px;
            left: -1px;
            right: -1px;
            top: -1px;
        }

        &:after {
            bottom: -2px;
            left: -2px;
            right: -2px;
            top: -2px;
        }
    }
}

.swatch-attribute.color {
    .swatch-attribute-options {
        &:after {
            content: '';
        }
    }
}

.swatch-attribute-options {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
    margin-top: 0;
    position: relative;

    .product__info-main & {
        margin-bottom: 0.9rem;
        margin-top: 0.9rem;
    }

    .product-list__additional & {
        margin-left: -0.225rem;
        margin-right: -0.225rem;
    }

    &:after {
        content: '->';
        display: inline-block;
        font-size: var(--fs-base);
        font-weight: var(--fw-normal);
        height: auto;
        pointer-events: none;
        position: absolute;
        right: 10px;
        top: 10px;
        visibility: visible;
    }
}

.swatch-option.image:not(.disabled), .swatch-option.color:not(.disabled) {
    &:hover {
        outline: 0;
    }
}

.swatch-select {
    font-size: var(--fs-xs);
    font-weight: var(--fw-semibold);
}

.swatch-option-tooltip {
    display: none !important;
}
