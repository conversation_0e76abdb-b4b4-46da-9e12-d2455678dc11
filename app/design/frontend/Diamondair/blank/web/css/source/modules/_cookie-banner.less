#cookie-status{
    display: none;
}
.message.cookie-bar {
    z-index: 99;
}
.cookie-bar {
    background-color: var(--c-white);
    position: fixed;
    bottom: 0;
    box-shadow: 1px 4px 21px 0 rgba(0, 0, 0, 0.13);
    width: 100%;

    &__content {
        .container(@screen__xl);
        align-items: center;
        display: grid;
        grid-template-columns: 1fr;
        padding: 1rem;
        @media screen and (min-width: @screen__s) {
            grid-template-columns: 8fr 4fr;
        }
    }

    &__actions {
        display: flex;
        @media screen and (min-width: @screen__s) {
            justify-content: flex-end;
        }
    }

    #btn-cookie-disallow{
        margin-left: 20px;
    }
}
