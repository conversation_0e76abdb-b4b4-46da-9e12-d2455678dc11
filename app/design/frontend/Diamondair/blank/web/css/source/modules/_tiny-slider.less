.tns-outer {
    padding: 0 !important; // remove padding: clientWidth = width + padding (0) = width
    [hidden] {
        display: none !important;
    }

    [aria-controls], [data-action] {
        cursor: pointer;
    }
}

.tns-slider {
    -webkit-transition: all 0s;
    -moz-transition: all 0s;
    transition: all 0s;

    > .tns-item {
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }
}

.tns-horizontal {
    &.tns-subpixel {
        display: flex;

        > .tns-item {
            display: inline-flex;
            justify-content: center;
            white-space: normal;
        }
    }

    &.tns-no-subpixel {
        &:after {
            clear: both;
            content: '';
            display: table;
        }

        > .tns-item {
            float: left;
        }
    }

    &.tns-carousel {
        &.tns-no-subpixel {
            > .tns-item {
                margin-right: -100%;
            }
        }
    }
}

.tns-no-calc {
    left: 0;
    position: relative;
}

.tns-gallery {
    left: 0;
    min-height: 1px; // make sure slider container is visible
    position: relative;
    // overflow: hidden;
    > .tns-item {
        left: -100%;
        position: absolute;
        -webkit-transition: transform 0s, opacity 0s;
        -moz-transition: transform 0s, opacity 0s;
        transition: transform 0s, opacity 0s;
    }

    > .tns-slide-active {
        left: auto !important;
        position: relative;
    }

    > .tns-moving {
        -webkit-transition: all 0.25s;
        -moz-transition: all 0.25s;
        transition: all 0.25s;
    }
}

.tns-autowidth {
    display: inline-block;
}

.tns-lazy-img {
    opacity: 0.6;
    -webkit-transition: opacity 0.6s;
    -moz-transition: opacity 0.6s;
    transition: opacity 0.6s;

    &.tns-complete {
        opacity: 1;
    }
}

.tns-ah {
    -webkit-transition: height 0s;
    -moz-transition: height 0s;
    transition: height 0s;
}

.tns-ovh {
    overflow: hidden;
}

.tns-visually-hidden {
    left: -10000em;
    position: absolute;
}

.tns-transparent {
    opacity: 0;
    visibility: hidden;
}

.tns-fadeIn {
    filter: alpha(opacity=100);
    opacity: 1;
    z-index: 0;
}

.tns-normal, .tns-fadeOut {
    filter: alpha(opacity=0);
    opacity: 0;
    z-index: -1;
}

// slider

.slider-controls {

    &--hide {
        display: none;
    }

    &__button {
        background-color: rgba(255, 255, 255, 0.3);
        border: 0;
        color: var(--c-blue-dark);
        cursor: pointer;
        font-size: var(--fs-m);
        position: absolute;
        top: 50%;
        transform: translate(0, calc(~'-50% - 6px'));
        transition: background-color 0.3s ease-in-out;
        z-index: 1;
        @media screen and (min-width: @screen__lg) {
            font-size: var(--fs-l);
        }

        &-left {
            left: -5px;

            @media screen and (min-width: @screen__lg) {
                left: -12px;
            }
        }

        &-right {
            right: -5px;
            @media screen and (min-width: @screen__lg) {
                right: -12px;
            }
        }
    }

    &.product-list__controls {
        .slider-controls__button {
            top: 37%;
        }
    }
}
