.wishlist {

    &__product-items {
        display: flex !important;
        flex-direction: column;
    }

    &__product-item-name {
        display: block;
        margin-top: @base;
    }

    &__tooltip {
        padding: @base;
    }

    &__field-qty {
        display: flex;
        justify-content: space-between;
        align-items: baseline;
        flex-direction: row;

        @media screen and (min-width: @screen__m) {
            justify-content: left;
            flex-direction: initial;
        }
    }

    &__qty-input {
        height: 55px !important;
        width: 60px;
        margin-left: 1rem;
        padding-left: .75rem;
        padding-right: 0;
        text-align: center;
        font-size: var(--fs-xs);
        font-weight: var(--fw-normal);
        border-radius: 0;
    }

    .price-box {
        display: flex;
        justify-content: center;
        @media screen and (min-width: @screen__m) {
            justify-content: left;
        }
    }

    &__product-item-info {
        @media screen and (min-width: @screen__m) {
            display: grid;
            grid-template-columns: 4fr 8fr;
            grid-gap: 1rem;
        }
    }

    &__delete {
        span {
            .sr--only();
        }

        &:before {
            color: var(--c-blue-dark);
            content: @i-trash;
            display: inline-block;
            font-family: @icons__font-name-custom;
            font-size: 1.563rem;
            text-align: center;
            vertical-align: top;
        }

        &:after {
            background: none;
            position: static;
        }

    }

    &__edit {
        span {
            .sr--only();
        }

        &:before {
            color: var(--c-blue-dark);
            content: @icon-settings;
            display: inline-block;
            font-family: @icons__font-name;
            font-size: 1.875rem;
            font-weight: normal;
            text-align: center;
            vertical-align: top;

        }

        &:after {
            background: none;
            position: static;
        }
    }

    &__tocart {
        margin: @base 0;
        width: 100%;
    }

    &__item {
        text-align: center;
        border: 0 !important;
        @media screen and (min-width: @screen__m) {
            text-align: left;
        }
    }

    .product-reviews-summary {
        display: flex;
        width: 100%;
        margin: 0;
        justify-content: center;
        @media screen and (min-width: @screen__m) {
            justify-content: left;
        }
    }

    @media screen and (min-width: @screen__m) {

        &__actions {
            background-color: var(--c-white);
            width: 100%;
        }

        &__item {
            padding: @base;
        }
    }

    .wishlist__actions > .wishlist__actions {
        @media screen and (max-width: @screen__m) {
            margin-top: @md;
        }
    }
}

.form-wishlist-items {
    .actions-toolbar {
        button {
            margin-bottom: @s;
        }
    }
}
