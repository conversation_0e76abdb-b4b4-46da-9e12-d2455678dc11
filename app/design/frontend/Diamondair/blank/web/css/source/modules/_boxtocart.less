.box-tocart {
    display: block;

    &__qty {
        display: flex;
        margin: @md 0;
    }

    &__control {
        width: 60px;
        margin-right: @md;
    }

    &__input-qty {
        max-height: 60px;
        padding-left: .75rem;
        padding-right: 0;
        text-align: center;
        font-size: var(--fs-xs);
        font-weight: var(--fw-normal);
        border-radius: 0;
    }

    &__label {
        font-size: var(--fs-xs) !important;
        font-weight: var(--fw-normal) !important;
        padding-top: 0.938rem;
    }

    &__action-btn {
        padding: 1.063rem 1.813rem !important;
        letter-spacing: 0.063rem;
        max-width: 263px;


        .i-cart, .i-edit {
            &:before {
                --fs-s: 1.5rem;
                font-size: var(--fs-s);
                padding-right: @md;
            }
        }
    }

    &__update-btn {
        display: flex;
        align-items: center;

        &--title {
            text-align: left;
        }
    }
}
