.price-box {
    display: flex;

    .product-list & {
        flex-flow: row;
        justify-content: center;
    }

    .old-price {
        align-self: end;
        order: 1;

        .product-list & {
            align-self: flex-end;
        }

        .price {
            --fs-xs: 1.125rem;

            font-size: var(--fs-xs);
            font-weight: var(--fw-normal);

            .product-list & {
                --fs-xs: 1rem;

                color: var(--c-gray);
            }
        }

        .price-wrapper {
            text-decoration: line-through;

            .product-list &, .product-info-main & {
                padding-right: 0.938rem;
            }
        }

        .price-label {
            &:extend(.sr--only);
        }

    }

    .special-price {
        display: inline-block;
        margin-left: @base;

        .price-label {
            &:extend(.sr--only);
        }
    }

    .special-price, .normal-price {
        --fs-s: 1.438rem;

        align-self: end;
        font-size: var(--fs-s);
        font-weight: var(--fw-bold);
        order: 2;
    }

    .price {
        &-final_price {
            --fs-s: 1.438rem;

            font-size: var(--fs-s);
            font-weight: var(--fw-bold);

            .product-list & {
                font-size: var(--fs-base);
                font-weight: var(--fw-normal);
            }

            .price-label {
                &:extend(.sr--only);
            }
        }
    }

    .price-wrapper {
        &:after {
            content: '*';
            display: inline-block;
        }

        .product-list & {
            &:after {
                // content: '';
            }
        }
    }
}

.price-box + .price-details {
    &:extend(.sr--only);
}
