@layout-column-main__sidebar-offset: 2%;
@layout-column__additional-sidebar-offset: @layout-column-main__sidebar-offset;

//
//  Common
//  _____________________________________________
.columns {
    [class*='page-layout-2columns-'] & {
        display: grid;
        grid-template-areas: 'sidebar' 'sidebarAdditional' 'main';
    }

    .cms-no-route.cms-noroute-index[class*='page-layout-2columns-'] & {
        display: block;
    }
}

.column.main {
    grid-area: main;
    order: 1;
}

.products-grid {
    .catalog-category-view & {
        grid-area: products;
    }
}

.sidebar-main {
    grid-area: sidebar;
    order: 0;
    padding-top: @sm;

    .catalog-category-view & {
        padding-top: 0;
    }
}

.sidebar-additional {
    grid-area: sidebarAdditional;
    order: 0;
    padding-top: @sm;
}

.layered-filter {
    grid-area: filter;
}

.toolbar-products {
    &:last-child {
        grid-area: toolbarBottom;
    }
}

.toolbar-products {
    &:not(:last-child) {
        grid-area: toolbar;
    }
}

//
//  Mobile
//  _____________________________________________


@media screen and (max-width: @screen__mmax) {
    .navigation,
    .page-header .header.panel,
    .header.content,
    .page-main,
    .page-wrapper > .widget,
    .block.category.event,
    .top-container {
        padding-left: @layout__width-xs-indent;
        padding-right: @layout__width-xs-indent;
    }

    .page-main {

        .catalog-category-view & {
            position: relative;
        }

        .cms-home & {
            max-width: inherit;
            padding-left: 0;
            padding-right: 0;
        }

        .account &,
        .cms-privacy-policy & {
            padding-top: 41px;
            position: relative;
        }
    }
}


//
//  Desktop
//  _____________________________________________


@media screen and (min-width: @screen__m) {
    .navigation,
    .page-header .header.panel,
    .header.content,
    .page-wrapper > .widget,
    .block.category.event,
    .top-container,
    .page-main {
        box-sizing: border-box;
        margin-left: auto;
        margin-right: auto;
        max-width: @layout__max-width;
        padding-left: @layout-indent__width;
        padding-right: @layout-indent__width;
        width: auto;
    }

    .page-wrapper > .page-bottom {
        box-sizing: border-box;
        max-width: 100%;
        width: auto;
    }

    .column.main {

    }


    .page-wrapper {
        overflow-x: hidden;
    }

    .page-main {
        width: 100%;

        .catalog-category-view & {
            padding-top: @md;
        }

        .cms-home & {
            max-width: inherit;
            padding-left: 0;
            padding-right: 0;
        }
    }

    .columns {
        [class*='page-layout-2columns-'] & {
            grid-gap: 30px;
            grid-template-areas: 'sidebar main' 'sidebarAdditional main';
            grid-template-columns: minmax(260px, 3fr) minmax(280px, 9fr);
        }
    }

    .column.main {
        grid-area: main;
        min-height: 300px;
    }

    .sidebar-main {
        padding-top: @lg;

        .catalog-category-view & {
            padding-top: 0;
        }
    }


    .page-layout-2columns-right .sidebar-main {

    }

    .sidebar-additional {
        padding-top: @lg;
    }

    .panel.header {
        padding: 10px 20px;
    }
}


@media screen and (min-width: @screen__l) {
    .page-main {
        padding-top: @s;

        .header-transparent & {
            padding-top: @lg;
        }
    }

    .columns {
        [class*='page-layout-2columns-']:not(.catalog-category-view) & {
            grid-template-columns: minmax(240px, 3fr) minmax(300px, 9fr);
        }
    }
}

.is--hidden {
    display: none;
}
