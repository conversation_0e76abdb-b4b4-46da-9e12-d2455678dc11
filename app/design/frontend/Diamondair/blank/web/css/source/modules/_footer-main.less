.page-footer {
    padding-top: @base;
    @media screen and (min-width: @screen__m) {
        padding-top: @teaser-lg;
    }
}

.footer {

    &__main-outer {
        background: var(--c-gray);
        @media screen and (min-width: @screen__xs) {
            padding-bottom: 0;
        }
        padding-bottom: 1.563rem;
    }

    &__main-container {
        .container(@screen__xl);
        display: grid;
        grid-gap: 1rem;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        @media screen and (min-width: @screen__m) {
            grid-template-rows: 248px;
            padding-bottom: @xl;
        }
        padding-top: @xl;

        a {
            &:hover {
                color: var(--c-blue-light);
            }
        }
    }

    &__social-links-wrapper {
        .container(@screen__xl);
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        width: 180px;
        height: 46px;
        background-color: var(--c-gray);
        top: 1px;
    }

    &__social-links {
        display: inline-flex;
        position: relative;
        top: 10px;
        z-index: 1;
        margin: 0;

        &--link {
            --fs-m: 1.625rem;
            font-size: var(--fs-m);
            border: 1px solid #fff;
            border-radius: 50%;
            margin-right: 1.563rem;
            padding: 0.125rem;
            width: 45px;
            text-align: center;

            &:last-child {
                margin-right: 0;
            }

            > a {
                color: var(--c-white);
            }

            &:hover {
                background: var(--c-white);

                a {
                    color: var(--c-gray);
                }
            }
        }
    }

    &__shape-left {
        position: absolute;
        left: -25px;
        top: 0;
        width: 50px;
        height: 46px;
        transform: skew(-25deg);
        background-color: var(--c-gray);
        border-top-left-radius: 15%;
    }

    &__shape-right {
        position: absolute;
        right: -25px;
        top: 0;
        width: 50px;
        height: 46px;
        transform: skew(25deg);
        background-color: var(--c-gray);
        border-top-right-radius: 15%;
    }

    &__column {
        padding-right: @base;
        padding-bottom: @base;
        @media screen and (min-width: @screen__m) {
            padding-bottom: 0;
        }
        margin-bottom: @md;
        @media screen and (min-width: @screen__xs) {
            margin-bottom: 0;
        }

        &--title {
            color: var(--c-white);
            font-size: var(--fs-xs);
            border-bottom: 1px solid #fff;
            padding-bottom: @md;
            margin-bottom: @md;
            font-weight: var(--fw-bold);
        }

        &--subtitle {
            color: var(--c-white);
            font-size: var(--fs-xs);
        }

        &:last-child {
            margin-bottom: 0;
        }

        &:nth-of-type(2) {
            .footer__column--subtitle {
                margin-top: -@md;
                @media screen and (min-width: @screen__xs) {
                    margin-top: 0;
                }
            }
        }
    }

    &__payment-methods {
        display: inline-flex;
        align-items: center;
        padding-top: @sm;
        flex-wrap: wrap;
        margin: 0;

        &--method {
            margin-right: 1.563rem;
            @media screen and (min-width: @screen__m) {
                margin-bottom: 1.563rem;
            }
            margin-bottom: 1rem;

            &:last-child {
                margin-right: 0;
            }
        }


    }

    &__shipping-methods {
        margin: 0;
        padding-top: 0.813rem;
        padding-bottom: @md;
        display: inline-flex;
        align-items: center;
        flex-wrap: wrap;

        &--method {
            margin-right: 1.563rem;
            @media screen and (min-width: @screen__m) {
                margin-bottom: 1.563rem;
            }
            margin-bottom: 1rem;

            &:last-child {
                margin-right: 0;
            }
        }
    }

    .newsletter {
        &__field {
            margin-top: @md;
        }

        &__input {
            height: 48px;
        }

        &__actions-subscribe {
            max-width: 200px;
            height: 47px;
        }

        input {
            color: var(--c-white);
        }

        input::placeholder { /* Firefox, Chrome, Opera */
            color: var(--c-white);
        }

        input:-ms-input-placeholder { /* Internet Explorer 10-11 */
            color: var(--c-white);
        }

        input::-ms-input-placeholder { /* Microsoft Edge */
            color: var(--c-white);
        }
    }

    &__quicklinks {
        margin: 0;

        &--quicklink {
            font-size: var(--fs-xs);
            margin-bottom: @base;
            color: var(--c-white);

            a {
                color: var(--c-white);
            }
        }
    }

    [data-content-type="html"] {
        display: contents;
    }

}
