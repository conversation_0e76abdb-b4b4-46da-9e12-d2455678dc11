.header-toggle {
    background: none;
    border: 0 none;
    cursor: pointer;
    display: block;
    flex-shrink: 0;
    grid-area: toggle;
    height: 34px;
    overflow: hidden;
    padding: 0;
    position: relative;
    right: -@layout-indent__width / 2;
    transition: background 0.2s ease-in-out;
    width: 34px;

    .checkout-index-index & {
        display: none;
    }

    .header-transparent & {
        color: @c-white;
    }

    @media screen and (min-width: @screen__m) {
        display: none;
    }


    &__close {
        position: absolute;
        right: 6px;
        top: 7px;
        transform: translate(calc(~'100% + 10px'), 0) rotate(-215deg);
        transition: transform 0.2s ease-in-out;
    }

    &__burger {
        font-size: var(--fs-xs);
        opacity: 1;
        position: absolute;
        right: 6px;
        top: 9px;
        transform: translate(0, 0);
        transition: transform 0.2s ease-in-out, opacity 0.1s ease-in-out;
    }

    &.is--active {

        .header-toggle__close {
            transform: translate(0, 0) rotate(0);
        }

        .header-toggle__burger {
            opacity: 0;
            transform: translate(0, calc(-100% - 35px));
        }
    }
}
@-moz-document url-prefix() {
    .header-toggle {
        @media screen and (max-width: @screen__m) {
            order: 3;
        }
    }
}
