.customer-account-create, .customer-address-form, .customer-account-edit, .multishipping-checkout_address-newshipping {
    .page-main {
        .container(@layout__s-max-width);

        .field {
            @media screen and (min-width: @screen__l) {
                width: 680px;
            }

            &.confirmation {
                @media screen and (min-width: @screen__l) {
                    margin-top: 3.438rem;
                }
            }

            .label {
                text-transform: uppercase;
                font-size: var(--fs-xs);
                font-weight: var(--fw-normal);
                padding-bottom: .625rem !important;
                letter-spacing: .063rem;
                display: block !important;
                float: none !important;
                text-align: left !important;
            }

            .control {
                width: 100% !important;
            }
        }

        .fieldset {
            @media screen and (min-width: @screen__l) {
                width: 680px;
            }

            &.create {
                > .field {
                    margin-top: 0;
                    @media screen and (min-width: @screen__l) {
                        margin-bottom: 2.188rem;
                    }
                }
            }

            &.account {
                @media screen and (min-width: @screen__l) {
                    margin-top: 1.875rem;
                }
            }
        }

        .control.agreement-text {
            display: flex;

            input#agreement-checkbox {
                width: 25px;
                height: 25px;
                margin-right: 0.625rem;
            }
        }
    }
}
