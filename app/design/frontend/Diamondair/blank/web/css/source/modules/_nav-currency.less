@menu-breakpoint: @screen__m;
@menu-breakpoint-mobile: @screen__xxs;

.currency-switcher {
    text-transform: uppercase;

    &__trigger {
        &-text {
            cursor: pointer;
            font-size: var(--fs-xs);
            padding: @layout-indent__width / 2;
            position: relative;

            .header-transparent & {
                color: var(--c-white);
            }
            @media screen and (min-width: @menu-breakpoint-mobile) {
                padding-right: 20px;
            }

            &::before {
                &:extend([class*=' i-']:before);
                &:extend(.i-chevron-right:before);

                display: none;
                position: absolute;
                right: 0;
                top: 7px;
                transform: rotate(90deg);
                transition: transform 0.2s ease;

                @media screen and (min-width: @menu-breakpoint-mobile) {
                    display: inline-block;
                }
            }

            &.is--active {
                &::before {
                    transform: rotate(0);
                }
            }
        }
    }

    &__dropdown {
        .shadow-xs();

        background-color: var(--c-white);
        font-size: 0.9rem;
        left: 50%;
        opacity: 0;
        padding: 0.938rem 1.438rem;
        position: absolute;
        top: 50px;
        transform: translate(-50%, 30px);
        transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
        z-index: 11;

        @media screen and (min-width: @menu-breakpoint) {
            left: calc(50% - 5px);
            top: 50px;
            transform: translate(-50%, 30px);
        }

        &:before {
            border: 0.5rem solid black;
            border-color: #fff #fff transparent transparent;
            box-shadow: 3px -3px 3px 0 rgba(0, 0, 0, 0.05);
            content: '';
            height: 0;
            left: 50%;
            position: absolute;
            top: 0;
            transform: translate(-50%, 0) rotate(-45deg);
            transform-origin: 0 0;
            width: 0;
            @media screen and (min-width: @menu-breakpoint) {
                left: 37%;
                transform: rotate(-45deg);
            }
        }

        &.is--show {
            opacity: 1;
            transform: translate(-50%, 0);

            @media screen and (min-width: @menu-breakpoint) {
                transform: translate(-50%, 0);
            }
        }

        &:not(.is--active) {
            display: none;
        }
    }
}
