// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */


//
//  Typography
//  _____________________________________________

.lib-font-face(
@family-name,
@font-path,
@font-weight: normal,
@font-style: normal
) {
    @font-face {
        font-family: @family-name;
        font-style: @font-style;
        font-weight: @font-weight;
        src:
            url('@{font-path}.woff2') format('woff2'),
            url('@{font-path}.woff') format('woff');
    }
}

.lib-font-face(
    @family-name: 'icons-blank-theme',
    @font-path: '../fonts/Blank-Theme-Icons/Blank-Theme-Icons',
    @font-weight: normal,
    @font-style: normal
);

//Common
// _____________________________________________
@font-family-name__base: 'Univers';


.lib-font-face(
    @family-name: @font-family-name__base,
    @font-path: '../fonts/Univers/UniversCom-45Light',
    @font-weight: 300,
    @font-style: normal
);


.lib-font-face(
    @family-name: @font-family-name__base,
    @font-path: '../fonts/Univers/UniversLTStd-Cn',
    @font-weight: 400,
    @font-style: normal
);


.lib-font-face(
    @family-name: @font-family-name__base,
    @font-path: '../fonts/Univers/UniversLTStd-BoldCn',
    @font-weight: 700,
    @font-style: normal
);
