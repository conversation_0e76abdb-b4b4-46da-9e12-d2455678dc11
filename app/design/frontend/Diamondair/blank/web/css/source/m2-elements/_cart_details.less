.cart {
    .input-text {
        max-width: 100%;
        padding-left: 0.75rem;
        padding-right: 0;
        text-align: center;
        width: 3.75rem;
        border-radius: 0;

        @media screen and (max-width: @screen__s) {
            padding: 0;
            text-align: center;
            font-size: var(--fs-xs);
        }
    }

    a.action {
        &:extend(.btn all);
        &:extend(.btn--primary all);
    }

    &.table-wrapper {
        margin-bottom: @base;
    }

    .product-item-details {
        font-size: var(--fs-xs);
    }

    .actions-toolbar {
        margin-top: @md;
        height: auto;
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        margin-bottom: 0 !important;
        @media screen and (min-width: @screen__s) {
            margin-top: 0;
        }

        > a {
            margin-right: @base !important;
            margin-bottom: 0 !important;
            padding: 0;
            width: 60px;
            height: 60px;

            span {
                &:extend(.sr-only);
            }
        }
    }

    .action.clear {
        width: 60px;
        height: 60px;
        padding: 0 !important;
    }

    .action.update {
        width: 60px;
        height: 60px;
        margin-left: 0 !important;
        padding: 0 !important;

        &:before {
            @media screen and (min-width: @screen__m) {
                padding-left: @md;
            }
        }
    }
}

.cart-summary {
    @media screen and (min-width: @screen__l) {
        &:before {
            bottom: 25px;
            content: '';
            display: block;
            left: 0;
            position: absolute;
            right: 0;
            top: 0;
            z-index: -1;
        }
    }
    @media screen and (min-width: @screen__l) {
        border: 1px solid #E1E0E0;
        box-shadow: 1px 7px 10px 0 rgba(0, 0, 0, 0.09);
        padding: 0.938rem;
    }

    .title {
        padding-bottom: @base;

        strong {
            padding-right: 3rem;
        }
    }

    .fieldset.coupon {
        border-bottom: 1px solid grey;
        margin: 0;
    }

    .cart-totals .table-wrapper {
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        font-size: var(--fs-xs);

        table {
            display: block;

            caption {
                text-align: left;
                padding: @s 0;
                display: block;
            }

            tbody {
                display: block;
            }

            tr {
                display: flex;
                justify-content: space-between;
            }

            th {
                padding-left: 0;
                text-align: left;
            }
        }
    }

    @media screen and (max-width: @screen__m) {
        position: static !important;
        width: 100% !important;
    }

    @media screen and (max-width: @screen__l) {
        padding: 0;
    }

    .radio {
        margin-top: @base;
        opacity: 0;
        z-index: -1;
    }

    .label {
        &:before {
            top: 0.25rem;
        }
    }

    .block {
        > .title {
            padding: @s 0;
        }

        .fieldset {
            margin: 0;
        }
    }

    #shipping-zip-form {
        select {
            font-size: var(--fs-xss);
        }
    }
}

.cart.main.actions {
    @media screen and (max-width: @screen__m) {
        display: flex;
    }
}

.checkout-methods-items {
    padding-left: 0;
}

.cart-container .form-cart {
    @media screen and (max-width: @screen__m) {
        width: 100% !important;
    }
}

#shopping-cart-table {
    @media screen and (max-width: @screen__m) {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
    }

    .item-options {
        @media screen and (max-width: @screen__m) {
            margin: 0;
            > dt, > dd {
                margin: 0;
            }
        }
    }

    .table-caption {
        &:extend(.sr-only all);
    }

    th.col {
        padding-bottom: 1rem;
        padding-top: 0;
        padding-left: 1.5rem;

        &:first-child {
            padding-left: 0;
        }

        &.price, &.qty, &.subtotal {
            @media screen and (min-width: @screen__m) {
                width: 120px;
            }
        }
    }

    .item-info {
        @media screen and (max-width: @screen__m) {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            grid-gap: 1rem;
        }
        @media screen and (max-width: @screen__s) {
            display: grid;
            grid-template-columns: 1fr;

            > td {
                display: flex;

                &:before {
                    padding: 0;
                }

                span {
                    line-height: inherit;
                }
            }

            .col.price {
                span {
                    padding-left: @s;
                }
            }

            .col.qty {
                .qty {
                    margin: 0;
                    padding-left: @s;
                    height: 2.5rem;
                }
            }

            .col.subtotal {
                span {
                    padding-left: @s;
                }
            }
        }
    }
}

.block.crosssell {
    .product-list {
        &__info {
            @media screen and (max-width: @screen__xs) {
                width: 100%;
            }
        }
    }
}
