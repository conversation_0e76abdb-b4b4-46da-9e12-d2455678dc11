//
//  Variables
//  _____________________________________________

@form-field__vertical-indent__desktop: 29px;
@form-calendar-icon__color: @primary__color;

///////////////////////////
// FORM RESET
// To set basic styles for input fields

@input-background-color--disabled: var(--c-gray-light);
@input-background-color: transparent;
@input-color: @color-gray20;
@input-placeholder-color: @primary__color;
@input-border: 1px solid @primary__color;
@input-border-radius: 0;
@input-font-size: @font-size__s;
@input-line-height: 1rem;
@letter-spacing: @letter-spacing__base;

input {
    background-clip: padding-box;
    background-color: @input-background-color;
    border: @input-border;
    border-radius: @input-border-radius;
    display: block;
    font-size: @input-font-size;
    line-height: @input-line-height;
    padding: 1rem;
    width: 100%;

    &:focus {

    }

    &::-ms-expand { // sass-lint:disable-line no-vendor-prefixes
        background-color: transparent;
        border: 0;
    }

    &::placeholder {
        color: @input-placeholder-color;
    }

    &:disabled,
    &[readonly] {
        background-color: @input-background-color--disabled;
    }
}

select {
    appearance: none;
    vertical-align: middle;

    &:focus::-ms-value { // sass-lint:disable-line no-vendor-prefixes
        background-color: @input-background-color;
        color: @input-color;
    }

    &::-ms-expand { // sass-lint:disable-line no-vendor-prefixes
        display: none;
    }
}

textarea {

}

.form {
    &__group {

    }
}

.control {
    &--inline {

        > div:not(:last-child) {

        }
    }
}
