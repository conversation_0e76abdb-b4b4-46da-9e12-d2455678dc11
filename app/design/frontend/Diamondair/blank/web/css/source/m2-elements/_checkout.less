.checkout-index-index {
    .nav-main {
        display: none; //ToDo: Navigation should be removed from checkout page with layout as soon as it is finished
    }

    button.action.action-edit-address {
        &:extend(.button-actions all);
        font-size: var(--fs-xs);
    }
}

.checkout-container {
    .container(@layout__l-max-width);
}

.opc-wrapper {
    margin-top: @teaser-md;

    ol {
        @media screen and (max-width: @screen__m) {
            padding: 0;
        }
    }

    .action.login {
        margin-bottom: 0;
    }

    .table-checkout-shipping-method {
        width: 100%;

        td {
            padding: 0.625rem;
        }

        input {
            position: static;
            @media screen and (max-width: @screen__s) {
                width: 100%;
            }
        }
    }

    .step-title {
        &:extend(.h3 all);
        border-bottom: 0;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .field-select-billing {
        label {
            font-weight: var(--fw-bold) !important;
            overflow: visible !important;
            position: static !important;
        }
    }

    .field {
        .control {
            @media screen and (min-width: @screen__l) {
                width: 680px;
            }

            &._with-tooltip input {
                @media screen and (min-width: @screen__l) {
                    width: calc(~'100% -' 45px);
                }
                margin-bottom: 0.75rem;
            }
        }

        .note {
            border-bottom: 1px solid @c-gray-light;
            padding-bottom: 2.188rem;
        }

        .label {
            padding-bottom: 0.625rem !important;
        }

        input {
            margin-bottom: @base;
            @media screen and (min-width: @screen__m) {
                margin-bottom: 1.875rem;
            }
        }
    }

    .actions-toolbar {
        justify-content: flex-end;
        max-width: 680px;
    }

    .shipping-address-items {
        display: block;
        @media screen and (min-width: @screen__m) {
            display: flex;
            justify-content: space-between;
        }

        .shipping-address-item {
            width: 100%;
            padding: @md;

            &:before {
                display: none;
            }

            .action.edit-address-link {
                &:extend(.button-actions all);
                font-size: var(--fs-xs);
                width: 100%;
                @media screen and (max-width: @screen__mmax) {
                    position: initial;
                }
            }

            .action-select-shipping-item {
                &:extend(.button-actions all);
                float: initial;
                font-size: var(--fs-xs);
                width: 100%;
            }
        }
    }
}

.field[name="shippingAddress.region_id"],
.field[name="shippingAddress.country_id"],
.field[name="billingAddresscheckmo.country_id"],
.field[name="billingAddresscheckmo.region_id"],
.field.country,
.field.region,
.field.gender {
    .control {
        &:extend(.select-arrow all);
    }
}

.opc-payment {
    .actions-toolbar {
        width: 100%;
        max-width: initial;
    }
}

.opc-sidebar {
    @media screen and (min-width: @screen__l) {
        max-width: 330px;
        margin-top: 3.438rem !important;
    }
}

.minicart-wrapper {
    @media screen and (min-width: @screen__m) {
        display: flex;
    }

    .counter-number {
        font-size: 1.125rem;
    }
}

.billing-address-same-as-shipping-block > .label {
    text-transform: none;
}

.billing-address-details {
    font-size: var(--fs-xs);
}

.checkout-agreements-block {
    font-size: var(--fs-base);
    padding: 1rem 0;
    text-align: left;

    .action-show {
        span {
            color: var(--c-primary);
        }

        &:hover {
            span {
                text-decoration: underline;
            }
        }
    }
}

.checkout-agreement.field.choice {
    .label {
        &:before {
            top: 4px;
        }

        &:after {
            top: 5px;
        }
    }
}

.checkout-agreement, .checkout-billing-address {
    position: relative;

    > input {
        height: 12px;
        left: 0;
        position: absolute;
        top: 6px;
        width: 12px;
    }

    > label {
        span {
            display: block;
            text-align: left;
            text-transform: initial;
            @media screen and (max-width: @screen__s) {
                font-size: var(--fs-xs);
            }
        }
    }

    .action-update {
        &:extend(.btn all);
        &:extend(.btn--primary all);
    }

    .action-cancel {
        margin-left: 0.188rem !important;
        margin-top: 0 !important;
    }
}

.field-tooltip-action {
    left: -25px;
    top: 7px;
    position: absolute;
}

.action.showcart {
    background: none;
    border: none;
    color: @c-primary;
    padding-bottom: 0;
    padding-top: 0;
}

.action.checkout {
    float: none;
}

.action.action-edit-address {
    margin-top: @lg;
}

.action-auth-toggle {
    &:extend(.btn all);
    &:extend(.btn--primary all);
}

.block-customer-login {
    .action.action-login.secondary {
        &:extend(.btn all);
        &:extend(.btn--primary all);
    }

    .actions-toolbar {
        @media screen and (min-width: @screen__s) {
            display: flex;
            justify-content: space-between;
        }
    }
}

.form-discount {
    max-width: 100% !important;

    .action-apply {
        &:extend(.btn all);
        &:extend(.btn--primary all);
    }
}

.payment-method {
    margin-top: 1.875rem;
    @media screen and (max-width: @screen__m) {
        padding: 0.938rem;
    }

    .field.choice {
        padding-top: 0.188rem;
    }

    .payment-method-title {
        border-top: none !important;

        .label {
            text-transform: none;

            &:before {
                top: 4px;
            }

            &:after {
                top: 5px;
            }
        }
    }
}


#payment_iframe {
    height: 440px !important;
    margin-top: 40px;
}


body.hgw-index-index #pay-now {
    padding: 14px 17px;
    margin-bottom: 80px;
    margin-left: 0 !important;
    background-color: var(--c-primary);
    color: var(--c-white);
    text-transform: uppercase;
    border: none;
    cursor: pointer;
}

body.hgw-index-index #pay-now:hover {
    background-color: var(--c-blue-darkest);
}

.checkout-onepage-success {
    .column.main {
        a.action.primary {
            margin: @md 0;
            display: inline-block;
        }
    }

    .heidelpay-additional-payment-information {
        margin-top: 0.938rem;
    }
}

.opc-estimated-wrapper {
    .counter-label {
        text-transform: initial !important;
    }

    .action.showcart {
        @media screen and (max-width: @screen__s) {
            padding: 0;
            width: 100%;
            text-align: left;
        }
    }
}

.print-order-link {
    margin: @md 0;

    a.action.primary {
        color: var(--c-white);
    }
}

.checkout-agreements-block .checkout-agreement.field.required .action-show::after {
    display:none;
}
