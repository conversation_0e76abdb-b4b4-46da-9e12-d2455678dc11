.header {
    @media screen and (min-width: @screen__m) {
        &:not(.header-transparent) {
            .shadow-xs();
        }
    }

    &.header-transparent {
        left: 0;
        position: absolute;
        width: 100%;
        z-index: 1;
    }

    &__container {
        margin-bottom: 0;
    }

    &__container {
        .container(@layout__max-width);

        align-items: start;
        display: grid;
        grid-auto-columns: auto 1fr auto;
        grid-template-areas: 'logo meta toggle' 'col col col';
        padding-left: @layout-indent__width / 2;
        padding-right: @layout-indent__width / 2;
        position: relative;

        @media screen and (min-width: @screen__m) {
            grid-auto-columns: auto 1fr;
            grid-template-areas: 'logo meta' 'logo col';
            padding-left: @layout-indent__width;
            padding-right: @layout-indent__width;
            padding-top: @md;
        }
    }

    @-moz-document url-prefix() {
        &__container {
            display: flex;
            @media screen and (min-width: @screen__m) {
                display: grid;
                grid-auto-columns: 1fr;
            }
        }
    }

    &__dropdown {
        grid-area: col;

        @media screen and (max-width: @screen__mmax) {
            background: var(--c-white);
            left: 0;
            overflow: hidden;
            padding: @base 0 @base;
            position: absolute;
            top: 0;
            transform: translate(0, 0);
            width: 100%;
            z-index: 3;


            &:not(.is--active) {
                display: none;
                transform: translate(100%, 0);
            }
        }
    }

    @-moz-document url-prefix() {
        &__dropdown {
            @media screen and (max-width: @screen__mmax) {
                top: 45px;
            }
        }
    }

    &__meta {
        align-items: flex-end;
        display: flex;
        flex-wrap: wrap;
        grid-area: meta;
        justify-content: flex-end;
        margin-left: -0.25rem;
        margin-right: -0.25rem;

        @media screen and (min-width: @screen__m) {
            justify-content: flex-end;
            margin-left: -0.45rem;
            margin-right: -0.45rem;
            padding-bottom: 1rem;
        }

        > div {
            padding: 0.4rem 0.5rem;
            position: relative;


            @media screen and (max-width: @screen__mmax) {
                align-items: center;
                display: flex;
                height: 39px;
            }

            @media screen and (min-width: @screen__m) {
                padding: 0 0.9rem;
            }

            &:not(:first-child) {
                &::before {
                    background-color: var(--c-gray-light);
                    content: '';
                    height: 24px;
                    left: 0;
                    position: absolute;
                    top: 50%;
                    transform: translate(0, -50%);
                    width: 1px;

                    .header-transparent & {
                        background-color: rgba(255, 255, 255, 0.4);
                    }

                    .checkout-index-index & {
                        display: none;
                    }
                }
            }
        }
    }
}
