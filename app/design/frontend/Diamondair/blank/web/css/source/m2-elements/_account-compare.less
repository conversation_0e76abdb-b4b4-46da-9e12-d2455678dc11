//
//  Compare Products Page
//  Basic style rewrite from luma
//  -----------------------------------------

body.catalog-product-compare-index {
    .action.print {
        float: right;
        margin: 15px 0;
    }
}

.table-wrapper.comparison {
    clear: both;
    max-width: 100%;
    overflow-x: scroll;
    overflow-y: scroll;
    position: relative; // Needed for Safari(iOS) to properly render 'overflow-x' rule.

    .table-comparison > tbody > tr {
        > th,
        > td {
            border-top: 0;
        }
    }
}

.table-comparison {
    table-layout: fixed;

    .cell {
        padding: 15px;
        width: 180px;

        &.label {
            border-right: 1px solid grey;

            &.remove,
            &.product {
                span {
                    &:extend(.abs-visually-hidden all);
                }
            }

            .attribute.label {
                display: block;
                width: 100%;
                word-wrap: break-word;
            }
        }

        &.product {
            &.info,
            &.label {
                border-bottom: 1px solid grey;
            }
        }

        &.attribute {
            font-size: var(--fs-xs);

            img {
                height: auto;
                max-width: 100%;
            }
        }

        &.remove {
            padding-bottom: 0;
            padding-top: 0;
            text-align: right;

            .action.delete {
                &:extend(.abs-remove-button-for-blocks all);
                padding: 7px;
                display: block;
                max-width: 142px;
                &:extend(.link-primary all);
            }
        }

        .attribute.value {
            overflow: hidden;
            width: 100%;
        }
    }

    td {
        &:last-child {
            border-right: 1px solid grey;
        }
    }

    .product-item-photo {
        display: block;
        margin: 0 auto 15px;
    }

    .product-image-photo {
        margin-left: 0;
    }

    .product-item-actions,
    .price-box,
    .product.rating,
    .product-item-name {
        display: block;
        margin: 15px 0;
    }

    .product-addto-links {
        margin-top: 15px;

        .action {
            &.toggle {
                padding: 0;
            }

            &.split,
            &.toggle {

            }
        }
    }

    .action {
        &.tocart {
            white-space: nowrap;
        }
    }

    .actions-secondary {
        a {
            &:extend(.link-primary all);
        }
    }
}

.comparison.headings {
    background: var(--c-white);
    left: 0;
    position: absolute;
    top: 0;
    width: auto;
    z-index: 2;
}

.block-compare {
    .block-title {
        &:extend(.abs-block-widget-title all);
    }

    .product-item .product-item-name {
        margin-left: 22px;
    }

    .action.delete {
        &:extend(.abs-remove-button-for-blocks all);
        left: 0;
        position: absolute;
        top: 0;
    }

    .counter {
        &:extend(.abs-block-items-counter all);
    }

    .actions-toolbar {
        margin: 17px 0 0;
    }

    .action.primary {
        &:extend(.abs-revert-to-action-secondary all);
    }
}


//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .table-wrapper.comparison {
        .table-comparison > tbody > tr {
            > th,
            > td {
                display: table-cell;
                &:extend(.abs-col-no-prefix all);
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .compare.wrapper,
    [class*='block-compare'] {
        display: none;
    }
    .catalog-product_compare-index {
        .columns {
            .column {
                &.main {
                    flex-basis: inherit;
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .compare.wrapper {
        float: right;
        .lib-list-reset-styles();

        .action.compare {
            line-height: @form-element-input__height;
            .lib-link(
                @_link-color: @primary__color,
                @_link-text-decoration: none,
                @_link-color-visited: @primary__color,
                @_link-text-decoration-visited: none,
                @_link-color-hover: @primary__color,
                @_link-text-decoration-hover: underline,
                @_link-color-active: @primary__color,
                @_link-text-decoration-active: underline
            );
        }

        .counter.qty {
            .lib-css(color, @primary__color__lighter);

            &:before {
                content: '(';
            }

            &:after {
                content: ')';
            }
        }
    }
}
