
@baseDir: "../"; // Base directory path
//
//  Theme Colors
//  ---------------------------------------------
@c-base: #000000;
@c-primary: #00467e;
@c-secondary: #b62525;
@c-gray: #424241;
@c-gray-light: #bdc1c4;
@c-gray-lightest: #ecedee;
@c-white: #ffffff;


@c-yellow-dark: #ffd032;
@c-yellow-light: #ffe0a1;
@c-blue-dark: @c-primary;
@c-blue-darkest: #023157;
@c-blue-light: #6192b5;
@c-green-dark: #175d3b;
@c-green-light: #b2d4bb;
@c-purple-dark: #6a3686;
@c-purple-light: #c2bcd7;
@c-red: @c-secondary;
@c-link-transparent: transparent;

//
//  Color nesting
//  ---------------------------------------------

@color-gray20: @c-base;
@color-gray91: @c-gray-light;
@color-white: @c-white;
@color-orange-red1: @c-primary;
@color-red10: @c-red;
@color-blue1: @c-primary;
@color-blue2: @c-primary;


@primary__color: @color-gray20;
@primary__color__dark: darken(@primary__color, 35%);
@primary__color__darker: darken(@primary__color, 13.5%);
@primary__color__lighter: lighten(@primary__color, 29%);
@primary__color__light: lighten(@primary__color, 45%);

@secondary__color: @color-gray91;
@secondary__color__light: lighten(@secondary__color, 5%);

@page__background-color: @color-white;
@panel__background-color: darken(@page__background-color, 6%);

@active__color: @color-orange-red1;
@error__color: @color-red10;

@color-sky-blue1: #68a8e0;


@font-size__base: 1.125rem; // 18px
@font-size__xl: 3.375rem; // 54px
@font-size__l: 2.5rem; // 40px
@font-size__m: 1.9375rem; // 31px
@font-size__s: 1.375rem; // 22px
@font-size__xs: 1rem; // 16px
@font-size__xss: 0.875rem; // 14px

//  Weights
@font-weight__thin: 200;
@font-weight__light: 300;
@font-weight__regular: 400;
@font-weight__semibold: 500;
@font-weight__bold: 700;

//  Styles
@font-style__base: normal;
@font-style__emphasis: italic;

//  Line heights
@line-height__base: 1.45;
@line-height__computed: 1.5;
@line-height__xl: 1.85;
@line-height__l: 1.4;
@line-height__s: 1;

// spaces

@xs: 0.25rem;
@s: 0.5rem;
@sm: 0.75rem;
@base: 1rem;
@md: 1.25rem;
@lg: 1.675rem;
@xl: 2.25rem; // 36px


@teaser-m: 2.75rem;
@teaser-md: 3rem;
@teaser-lg: 3.5rem;
@teaser-xl: 4.5rem;
@teaser-xxl: 6.5rem;

@padding-primary: @xl 0;
@padding-primary-mobile: @base 0;
@padding-secondary: @teaser-m 0 @teaser-m 0;
@padding-secondary-mobile: @base 0 @base 0;

//
//  Links
//  ---------------------------------------------

@link__color: @color-blue1;
@link__text-decoration: none;

@link__visited__color: @link__color;
@link__visited__text-decoration: none;

@link__hover__color: @color-blue2;
@link__hover__text-decoration: underline;

@link__active__color: @active__color;
@link__active__text-decoration: underline;

:root {
    --c-white: @c-white;
    --c-base: @c-base;
    --c-primary: @c-primary;
    --c-secondary: @c-secondary;
    --c-yellow-dark: @c-yellow-dark;
    --c-yellow-light: @c-yellow-light;
    --c-blue-dark: @c-blue-dark;
    --c-blue-darkest: @c-blue-darkest;
    --c-blue-light: @c-blue-light;
    --c-green-dark: @c-green-dark;
    --c-green-light: @c-green-light;
    --c-purple-dark: @c-purple-dark;
    --c-purple-light: @c-purple-light;
    --c-red: @c-red;
    --c-gray: @c-gray;
    --c-gray-light: @c-gray-light;
    --c-gray-lightest: @c-gray-lightest;
    --c-primary__color: @primary__color;
    --c-primary__color__dark: @primary__color__dark;
    --c-primary__color__darker: @primary__color__darker;
    --c-primary__color__lighter: @primary__color__lighter;
    --c-primary__color__light: @primary__color__light;
    --c-secondary__color: @secondary__color;
    --c-secondary__color__light: @secondary__color__light;
    --c-page__background-color: @page__background-color;
    --c-panel__background-color: @panel__background-color;
    --c-active__color: @active__color;
    --c-error__color: @error__color;
    --c-link-transparent: @c-link-transparent;
    --c-link: @c-base;
    --c-link-bg: @c-yellow-light;
    --c-link-hover: @c-gray;
    --fs-base: @font-size__base;
    --fs-xl: @font-size__xl;
    --fs-l: @font-size__l;
    --fs-m: @font-size__m;
    --fs-s: @font-size__s;
    --fs-xs: @font-size__xs;
    --fw-thin: @font-weight__thin;
    --fs-xss: @font-size__xss;
    --fw-light: @font-weight__light;
    --fw-normal: @font-weight__regular;
    --fw-semibold: @font-weight__semibold;
    --fw-bold: @font-weight__bold;
    --lh-base: @line-height__base;
    --lh-computed: @line-height__computed;
    --lh-xl: @line-height__xl;
    --lh-l: @line-height__l;
    --lh-s: @line-height__s;
}


//
//  Widths
//  ---------------------------------------------
@layout__width: ''; // for the fixed width layout
@layout__max-width: 1400px;
@layout__l-max-width: 1168px;
@layout__m-max-width: 940px;
@layout__s-max-width: 703px;
@layout-indent__width: 10px;
@layout__width-xs-indent: 10px;

//
// Breakpoints
//  ---------------------------------------------
@screen__xxs: 370px;
@screen__xs: 480px;
@screen__s: 640px;
@screen__m: 768px;
@screen__mmax: 767px;
@screen__l: 1024px;
@screen__l: 1024px;
@screen__lmax: 1023px;
@screen__lg: 1260px;
@screen__xl: 1400px;
@screen__xxl: 1600px;

/*
// Media Queries Example
//  ---------------------------------------------
@media screen and (max-width: @screen__l - 1) {
    your style
}
@media screen and (min-width: @screen__l) {
    your style
}
*/

//
//  Variables for icons-blank-theme
//  ---------------------------------------------

@icons-blank-theme: 'icons-blank-theme';

@icon-wishlist-full: '\e600';
@icon-wishlist-empty: '\e601';
@icon-warning: '\e602';
@icon-update: '\e603';
@icon-trash: '\e604';
@icon-star: '\e605';
@icon-settings: '\e606';
@icon-pointer-down: '\e607';
@icon-next: '\e608';
@icon-menu: '\e609';
@icon-location: '\e60a';
@icon-list: '\e60b';
@icon-info: '\e60c';
@icon-grid: '\e60d';
@icon-comment-reflected: '\e60e';
@icon-collapse: '\e60f';
@icon-checkmark: '\e610';
@icon-cart: '\e611';
@icon-calendar: '\e612';
@icon-arrow-up: '\e613';
@icon-arrow-down: '\e614';
@icon-search: '\e615';
@icon-remove: '\e616';
@icon-prev: '\e617';
@icon-pointer-up: '\e618';
@icon-pointer-right: '\e619';
@icon-pointer-left: '\e61a';
@icon-flag: '\e61b';
@icon-expand: '\e61c';
@icon-envelope: '\e61d';
@icon-compare-full: '\e61e';
@icon-compare-empty: '\e61f';
@icon-comment: '\e620';
@icon-up: '\e621';
@icon-down: '\e622';
@icon-help: '\e623';
@icon-arrow-right-thin: '\e624';
@icon-arrow-left-thin: '\e625';
@icon-arrow-down-thin: '\e626';
@icon-account: '\e627';
@icon-gift-registry: '\e628';
@icon-present: '\e629';
@icon-arrow-up-thin: '\e633';


//
//  Variables for diamond icons
//  ---------------------------------------------


@i-burger_close: '\e800';
@i-burger: '\e801';
@i-cart: '\e802';
@i-check: '\e803';
@i-chevron-cirlce: '\e804';
@i-chevron-down: '\e805';
@i-chevron-left: '\e806';
@i-chevron-right: '\e807';
@i-chevron-up: '\e808';
@i-dot: '\e809';
@i-edit: '\e80a';
@i-facebook: '\e80b';
@i-favourite: '\e80c';
@i-help: '\e80d';
@i-instagram: '\e80e';
@i-login: '\e80f';
@i-marker: '\e810';
@i-search: '\e811';
@i-top: '\e812';
@i-trash: '\e813';
@i-video: '\e814';
@i-youtube: '\e815';
@i-twitter: '\f099';
@i-circle-thin: '\f1db';


// Letter spacing
@letter-spacing__base: 0.225rem;
@letter-spacing_xl: 0.625rem;
@letter-spacing_l: 0.4375rem;
@letter-spacing_m: 0.332rem;
@letter-spacing_s: 0.15875rem;

//  Colors
//
//  Default blank Theme Color Overrides
//  ---------------------------------------------


@text__color: @primary__color;
@text__color__intense: @primary__color__darker;
@text__color__muted: @primary__color__lighter;

//
//  Typography variables
//  _____________________________________________

//
//  Fonts
//  ---------------------------------------------
//  Path
@icons__font-path: "../fonts/Blank-Theme-Icons/Blank-Theme-Icons";
@icons__font-path-custom: "../fonts/diamond-icons";

//  Names
@icons__font-name: 'icons-blank-theme'; // ToDo UI: we need to rename (it shouldn't use blank theme name) or move icon fonts to blank theme
@icons__font-name-custom: 'diamond-icons'; // ToDo UI: we need to rename (it shouldn't use blank theme name) or move icon fonts to blank theme


//  Font families
@font-family-name__base: 'Univers';

@font-family__sans-serif: @font-family-name__base, 'Helvetica Neue', Helvetica, Arial, sans-serif;
@font-family__serif: Georgia, 'Times New Roman', Times, serif;
@font-family__monospace: Menlo, Monaco, Consolas, 'Courier New', monospace;

@font-family__base: @font-family__sans-serif;

@font-family__heidelpay: 'Helvetica Neue', Helvetica, Arial, sans-serif;

//  Sizes
@root__font-size: 16px; // Defines ratio between root font size and base font size, 1rem = 10px
@font-size-ratio__base: 1.4; // Defines ratio of the root font-size to the base font-size

@font-size-unit: rem; // The unit to which most typography values will be converted by default
@font-size-unit-ratio: unit(@root__font-size * 16/100); // Ratio of the root font-size to the font-size unit
@font-size-unit-convert: true; // Controls whether font-size values are converted to the specified font-size unit


//
//  Indents
//  ---------------------------------------------
@indent__base: 1.25rem; // 20px
@indent__xl: @indent__base; // 40px
@indent__l: @indent__base; // 30px
@indent__m: @indent__base; // 25px
@indent__s: @indent__base; // 10px
@indent__xs: @indent__base; // 5px


//
//  Borders
//  ---------------------------------------------

@border-color__base: darken(@page__background-color, 18%);
@border-width__base: 1px;

//
//  Focus
//  ---------------------------------------------

@focus__color: @color-sky-blue1;
@focus__box-shadow: 0 0 3px 1px @focus__color;

//
//  Lists
//  ---------------------------------------------

@list__color__base: false;
@list__font-size__base: false;
@list__margin-top: 0;
@list__margin-bottom: @indent__m;

@list-item__margin-top: 0;
@list-item__margin-bottom: @indent__s;

@dl__margin-top: 0;
@dl__margin-bottom: @indent__base;

@dt__margin-top: 0;
@dt__margin-bottom: @indent__xs;
@dt__font-weight: @font-weight__bold;

@dd__margin-top: 0;
@dd__margin-bottom: @indent__s;

//
//  Paragraphs
//  ---------------------------------------------

@p__margin-top: 0;
@p__margin-bottom: @indent__s;

//
//  Headings
//  ---------------------------------------------

@heading__font-family__base: false;
@heading__font-weight__base: @font-weight__bold;
@heading__line-height__base: 1.2;
@heading__color__base: false;
@heading__font-style__base: false;
@heading__margin-top__base: 0;
@heading__margin-bottom__base: @indent__base;

@h1__font-size: @font-size__l; // 26px
@h1__margin-top: 0;
@h1__margin-bottom: @heading__margin-bottom__base;
@h1__font-size-desktop: @font-size__xl; // 40px

@h2__font-size: @font-size__l;
@h2__margin-top: 0;
@h2__margin-bottom: @heading__margin-bottom__base;

@h3__font-size: @font-size__m; // 18px
@h3__margin-top: 0;
@h3__margin-bottom: @heading__margin-bottom__base;

@h4__font-size: @font-size__base; // 14px
@h4__font-weight: @font-weight__bold;
@h4__margin-top: 0;
@h4__margin-bottom: @heading__margin-bottom__base;

@h5__font-size: @font-size__s; // 14px
@h5__font-weight: @font-weight__bold;
@h5__margin-top: 0;
@h5__margin-bottom: @heading__margin-bottom__base;


@h6__font-size: @font-size__xs; // 14px
@h6__font-weight: @font-weight__bold;
@h6__margin-top: 0;
@h6__margin-bottom: @heading__margin-bottom__base;


@heading__small-color: @primary__color;
@heading__small-line-height: 1;
@heading__small-size: (@font-size__xs/@font-size__base) * 100%;


//  Blockquote
@blockquote__border-color: @border-color__base;
@blockquote__border-width: 0;
@blockquote__content-before: '\2014 \00A0';
@blockquote__font-size: @font-size__base;
@blockquote__font-style: @font-style__emphasis;
@blockquote__margin: 0 0 @indent__base @indent__xl;
@blockquote__padding: 0;

@blockquote-small__color: @primary__color;
@blockquote-small__font-size: @font-size__xs;

@cite__font-style: @font-style__base;


//  Misc
@hr__border-color: @border-color__base;
@hr__border-style: solid;
@hr__border-width: @border-width__base;

@mark__color: @primary__color__dark;
@mark__background-color: @panel__background-color;

@abbr__border-color: @border-color__base;

//  Disable filters output in css
@disable-filters: false;
@sidebar__background-color: @c-white;
