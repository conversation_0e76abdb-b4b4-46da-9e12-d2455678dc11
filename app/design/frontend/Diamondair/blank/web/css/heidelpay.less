/*
* Static Styles for the Heidelpay IFrame Credit Card Payment Form
* Dont change the URL, the styles are referenced by an absolute path
* See Magento Configuration > Payment Methods > Heidelpay > Settings > Iframe CSS
*/

// variables
@import 'source/_variables_extend.less';

html,
body {
    font-family: @font-family__heidelpay;
    font-size: @root__font-size;
    color: var(--c-primary__color);
    letter-spacing: 1.01px;
}

select,
input {
    padding: 0px 10px 0px 10px;
    margin: 0 0 25px 0;
    height: 55px;
    font-family: @font-family__heidelpay;
    font-size: @root__font-size;
    color: var(--c-primary__color);
    border: 1px solid var(--c-gray-light);
}

input {
    text-indent: 0px;
}

.form-label {
    margin-bottom: 12px;
    text-transform: uppercase;
}

.form-input,
.form-select {
    margin-bottom: 25px;
    padding-right: 10px;
}

.error{
    background-color: var(--c-white);
    border-color: var(--c-red);
}
