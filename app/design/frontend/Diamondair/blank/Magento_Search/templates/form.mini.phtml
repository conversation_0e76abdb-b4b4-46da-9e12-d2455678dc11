<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
?>
<?php
/** @var $block \Magento\Framework\View\Element\Template */
/** @var $helper \Magento\Search\Helper\Data */
$helper = $this->helper(\Magento\Search\Helper\Data::class);
?>
<div class="block block-search" data-toggle-animate>
    <div class="block block-content">
        <form class="form minisearch" id="search_mini_form" action="<?= $block->escapeUrl($helper->getResultUrl()) ?>" method="get">
            <div class="field search nav-search">
                <label class="label sr-only" for="search" data-role="minisearch-label">
                    <span><?= $block->escapeHtml(__('Search')) ?></span>
                </label>
                <div class="control">
                    <input id="search"
                           data-mage-init='{"quickSearch":{
                                "formSelector":"#search_mini_form",
                                "url":"<?= $block->escapeUrl($helper->getSuggestUrl())?>",
                                "destinationSelector":"#search_autocomplete",
                                "minSearchLength":"<?= $block->escapeHtml($helper->getMinQueryLength()) ?>"}
                           }'
                           class="nav-search__input"
                           type="text"
                           name="<?= $block->escapeHtmlAttr($helper->getQueryParamName()) ?>"
                           value="<?= $block->escapeHtmlAttr($helper->getEscapedQueryText()) ?>"
                           placeholder="<?= $block->escapeHtmlAttr(__('Search')) ?>"
                           class="input-text"
                           maxlength="<?= $block->escapeHtmlAttr($helper->getMaxQueryLength()) ?>"
                           role="combobox"
                           aria-haspopup="false"
                           aria-autocomplete="both"
                           autocomplete="off"
                           aria-expanded="false"/>
                    <div id="search_autocomplete" class="search-autocomplete"></div>
                </div>
            </div>
            <div class="actions">
                <button type="submit"
                    title="<?= $block->escapeHtmlAttr(__('Search')) ?>"
                    class="action search"
                    aria-label="Search"
                >
                    <span class="sr-only"><?= $block->escapeHtml(__('Search')) ?></span>
                </button>
            </div>
        </form>
    </div>
</div>
