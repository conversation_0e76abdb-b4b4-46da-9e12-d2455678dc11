//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .fotorama-video-container {
        &:after {
            background: url('@{baseDir}images/gallery-sprite.png') -50px -315px;
            bottom: 0;
            content: '';
            height: 130px;
            left: 0;
            margin: auto;
            position: absolute;
            right: 0;
            top: 10px;
            width: 100px;
        }

        .magnify-lens {
            display: none !important;
        }

        &.video-unplayed {
            &:hover {
                img {
                    opacity: .6;
                }

                &:after {
                    transform: scale(1.25);
                }
            }
        }
    }

    .video-thumb-icon:after {
        background: url('@{baseDir}images/gallery-sprite.png')  5px 38px;
        bottom: 0;
        content: '';
        height: 40px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 10px;
        width: 50px;
    }

    .product-video {
        bottom: 0;
        height: 75%;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 0;
        width: 100%;

        iframe {
            height: 100%;
            left: 0;
            position: absolute;
            top: 0;
            width: 100%;
            z-index: 9999;
        }
    }

    .fotorama__stage__shaft:focus .fotorama__stage__frame.fotorama__active:after {
        bottom: 0;
        content: '';
        height: 100px;
        left: 0;
        margin: auto;
        position: absolute;
        right: 0;
        top: 12px;
        width: 100px;
    }

    .fotorama__product-video--loading {
        &:after {
            visibility: hidden;
        }
    }
}

//
//  Mobile
//  _____________________________________________

//  @TODO UI: check possibility to use .media-width() mixin
@media only screen
and (min-device-width: 320px)
and (max-device-width: 780px)
and (orientation: landscape) {
    .product-video {
        height: 100%;
        width: 81%;
    }
}
