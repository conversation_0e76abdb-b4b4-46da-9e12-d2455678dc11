<?php
/* @var \Magento\Wishlist\Block\Customer\Wishlist\Item\Column\Comment $block  */

/* @var \Magento\Wishlist\Model\Item $item */
$item = $block->getItem();
?>

<div class="field comment-box wishlist__comment-box">
    <label class="label wishlist__comment-label" for="product-item-comment-<?= $block->escapeHtmlAttr($item->getWishlistItemId()) ?>">
        <span><?= $block->escapeHtml(__('Comment')) ?></span>
    </label>
    <div class="control">
        <textarea id="product-item-comment-<?= $block->escapeHtmlAttr($item->getWishlistItemId()) ?>" placeholder="<?= /* @noEscape */ $this->helper(\Magento\Wishlist\Helper\Data::class)->defaultCommentString() ?>" name="description[<?= $block->escapeHtmlAttr($item->getWishlistItemId()) ?>]" title="<?= $block->escapeHtmlAttr(__('Comment')) ?>" class="product-item-comment wishlist__product-item-comment" <?= $item->getProduct()->isSaleable() ? '' : 'disabled="disabled"' ?>><?= ($block->escapeHtml($item->getDescription())) ?></textarea>
    </div>
</div>
