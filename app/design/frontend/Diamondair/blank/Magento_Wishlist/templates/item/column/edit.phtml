<?php
/** @var \Magento\Wishlist\Block\Customer\Wishlist\Item\Column\Edit $block */

/** @var \Magento\Wishlist\Model\Item $item */
$item = $block->getItem();
$product = $item->getProduct();
?>

<?php if ($product->isVisibleInSiteVisibility()) : ?>
    <a class="edit wishlist__edit" href="<?= $block->escapeUrl($block->getItemConfigureUrl($item)) ?>">
        <span><?= $block->escapeHtml(__('Edit')) ?></span>
    </a>
<?php endif ?>
