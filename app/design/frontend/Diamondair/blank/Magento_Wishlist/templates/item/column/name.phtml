<?php
/** @var \Magento\Wishlist\Block\Customer\Wishlist\Item\Column\Info $block */

/** @var \Magento\Wishlist\Model\Item $item */
$item = $block->getItem();
$product = $item->getProduct();
?>
<strong class="product-item-name wishlist__product-item-name">
    <a href="<?= $block->escapeUrl($block->getProductUrl($item)) ?>" title="<?= $block->escapeHtmlAttr($product->getName()) ?>" class="product-item-link wishlist__product-item-link">
        <?= $block->escapeHtml($product->getName()) ?>
    </a>
</strong>
