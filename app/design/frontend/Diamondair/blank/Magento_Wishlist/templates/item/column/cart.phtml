<?php
/** @var \Magento\Wishlist\Block\Customer\Wishlist\Item\Column\Cart $block */

/** @var \Magento\Wishlist\Model\Item $item */
$item = $block->getItem();
$product = $item->getProduct();
/** @var \Magento\Wishlist\ViewModel\AllowedQuantity $viewModel */
$viewModel = $block->getData('allowedQuantityViewModel');
$allowedQty = $viewModel->setItem($item)->getMinMaxQty();
?>
<?php foreach ($block->getChildNames() as $childName) : ?>
    <?= /* @noEscape */ $block->getLayout()->renderElement($childName, false) ?>
<?php endforeach;?>
<div class="box-tocart wishlist__box-tocart">
    <fieldset class="fieldset wishlist__fieldset">
    <?php if ($item->canHaveQty() && $product->isVisibleInSiteVisibility()) : ?>
        <div class="field qty wishlist__field-qty">
            <label class="label wishlist__field-qty-label" for="qty[<?= $block->escapeHtmlAttr($item->getId()) ?>]"><span><?= $block->escapeHtml(__('Qty')) ?></span></label>
            <div class="control wishlist__field-control">
                <input type="number" data-role="qty" id="qty[<?= $block->escapeHtmlAttr($item->getId()) ?>]" class="input-text qty wishlist__qty-input" data-validate="{'required-number':true,'validate-greater-than-zero':true, 'validate-item-quantity':{'minAllowed':<?= /* @noEscape */ $allowedQty['minAllowed'] ?>,'maxAllowed':<?= /* @noEscape */ $allowedQty['maxAllowed'] ?>}}"
               name="qty[<?= $block->escapeHtmlAttr($item->getId()) ?>]" value="<?= /* @noEscape */ $block->getAddToCartQty($item) * 1 ?>" <?= $product->isSaleable() ? '' : 'disabled="disabled"' ?>>
            </div>
        </div>
    <?php endif; ?>
    <?php if ($product->isSaleable()) : ?>
    <div class="product-item-actions wishlist__product-item-actions">
        <div class="actions-primary wishlist__actions-primary">
            <button type="button" data-role="tocart" data-post='<?= /* @noEscape */ $block->getItemAddToCartParams($item) ?>' title="<?= $block->escapeHtmlAttr(__('Add to Cart')) ?>" data-item-id="<?= $block->escapeHtmlAttr($item->getId()) ?>" class="action tocart primary wishlist__tocart">
                <span><?= $block->escapeHtml(__('Add to Cart')) ?></span>
            </button>
        </div>
    </div>
    <?php else : ?>
        <?php if ($product->getIsSalable()) : ?>
            <p class="available stock wishlist__available" title="<?= $block->escapeHtmlAttr(__('Availability')) ?>">
                <span><?= $block->escapeHtml(__('In stock')) ?></span>
            </p>
        <?php else : ?>
            <p class="unavailable stock wishlist__unavailable" title="<?= $block->escapeHtmlAttr(__('Availability')) ?>">
                <span><?= $block->escapeHtml(__('Out of stock')) ?></span>
            </p>
        <?php endif; ?>
    <?php endif; ?>
    </fieldset>
</div>
