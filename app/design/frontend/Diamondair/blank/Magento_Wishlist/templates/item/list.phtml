<?php
// @codingStandardsIgnoreFile
?>
<?php
/** @var \Magento\Wishlist\Block\Customer\Wishlist\Items $block */
$columns = $block->getColumns();
?>

<div class="products-grid wishlist">
    <?php $iterator = 1; ?>
    <?php if (count($block->getItems())): ?>
    <ol class="product-items wishlist__product-items">
        <?php foreach ($block->getItems() as $item): ?>
        <?php /* @escapeNotVerified */ echo($iterator++ == 1) ? '<li data-row="product-item" class="product-item wishlist__item" id="item_' . $item->getId() . '">' : '</li><li class="product-item wishlist__item" id="item_' . $item->getId() . '">' ?>
            <div class="product-item-info wishlist__product-item-info clearfix">
                <?php foreach ($columns as $column): ?>
                    <?php $column->setItem($item); echo $column->toHtml($item);?>
                <?php endforeach; ?>
            </div>
        <?php echo($iterator == count($block->getItems())+1) ? '</li>' : '' ?>
        <?php endforeach; ?>
    </ol>
    <?php else: ?>
        <div class="message info empty wishlist__empty-info">
            <span><?php /* @escapeNotVerified */ echo __('This Wish List has no Items');?></span>
        </div>
    <?php endif; ?>
</div>

<?php foreach ($columns as $column): ?>
    <?php echo $column->getAdditionalHtml();?>
<?php endforeach; ?>
