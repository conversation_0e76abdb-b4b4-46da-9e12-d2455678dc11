//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    .toolbar {
        &.wishlist-toolbar {
            .limiter {
                float: right;
            }

            .main .pages {
                display: inline-block;
                position: relative;
                z-index: 0;
            }

            .toolbar-amount,
            .limiter {
                display: inline-block;
                z-index: 1;
            }
        }
    }

    .form.wishlist.items {
        .actions-toolbar {
            &:extend(.abs-reset-left-margin all);
        }
    }


    .account .table-wrapper .data.table.wishlist {
        .lib-table-bordered(
            @_table_type: horizontal
        );

        thead > tr > th {
            border-bottom: 0;
        }

        tbody > tr:last-child > td {
            border-bottom: 1px solid @table__border-color;
        }

        .product.name {
            display: inline-block;
            margin-bottom: @indent__s;
        }

        .box-tocart {
            margin: @indent__s 0;

            .qty {
                &:extend(.abs-input-qty all);

                vertical-align: middle;
            }
        }

        .col {
            &.item {
                width: 50%;
            }

            &.photo {
                max-width: 150px;
            }

            &.selector {
                max-width: 15px;
            }
        }

        textarea {
            margin: @indent__s 0;
        }

        .input-text.qty {
            margin-bottom: @indent__s;
        }

        .action.primary {
            vertical-align: top;
        }

        .price {
            font-weight: @font-weight__bold;
        }
    }

    .block-wishlist {
        .block-title {
            &:extend(.abs-block-title all);
        }

        .counter {
            &:extend(.abs-block-items-counter all);
        }

        .product-item-name {
            margin-right: @indent__m;
        }
    }

    .products-grid.wishlist {
        .product {
            &-item {
                &-photo {
                    display: block;
                    margin-bottom: @indent__s;
                }

                &-name {
                    margin-top: 0;
                }

                .price-box {
                    margin: 0;
                }

                &-tooltip {
                }

                .comment-box {
                    .label {
                        &:extend(.abs-visually-hidden all);
                    }
                }

                &-comment {
                    display: block;
                    height: 42px;
                    margin: @indent__s 0;
                }

                &-actions {
                    > * {
                        margin-right: 15px;

                        &:last-child {
                            margin-right: 0;
                        }
                    }
                }

                .box-tocart {
                    input.qty {
                        &:extend(.abs-input-qty all);

                        height: 32px;
                    }
                }
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .products-grid.wishlist {
        .product-item {
            border-bottom: 1px solid @secondary__color;

            &:first-child {
                border-top: 1px solid @secondary__color;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .products-grid.wishlist {
        margin-bottom: @indent__l;
        margin-right: 0;

        .product {
            &-item {
                padding: @indent__base 0 @indent__base 0;
                position: relative;

                &-photo {
                    display: inline-block;
                    width: 100%;
                }

                &-name {
                    .lib-font-size(16);
                }

                &-actions {
                    display: block;
                    float: left;

                    .action {
                        margin-right: 15px;

                        &:last-child {
                            margin-right: 0;
                        }

                        &.edit {
                            float: left;
                        }

                        &.delete {
                            float: right;
                        }

                        &.edit,
                        &.delete {
                            margin-top: 7px;
                        }
                    }
                }

                .box-tocart {
                    float: left;
                    margin-right: @indent__base;

                    .stock {
                        margin-top: 7px;
                    }
                }

                .giftregisty-dropdown,
                .field.qty {
                    display: none;
                }
            }

            &-image-container {
                max-width: 80px;
            }
        }
    }

    //
    //  Grid view for wishlist
    //  -----------------------------------------

    .wishlist-index-index {
        .product {
            &-item {
                width: 100%;

                &-info {
                    width: auto;
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .wishlist-index-index {
        .products-grid {
            .product-item {
                margin-bottom: @indent__base;
            }

            .product-item-actions {
                margin: 0;
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .products-grid.wishlist {
        .product {
            &-item {
                &-tooltip {
                    display: inline-block;
                }

                &-actions {
                    margin: @indent__s 0 0;
                }

                .fieldset {
                    .field.qty {
                        margin-bottom: @indent__s;
                        padding-right: @indent__s;

                        .label {
                            width: auto;
                        }
                    }
                }

                .box-tocart {
                    .actions-primary {
                        margin: 0;
                    }

                    .stock {
                        margin: @indent__base 0 0;
                    }
                }
            }
        }
    }

    .wishlist-index-index {
        .main {
            .form-wishlist-items {
                .actions-toolbar {
                    &:extend(.abs-reset-left-margin-desktop all);
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .wishlist-index-index {
        .products-grid {
            .product-items {
                margin: 0;
            }
        }
    }
}
