<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\App\Action\Action;

/** @var \Magento\CatalogWidget\Block\Product\ProductsList $block */
?>
<?php if ($exist = ($block->getProductCollection() && $block->getProductCollection()->getSize())) : ?>
    <?php
    $type = 'widget-product-grid';

    $mode = 'grid';

    $image = 'new_products_content_widget_grid';
    $items = $block->getProductCollection()->getItems();

    $showWishlist = true;
    $showCompare = true;
    $showCart = true;
    $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
    $description = false;
    $parsedUrl = parse_url($block->getBaseUrl());
    $imageUrl = '';

    if (
        $parsedUrl !== null
        && $block->getData('image') !== null
        && is_array($parsedUrl) === true
        && count($parsedUrl) > 1
    ) {
        $imageUrl = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] : 'http';
        $imageUrl .= '://' . $parsedUrl['host'] . $block->getData('image');
    }

    $imageLeft = ($block->getData('image_position') === 'position_left' && $imageUrl !== '' ? ' product-list--image-left' : '');
    $imageRight = ($block->getData('image_position') === 'position_right' && $imageUrl !== '' ? ' product-list--image-right' : '');
    $imageAbove = ($block->getData('image_position') === 'position_above' && $imageUrl !== '' ? ' product-list--image-above' : '');
    $noImage = $imageUrl === '' || $imageAbove ? ' product-list--no-image' : '';
    $sliderFull = ($imageUrl === '' || $imageAbove !== '') ? ' product-slider--full' : '';
    $sliderHalf = ($imageRight !== '' || $imageLeft !== '') ? ' product-slider--half' : '';

    ?>
    <div class="product-list product-list-widget<?= $imageAbove ?><?= $noImage ?>">
        <?php if ($imageAbove !== '') : ?>
            <div class="product-list__media">
                <img
                    class="product-list__image lazyload"
                    alt="<?php /* @escapeNotVerified */
                    echo $block->stripTags($block->getData('image_title'), null, true); ?>"
                    src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                    data-src="<?= $imageUrl; ?>"
                />
                <div class="product-list__figcaption">
                    <?= $block->getData('image_title') ?>
                </div>
            </div>
        <?php endif; ?>
        <?php if ($block->getTitle()) : ?>
            <div class="product-list__heading">
                <?php if ($block->getData('title_type') === 'heading_one') : ?>
                    <h1 class="product-list__header"><?= $block->escapeHtml(__($block->getTitle())) ?></h1>
                <?php endif; ?>
                <?php if ($block->getData('title_type') === 'heading_two') : ?>
                    <h2 class="product-list__header"><?= $block->escapeHtml(__($block->getTitle())) ?></h2>
                <?php endif; ?>
            </div>
        <?php endif ?>
        <div class="product-list__content<?= $imageLeft ?><?= $imageRight ?>">
            <?php if ($imageRight !== '' || ($imageLeft !== '')) : ?>
                <div class="product-list__media">
                    <img
                        class="product-list__image lazyload"
                        alt="<?php /* @escapeNotVerified */
                        echo $block->stripTags($block->getData('image_title'), null, true); ?>"
                        src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                        data-src="<?= $imageUrl; ?>"
                    />
                    <div class="product-list__figcaption">
                        <?= $block->getData('image_title') ?>
                    </div>
                </div>
            <?php endif; ?>
            <div class="product-list__slider<?= $sliderFull ?><?= $sliderHalf ?>">
                <div class="product-list__controls slider-controls" aria-label="Carousel Navigation">
                    <button class="slider-controls__button slider-controls__button-left" data-controls="prev"
                            tabindex="0" aria-controls="product-list"><span
                            class="slider-controls__left i-chevron-left" aria-label="slide left"></span></button>
                    <button class="slider-controls__button slider-controls__button-right" data-controls="next"
                            tabindex="0" aria-controls="product-list"><span
                            class="slider-controls__right i-chevron-right" aria-label="slide right"></span></button>
                </div>
                <?= /* @noEscape */
                '<!-- ' . $image . '-->' ?>
                <ol class="product-list__list slider-list <?= /* @noEscape */
                $type ?>">
                    <?php $iterator = 1; ?>
                    <?php foreach ($items as $_item) : ?>
                        <?= /* @noEscape */
                        ($iterator++ == 1) ? '<li class="product-list__item">' : '</li><li class="product-list__item">' ?>
                        <div class="product-list__info product-item-info">
                            <?= $this->getLayout()->createBlock('Diamond\Catalog\Block\ProductBadges')->setTemplate('Magento_Catalog::product/view/badge.phtml')->setProduct($_item)->toHtml() ?>
                            <a href="<?= $block->escapeUrl($block->getProductUrl($_item)) ?>"
                               class="product-list__photo product photo product-item-photo">
                                <?= $block->getImage($_item, $image)->toHtml() ?>
                            </a>
                            <div class="product-list__details product-item-details">
                                <strong class="product-list__name">
                                    <a title="<?= $block->escapeHtmlAttr($_item->getName()) ?>"
                                       href="<?= $block->escapeUrl($block->getProductUrl($_item)) ?>"
                                       class="product-list__link">
                                        <?= $block->escapeHtml($_item->getName()) ?>
                                    </a>
                                </strong>
                                <?php if ($templateType) : ?>
                                    <?= $block->getReviewsSummaryHtml($_item, $templateType) ?>
                                <?php endif; ?>
                                <div class="product-list__price">
                                    <?= $block->getProductPriceHtml($_item, $type) ?>
                                </div>

                                <div class="product-list__stock-status">
                                    <?php if ($_item->getIsSalable()): ?>
                                        <div class="stock available product__available"
                                             title="<?php /* @escapeNotVerified */
                                             echo __('Availability') ?>">
                                                    <span><?php /* @escapeNotVerified */
                                                        echo __('In stock') ?></span>
                                        </div>
                                    <?php else: ?>
                                        <div
                                            class="stock unavailable product-delivery-standard product__delivery-standard">
                                                <span><?php /* @escapeNotVerified */
                                                    echo __('Out of stock') ?></span></div>
                                    <?php endif; ?>
                                </div>

                                <div class="product-list__swatches">
                                    <?php $swatchBlock = $this->getLayout()->createBlock("Magento\Swatches\Block\Product\Renderer\Listing\Configurable")->setTemplate("Diamond_Swatches::product/renderer.phtml"); ?>
                                    <?= $swatchBlock->setProduct($_item)->toHtml(); ?>
                                </div>
                            </div>
                        </div>
                        <?= ($iterator == count($items) + 1) ? '</li>' : '' ?>
                    <?php endforeach ?>
                </ol>
                <?= $block->getPagerHtml() ?>
            </div>
        </div>
    </div>
<?php endif; ?>
