<?php
/**
 * Currency switcher
 *
 * @var \Magento\Directory\Block\Currency $block
 */

?>
<?php if ($block->getCurrencyCount() > 1) : ?>
    <?php $currencies = $block->getCurrencies(); ?>
    <?php $currentCurrencyCode = $block->getCurrentCurrencyCode(); ?>
    <?php $currentCurrencySymbol = $block->getCurrentCurrencySymbol(); ?>
    <?php $id = $block->getIdModifier() ? '-' . $block->getIdModifier() : '' ?>
    <?php $currencySymbol = $this->helper('Diamond\Theme\Helper\Data')->getCurrencySymbol($currentCurrencyCode); ?>

    <div class="currency-switcher">
        <div class="currency-switcher__trigger">
            <a href="#"
               class="currency-switcher__trigger-text"
               data-toggle="#currencyNav"
               data-toggle-group="header"
               data-toggle-global>
                <?= $currencySymbol ?>
            </a>
        </div>
        <ul class="currency-switcher__dropdown"
            id="currencyNav"
            data-toggle-animate
            data-toggle-hidden="true">
            <?php foreach ($currencies as $_code => $_name) : ?>
                <?php if ($_code !== $currentCurrencyCode) : ?>
                    <li class="currency-switcher__option">
                        <a class="currency-switcher__option-text" href="#" data-post='<?= /* @noEscape */
                        $block->getSwitchCurrencyPostData($_code) ?>'>
                            <?= $this->helper('Diamond\Theme\Helper\Data')->getCurrencySymbol($_code) ?>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>
