<?php
/** @var \Magento\Cookie\Block\Html\Notices $block */
?>
<?php if ($this->helper(\Magento\Cookie\Helper\Cookie::class)->isCookieRestrictionModeEnabled()) : ?>
    <div role="alertdialog"
         tabindex="-1"
         class="message global cookie cookie-bar"
         id="notice-cookie-block"
         style="display: none;">
        <div role="document" class="content cookie-bar__content" tabindex="0">
            <p class="cookie-bar__paragraph">
                <strong><?= $block->escapeHtml(__('We use cookies to make your experience better.')) ?></strong>
                <span><?= $block->escapeHtml(__('To comply with the new e-Privacy directive, we need to ask for your consent to set the cookies.')) ?></span>
                <?= $block->escapeHtml(__('<a href="/en/privacy">Learn more</a>.'), ['a']) ?>
            </p>
            <div class="actions cookie-bar__actions">
                <button id="btn-cookie-allow" class="action allow primary cookie-bar__actions--button">
                    <span><?= $block->escapeHtml(__('Allow Cookies')) ?></span>
                </button>
                <button id="btn-cookie-disallow" class="action allow secondary cookie-bar__actions--button">
                    <span><?= $block->escapeHtml(__('Close')) ?></span>
                </button>
            </div>
        </div>
    </div>
    <script type="text/x-magento-init">
        {
            "#notice-cookie-block": {
                "cookieNotices": {
                    "cookieAllowButtonSelector": "#btn-cookie-allow",
                    "cookieDisallowButtonSelector": "#btn-cookie-disallow",
                    "cookieNameDisallow": "<?= /* @noEscape */ \LimeSoda\CookieAdvanced\Helper\Cookie::IS_USER_DISALLOWED_SAVE_COOKIE ?>",
                    "cookieName": "<?= /* @noEscape */ \Magento\Cookie\Helper\Cookie::IS_USER_ALLOWED_SAVE_COOKIE ?>",
                    "cookieValue": <?= /* @noEscape */ $this->helper(\Magento\Cookie\Helper\Cookie::class)->getAcceptedSaveCookiesWebsiteIds() ?>,
                    "cookieLifetime": <?= /* @noEscape */ $this->helper(\Magento\Cookie\Helper\Cookie::class)->getCookieRestrictionLifetime() ?>,
                    "noCookiesUrl": "<?= $block->escapeJs($block->escapeUrl($block->getUrl('cookie/index/noCookies'))) ?>"
                }
            }
        }
    </script>
<?php endif; ?>
