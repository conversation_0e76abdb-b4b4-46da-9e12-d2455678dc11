<?php
/**
 * Category view template
 *
 * @var $block \Magento\Catalog\Block\Category\View
 */
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$_category = $block->getCurrentCategory();

/* check if image exists, avoid unnecessary logging */
$image = $_category->getImage();

if ($image) {
    $categoryName = $_category->getName();
    $imgUrl = $_category->getImageUrl();

    /* replace the absolute path to the relative one */
    $imgUrl = ltrim($imgUrl, '/');

    if ($imgUrl) : ?>
        <?php
        /** @var \Staempfli\ImageResizer\Model\Resizer $imageResizer */
        $imageResizer = $block->getImageResizer();

        if ($imageResizer != null) {
            /* return back the slash for the browser */
            $resizedImageUrlBig = '/' . $imageResizer->resizeAndGetUrl($imgUrl, 1920, 571);
            $resizedImageUrlDesktop = '/' . $imageResizer->resizeAndGetUrl($imgUrl, 1200, 357);
            $resizedImageUrlTablet = '/' . $imageResizer->resizeAndGetUrl($imgUrl, 970, 288);
            $resizedImageUrlMobile = '/' . $imageResizer->resizeAndGetUrl($imgUrl, 767, 228);
            $resizedImageUrlMobileS = '/' . $imageResizer->resizeAndGetUrl($imgUrl, 370, 110);
        }
        ?>
        <div class="category-view__media">
            <img alt="<?= $block->escapeHtml($categoryName) ?>" title="<?= $block->escapeHtml($categoryName) ?>"
                 class="lazyload category-view__image"
                 src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
                 data-src="<?= $block->escapeUrl($resizedImageUrlMobileS); ?>"
                 data-srcset="<?= $block->escapeUrl($resizedImageUrlMobile); ?> 767w, <?= $block->escapeUrl(
                     $resizedImageUrlTablet
                 ); ?> 970w, <?= $block->escapeUrl($resizedImageUrlDesktop); ?> 1200w, <?= $block->escapeUrl(
                     $resizedImageUrlBig
                 ); ?> 1400w"
            />
            <div class="category-view__header">
                <h1 class="category-view__title">
                    <?= /* @noEscape */
                    $block->escapeHtml($categoryName) ?>
                </h1>
            </div>
        </div>

    <?php
    endif ?>
    <?php
}
?>
