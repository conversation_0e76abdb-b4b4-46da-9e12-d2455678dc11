<?php
/* @var $block \Magento\Catalog\Block\Product\Image */
$dataSrcSet = "data-src=\"{$block->escapeUrl($block->getData('image_default_data_src'))}\"";
$srcSetParts = explode(',', $block->getData('adaptive_images_data_list') ?? '');
if (count($srcSetParts) <= 1) {
    $dataSrcSet = '';
}
?>
<span class="product-image-container">
    <span class="product-image-wrapper">
        <?php /** @var $block \Magento\Catalog\Block\Product\Image */ ?>
        <img class="product-image-photo ratio-container lazyload"
             width="<?php /* @escapeNotVerified */ echo $block->getWidth(); ?>"
             height="<?php /* @escapeNotVerified */ echo $block->getHeight(); ?>"
             alt="<?php /* @escapeNotVerified */ echo $block->stripTags($block->getLabel(), null, true); ?>"
             src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="
             data-src="<?= $block->escapeUrl($block->getData($block->getData('image_default_data_src')?'image_default_data_src':'image_url')) ?>"
            <?php echo $dataSrcSet ?>
        />
    </span>
</span>
