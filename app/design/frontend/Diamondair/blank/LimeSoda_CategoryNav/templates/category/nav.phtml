<?php $categories = $block->getCategories(); ?>
<?php if ($block->getData('child_categories')) : ?>
    <?php $categories = $block->getData('child_categories'); ?>
        <?php foreach ($categories as $category) : ?>
            <?php if ($block->isCurrentCategory($category)) {
                $currentClass = ' sidebar-nav--current';
            } else {
                $currentClass = '';
            }
            ?>
            <li class="sidebar-nav__item <?php /* @noEscape */ echo $currentClass ?>">
                <a class="sidebar-nav__link sidebar-nav__link-<?php echo (int) $block->getLevel() ?>" href="<?php echo $block->escapeUrl($category->getUrl()); ?>">
                    <?php echo $block->escapeHtml($category->getData('name')) ?>
                </a>
                <?php if ($block->categoryHasChildren($category)) : ?>
                    <?php echo $this->getChildCategoryHtml($category) ?>
                <?php endif; ?>
            </li>
        <?php endforeach; ?>
<?php else : ?>

<div class="sidebar-nav categories-menu">

    <?php foreach ($categories as $category) : ?>
        <div class="sidebar-nav__category" data-toggle-next>
            <ul class="sidebar-nav__list sidebar-nav__list-<?php echo (int) $block->getLevel() ?>">
                <?php if ($block->isCurrentCategory($category)) {
                    $currentClass = ' sidebar-nav--current';
                } else {
                    $currentClass = '';
                }
                $isParent = $block->categoryHasChildren($category) ? ' sidebar-nav__parent  sidebar-nav__title title' : '';
                $parentData = $block->categoryHasChildren($category) ? ' data-toggle=".sidebar-nav__list" data-toggle-group="sidebar-nav"' : '';
                $parentIcon = $block->categoryHasChildren($category) ? '<span class="i-chevron-right sidebar-nav__link-icon"></span>' : '';
                $toggleList = $block->categoryHasChildren($category) ? 'data-toggle-animate' : '';
                $toggleClass = $block->categoryHasChildren($category) ? ' sidebar-nav__dropdown' : '';
                ?>

                <li class="sidebar-nav__item<?php /* @noEscape */ echo $currentClass ?>">
                    <a class="sidebar-nav__link<?= $isParent ?>" href="<?php echo $block->escapeUrl($category->getUrl()); ?>">
                        <?php echo $block->escapeHtml($category->getData('name')) ?>
                    </a>
                    <?php if ($block->categoryHasChildren($category)) : ?>
                        <ul class="sidebar-nav__list <?= $toggleClass ?> sidebar-nav__list-<?php echo (int) $block->getLevel() ?>">
                            <?php echo $this->getChildCategoryHtml($category) ?>
                        </ul>

                    <?php endif; ?>
                </li>
            </ul>
        </div>
    <?php endforeach; ?>

</div>
<?php endif; ?>
