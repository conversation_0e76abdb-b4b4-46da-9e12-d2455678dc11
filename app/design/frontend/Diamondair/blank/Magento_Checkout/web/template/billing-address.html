<div class="checkout-billing-address">
    <div class="billing-address-same-as-shipping-block field choice" data-bind="visible: canUseShippingAddress()">
        <input type="checkbox" name="billing-address-same-as-shipping" class="checkbox"
               data-bind="checked: isAddressSameAsShipping, click: useShippingAddress, attr: {id: 'billing-address-same-as-shipping-' + getCode($parent)}"/>
        <label data-bind="attr: {for: 'billing-address-same-as-shipping-' + getCode($parent)}" class="label"><span
            data-bind="i18n: 'My billing and shipping address are the same'"></span></label>
    </div>
    <render args="detailsTemplate"></render>
    <fieldset class="fieldset" data-bind="visible: !isAddressDetailsVisible()">
        <each args="getRegion('billing-address-list')" render=""></each>
        <div data-bind="fadeVisible: isAddressFormVisible">
            <render args="formTemplate"></render>
        </div>
        <render args="actionsTemplate"></render>
    </fieldset>
</div>
