//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Shopping cart
    //  ---------------------------------------------

    //  Summary block
    .cart-summary {
        &:extend(.abs-add-box-sizing all);
        &:extend(.abs-adjustment-incl-excl-tax all);

        margin-bottom: @indent__m;
        padding: 1px 15px @indent__m;

        > .title {
            display: block;
            .lib-heading(h3);
        }

        .block {
            &:extend(.abs-discount-block all);

            margin-bottom: 0;

            .item-options {
                margin-left: 0;
            }

            .fieldset {
                margin: 15px 0 @indent__m @indent__xs;

                .field {
                    margin: 0 0 @indent__s;
                }

                .methods {
                    .field {
                        > .label {
                            display: inline;
                        }
                    }
                }
            }

            .fieldset.estimate {
                > .legend,
                > .legend + br {
                    &:extend(.abs-no-display all);
                }
            }
        }
    }

    //  Totals block
    .cart-totals {
        &:extend(.abs-sidebar-totals all);

        border-top: 1px solid @border-color__base;

        .table-wrapper {
            margin-bottom: 0;
            overflow: inherit;
        }

        .discount.coupon {
            display: none;
        }
    }

    //  Products table
    .cart.table-wrapper {
        .items {
            thead + .item {
                border-top: @border-width__base solid @border-color__base;
            }

            > .item {
                border-bottom: @border-width__base solid @border-color__base;
                position: relative;
            }
        }

        .col {
            padding-top: @indent__base;

            &.qty {
                .input-text {
                    &:extend(.abs-input-qty all);

                    margin-top: -5px;
                }

                .label {
                    &:extend(.abs-visually-hidden all);
                }
            }
        }

        .item {
            &-actions td {
                padding-bottom: @indent__s;
                text-align: center;
                white-space: normal;
            }

            .col {
                &.item {
                    display: flex;
                    min-height: 75px;
                    padding: @indent__m 0 @indent__s 75px;
                    position: relative;
                }
            }
        }

        .actions-toolbar {
            &:extend(.abs-add-clearfix all);

            > .action {
                margin-bottom: @indent__s;
                margin-right: @indent__s;

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        .action {
            &.help.map {
                font-weight: @font-weight__regular;
            }
        }

        .product {
            &-item-photo {
                display: block;
                left: 0;
                padding: 0;
                position: absolute;
                top: 15px;
                width: 100%;
            }

            &-item-details {
                white-space: normal;
            }

            &-item-name {
                display: inline-block;
                font-weight: @font-weight__regular;
            }
        }

        .gift-registry-name-label {
            &:after {
                content: ':';
            }
        }

        //  Product options
        .item-options {
            &:extend(.abs-product-options-list all);
            &:extend(.abs-add-clearfix all);
            margin-bottom: @indent__s;

            dt, dd {
                margin: 0;
            }

            dt {
                &:after {
                    padding-right: 5px;
                }
            }
        }

        .product-item-name + .item-options {
            margin-top: @indent__s;
        }

        .product-image-wrapper {
            &:extend(.abs-reset-image-wrapper all);
        }

        .action.configure {
            display: inline-block;
            margin: 0 0 @indent__base;
        }
    }

    .cart-container {

        .checkout-methods-items {
            &:extend(.abs-reset-list all);

            margin-top: @indent__base;
            text-align: center;

            .action.primary {
                &:extend(.abs-button-l all);

                width: 100%;
            }

            .item {
                & + .item {
                    margin-top: @indent__base;
                }
            }
        }
    }

    //  Products pager
    .cart-products-toolbar {
        .toolbar-amount {
            margin: @indent__m 0 15px;
            padding: 0;
            text-align: center;
        }

        .pages {
            margin: 0 0 @indent__m;
            text-align: center;

            .items {
                > .item {
                    border-bottom: 0;
                }
            }
        }
    }

    .cart-products-toolbar-top {
        border-bottom: @border-width__base solid @border-color__base;
    }

    //
    //  Cross sell
    //  ---------------------------------------------

    .block.crosssell {
        margin-top: 70px;
    }

    .cart-tax-info,
    .cart .cart-tax-info {
        + .cart-tax-total {
            display: block;
        }
    }

    .cart.table-wrapper,
    .order-items.table-wrapper {
        .col.price,
        .col.qty,
        .col.subtotal,
        .col.msrp {
            text-align: right;
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .cart {
        &.table-wrapper {
            overflow: inherit;

            thead {
                .col {
                    &:not(.item) {
                        display: none;
                    }
                }
            }

            .col {
                &.qty,
                &.price,
                &.subtotal,
                &.msrp {
                    box-sizing: border-box;
                    display: block;
                    float: left;
                    text-align: center;
                    white-space: nowrap;
                    width: 33%;

                    &[data-th]:before {
                        content: attr(data-th) ':';
                        display: block;
                        font-weight: @font-weight__bold;
                        padding-bottom: @indent__s;
                    }
                }

                &.msrp {
                    white-space: normal;
                }
            }

            .item {
                .col.item {
                    display: block;
                    padding-bottom: 0;
                }
            }
        }

        .product {
            &-item-photo {
                max-width: 60px;
            }
        }
    }

    .cart-container {
        .form-cart {
            &:extend(.abs-shopping-cart-items-mobile all);
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .cart-container {
        &:extend(.abs-add-clearfix-desktop all);

        .form-cart {
            &:extend(.abs-shopping-cart-items-desktop all);
        }

        .widget {
            float: left;
        }
    }

    .cart-summary {
        float: right;
        position: relative;
        width: 23%;

        .actions-toolbar {
            .column.main & {
                &:extend(.abs-reset-left-margin-desktop all);

                > .secondary {
                    float: none;
                }
            }
        }

        .block {
            .fieldset {
                .field {
                    .lib-form-field-type-revert(@_type: block);

                    margin: 0 0 @indent__s;
                }
            }
        }
    }

    .cart {
        &.table-wrapper {
            .items { // Google Chrome version 44.0.2403.107 m fix
                min-width: 100%;
                width: auto;
            }

            .item {
                .col {
                    &.item {
                        padding: 27px 8px @indent__s;
                    }
                }

                &-actions {
                    td {
                        text-align: right;
                    }
                }
            }

            .product {
                &-item-photo {
                    display: table-cell;
                    padding-right: @indent__base;
                    position: static;
                    vertical-align: top;
                    width: 100%;
                }

                &-item-details {
                    display: table-cell;
                    vertical-align: top;
                    white-space: normal;
                    width: 99%;
                }
            }

            .item-actions {
                .actions-toolbar {
                    &:extend(.abs-reset-left-margin-desktop all);

                    text-align: left;
                }
            }
        }

        .action {
            margin-bottom: @indent__s;
        }
    }

    //  Products pager
    .cart-products-toolbar {
        .toolbar-amount {
            line-height: 30px;
            margin: 0;
        }

        .pages {
            float: right;
            margin: 0 0 1px;

            .item {
                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }

    .cart.table-wrapper {
        .cart-products-toolbar {
            + .cart {
                thead {
                    tr {
                        th.col {
                            padding-bottom: 7px;
                            padding-top: 8px;
                        }
                    }
                }
            }
        }

        .cart {
            + .cart-products-toolbar {
                margin-top: @indent__m;
            }
        }
    }
}
