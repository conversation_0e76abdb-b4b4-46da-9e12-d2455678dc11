//
//  Variables
//  _____________________________________________

@checkout-progress-bar__font-size: 18px;
@checkout-progress-bar__font-weight: @font-weight__light;
@checkout-progress-bar__margin: 0;

@checkout-progress-bar-item__background-color: @color-gray-middle1;
@checkout-progress-bar-item__border-radius: 6px;
@checkout-progress-bar-item__color: @color-gray40;
@checkout-progress-bar-item__margin: @indent__s;
@checkout-progress-bar-item__transition: background .3s;
@checkout-progress-bar-item__width: 345px;

@checkout-progress-bar-item__active__background-color: @active__color;
@checkout-progress-bar-item__active__color: @primary__color;
@checkout-progress-bar-item__active__font-weight: @font-weight__semibold;
@checkout-progress-bar-item__complete__color: @link__color;
@checkout-progress-bar-item__hover__background-color: darken(@checkout-progress-bar-item__background-color, 5%);

@checkout-progress-bar-item-element__height: @checkout-progress-bar-item-element__width;
@checkout-progress-bar-item-element__width: 47px;

@checkout-progress-bar-item-element-inner__background-color: @page__background-color;
@checkout-progress-bar-item-element-inner__border-color: @color-gray80;
@checkout-progress-bar-item-element-inner__color: @primary__color;
@checkout-progress-bar-item-element-inner__height: 45px;
@checkout-progress-bar-item-element-inner__width: 45px;
@checkout-progress-bar-item-element-inner__active__border-color: @active__color;
@checkout-progress-bar-item-element-inner__active__content: @icon-checkmark;

@checkout-progress-bar-item-element-outer-radius__width: 6px;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    //
    //  Checkout Progress Bar
    //  ---------------------------------------------

    .opc-progress-bar {
        &:extend(.abs-reset-list all);
        display: none;
    }
}

.opc-progress-bar {
    .lib-css(margin, 0 0 @checkout-progress-bar__margin);
    counter-reset: i;
    display: block;
}

.opc-progress-bar-item {
    .lib-css(margin, 0 0 @checkout-progress-bar-item__margin);
    .lib-css(width, @checkout-progress-bar-item__width);
    display: inline-block;
    position: relative;
    text-align: center;
    vertical-align: top;

    &:before { // Horizontal line
        .lib-css(background, @checkout-progress-bar-item__background-color);
        .lib-css(border, 1px solid @checkout-progress-bar-item-element-inner__border-color);
        .lib-css(top, @checkout-progress-bar-item-element__width/2);
        .lib-css(transition, @checkout-progress-bar-item__transition);
        content: '';
        height: 12px;
        left: 0;
        position: absolute;
        width: 100%;
    }

    &:first-child {
        &:before {
            .lib-css(border-radius, @checkout-progress-bar-item__border-radius 0 0 @checkout-progress-bar-item__border-radius);
        }
    }

    &:last-child {
        &:before {
            .lib-css(border-radius, 0 @checkout-progress-bar-item__border-radius @checkout-progress-bar-item__border-radius 0);
        }
    }

    > span {
        display: inline-block;
        padding-top: 50px;
        width: 100%;
        word-wrap: break-word;

        .lib-typography(
            @_color: @checkout-progress-bar-item__color,
            @_font-family: false,
            @_font-size: @checkout-progress-bar__font-size,
            @_font-style: false,
            @_font-weight: @checkout-progress-bar__font-weight,
            @_line-height: false
        );

        &:before, // Item element
        &:after {
            .lib-css(background, @checkout-progress-bar-item__background-color);
            .lib-css(border, 1px solid @checkout-progress-bar-item-element-inner__border-color);
            .lib-css(height, @checkout-progress-bar-item-element__height);
            .lib-css(margin-left, -(@checkout-progress-bar-item-element__width/2));
            .lib-css(transition, @checkout-progress-bar-item__transition);
            .lib-css(width, @checkout-progress-bar-item-element__width);
            border-radius: 50%;
            content: '';
            left: 50%;
            position: absolute;
            top: 0;
        }

        &:after { // Item element inner
            .lib-css(background, @checkout-progress-bar-item-element-inner__background-color);
            .lib-css(height, @checkout-progress-bar-item-element-inner__height);
            .lib-css(margin-left, -22.5px);
            .lib-css(top, 1px);
            .lib-css(width, @checkout-progress-bar-item-element-inner__width);
            content: counter(i);
            counter-increment: i;
            padding-top: 10px;

            .lib-typography(
                @_color: @checkout-progress-bar-item-element-inner__color,
                @_font-family: false,
                @_font-size: @checkout-progress-bar__font-size,
                @_font-style: false,
                @_font-weight: @font-weight__semibold,
                @_line-height: false
            );
        }
    }

    &._complete {
        cursor: pointer;

        &:hover {
            &:before {
                .lib-css(background, @checkout-progress-bar-item__hover__background-color);
            }

            > span {
                &:before {
                    .lib-css(background, @checkout-progress-bar-item__hover__background-color);
                }
            }
        }

        > span {
            .lib-css(color, @checkout-progress-bar-item__complete__color);

            &:after {
                .lib-css(font-family, @icons__font-name);
                .lib-css(content, @checkout-progress-bar-item-element-inner__active__content);
            }
        }
    }

    &._active {
        &:before {
            .lib-css(background, @checkout-progress-bar-item__active__background-color);
            .lib-css(border-color, @checkout-progress-bar-item-element-inner__active__border-color);
        }

        > span {
            .lib-css(color, @checkout-progress-bar-item__active__color);
            .lib-css(font-weight, @checkout-progress-bar-item__active__font-weight);

            &:before {
                .lib-css(background, @checkout-progress-bar-item__active__background-color);
                .lib-css(border-color, @checkout-progress-bar-item-element-inner__active__border-color);
            }

            &:after {
                .lib-css(border-color, @checkout-progress-bar-item-element-inner__active__border-color);
                .lib-css(content, @checkout-progress-bar-item-element-inner__active__content);
                .lib-css(font-family, @icons__font-name);
            }
        }
    }
}
