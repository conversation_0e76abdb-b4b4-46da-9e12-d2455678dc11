//  Variables
//  _____________________________________________

@checkout-shipping-address__max-width: 680px;
@checkout-shipping-address__margin-top: 28px;

@checkout-shipping-item-icon__background-color: @checkout-shipping-item__active__border-color;
@checkout-shipping-item-icon__color: @color-white;
@checkout-shipping-item-icon__content: @icon-checkmark;

@checkout-shipping-item__border: 2px solid transparent;
@checkout-shipping-item__line-height: 30px;
@checkout-shipping-item__margin: 0 0 @indent__base;
@checkout-shipping-item__padding: @indent__base (@indent__l + 5px) @indent__base @indent__base;
@checkout-shipping-item__transition: .3s border-color;
@checkout-shipping-item__width: 100%/3;
@checkout-shipping-item-tablet__width: 100%/2;
@checkout-shipping-item-mobile__width: 100%;
@checkout-shipping-item__active__border-color: @active__color;

@checkout-shipping-item-icon__selected__height: 27px;
@checkout-shipping-item-icon__selected__width: 29px;

@checkout-shipping-item-mobile__padding: 0 0 15px;
@checkout-shipping-item-mobile__margin: @checkout-shipping-item-mobile__padding;
@checkout-shipping-item-mobile__active__padding: 15px (@indent__l + 5px) 15px 18px;

@checkout-shipping-item-before__border-color: @color-gray80;
@checkout-shipping-item-before__height: calc(~'100% - 45px');

@checkout-shipping-method__border: @checkout-step-title__border;
@checkout-shipping-method__padding: @indent__base;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {

    .opc-wrapper {

        //
        //  Shipping Address
        //  ---------------------------------------------

        .form-login,
        .form-shipping-address {
            .lib-css(margin-top, @checkout-shipping-address__margin-top);

            .fieldset {
                .note {
                    font-size: @font-size__base;
                }
            }
        }

        .shipping-address-items {
            font-size: 0;
        }

        .shipping-address-item {
            &:extend(.abs-add-box-sizing all);
            .lib-css(border, @checkout-shipping-item__border);
            .lib-css(line-height, @checkout-shipping-item__line-height);
            .lib-css(margin, @checkout-shipping-item__margin);
            .lib-css(padding, @checkout-shipping-item__padding);
            .lib-css(transition, @checkout-shipping-item__transition);
            .lib-css(width, @checkout-shipping-item-tablet__width);
            display: inline-block;
            font-size: @font-size__base;
            position: relative;
            vertical-align: top;
            word-wrap: break-word;

            &.selected-item {
                .lib-css(border-color, @checkout-shipping-item__active__border-color);

                &:after {
                    .lib-css(background, @checkout-shipping-item-icon__background-color);
                    .lib-css(color, @checkout-shipping-item-icon__color);
                    .lib-css(content, @checkout-shipping-item-icon__content);
                    .lib-css(font-family, @icons__font-name);
                    .lib-css(height, @checkout-shipping-item-icon__selected__height);
                    .lib-css(width, @checkout-shipping-item-icon__selected__width);
                    font-size: 27px;
                    line-height: 21px;
                    padding-top: 2px;
                    position: absolute;
                    right: 0;
                    text-align: center;
                    top: 0;
                }
            }
        }

        .field {
            &.addresses {
                &:extend(.abs-add-clearfix all);
            }
        }

        .action-show-popup {
            margin: 0 0 @indent__base;

            > span {
                &:before {
                    content: '+';
                    padding-right: @indent__xs;
                }
            }
        }

        .action-select-shipping-item {
            float: right;
            margin: @indent__base 0 0;
        }

        .edit-address-link {
            &:extend(.abs-action-button-as-link all);
            display: block;
            float: left;
            margin: 26px 5px 0 0;
        }
    }

    //
    //  Shipping Methods
    //  ---------------------------------------------

    .checkout-shipping-method {
        .step-title {
            margin-bottom: 0;
        }

        .no-quotes-block {
            margin: @indent__base 0;
        }
    }

    .methods-shipping {
        .actions-toolbar {
            .action {
                &.primary {
                    &:extend(.abs-button-l all);
                    margin: @indent__base 0 0;
                }
            }
        }
    }

    .table-checkout-shipping-method {
        thead {
            th {
                display: none;
            }
        }

        tbody {
            td {
                .lib-css(border-top, @checkout-shipping-method__border);
                .lib-css(padding-bottom, @checkout-shipping-method__padding);
                .lib-css(padding-top, @checkout-shipping-method__padding);

                &:first-child {
                    padding-left: 0;
                    padding-right: 0;
                    width: 20px;
                }
            }

            tr {
                &:first-child {
                    td {
                        border-top: none;
                    }
                }
            }

            .row-error {
                td {
                    border-top: none;
                    padding-bottom: @indent__s;
                    padding-top: 0;
                }
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .opc-wrapper {
        .form-login {
            .lib-css(margin, @checkout-shipping-item-mobile__padding);
            .lib-css(padding, @checkout-shipping-item-mobile__padding);
        }

        .shipping-address-item {
            .lib-css(margin, @checkout-shipping-item-mobile__margin);
            .lib-css(padding, @checkout-shipping-item-mobile__padding);
            width: 100%;

            &.selected-item {
                .lib-css(padding, @checkout-shipping-item-mobile__active__padding);
                border-bottom-width: 2px;

                .edit-address-link {
                    .lib-css(right, @checkout-shipping-item-icon__selected__width + @indent__s);
                }
            }
        }

        .form-login,
        .form-shipping-address {
            .lib-css(margin-top, @checkout-shipping-address__margin-top);
        }

        .action-select-shipping-item {
            float: none;
            margin-top: @indent__s;
            width: 100%;
        }

        .action-show-popup {
            width: 100%;
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .checkout-shipping-method {
        .actions-toolbar {
            > .primary {
                float: right;
            }

            .action {
                &.primary {
                    margin: 0;
                }
            }
        }
    }

    .opc-wrapper {
        .form-login,
        .form-shipping-address {
            .lib-css(max-width, @checkout-shipping-address__max-width);
        }
    }

    .table-checkout-shipping-method {
        width: auto;
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__l) {
    .opc-wrapper {
        .shipping-address-item {
            .lib-css(width, @checkout-shipping-item__width);

            &:before {
                .lib-css(background, @checkout-shipping-item-before__border-color);
                .lib-css(height, @checkout-shipping-item-before__height);
                content: '';
                left: 0;
                position: absolute;
                top: 0;
                width: 1px;
            }

            &:nth-child(3n + 1) {
                &:before {
                    display: none;
                }
            }

            &.selected-item {
                &:before {
                    display: none;
                }

                + .shipping-address-item {
                    &:before {
                        display: none;
                    }
                }
            }
        }
    }

    .table-checkout-shipping-method {
        min-width: 500px;
    }
}
