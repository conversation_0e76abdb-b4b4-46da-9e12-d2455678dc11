<?php
/** @var $block \Magento\Catalog\Block\Product\View */
?>
<?php $_product = $block->getProduct(); ?>
<?php $buttonTitle = __('Update Cart'); ?>
<?php if ($_product->isSaleable()) : ?>
    <div class="box-tocart update">
        <fieldset class="fieldset box-tocart__fieldset">
            <?php if ($block->shouldRenderQuantity()) : ?>
                <div class="field qty box-tocart__qty">
                    <div class="control box-tocart__control">
                        <input type="number"
                               name="qty"
                               id="qty"
                               min="0"
                               value=""
                               title="<?= $block->escapeHtmlAttr(__('Qty')) ?>"
                               class="input-text qty box-tocart__input-qty"
                               data-validate="<?= $block->escapeHtmlAttr(json_encode($block->getQuantityValidators())) ?>"/>
                    </div>
                    <label class="label box-tocart__label"
                           for="qty"><span><?= $block->escapeHtml(__('Qty')) ?></span></label>
                </div>
            <?php endif; ?>
            <div class="actions box-tocart__actions">
                <button type="submit"
                        title="<?= $block->escapeHtmlAttr($buttonTitle) ?>"
                        class="action primary tocart box-tocart__action-btn box-tocart__update-btn"
                        id="product-updatecart-button">
                    <span class="i-edit"></span>
                    <span class="box-tocart__update-btn--title"><?= $block->escapeHtml($buttonTitle) ?></span>
                </button>
                <?= $block->getChildHtml('', true) ?>
            </div>
        </fieldset>
    </div>
    <script type="text/x-magento-init">
        {
            "#product_addtocart_form": {
                "validation": {},
                "addToCart": {
                    "cartButtonId": "#product-updatecart-button",
                    "cartForm": "#product_addtocart_form"
                }
            }
        }







    </script>
<?php endif; ?>
