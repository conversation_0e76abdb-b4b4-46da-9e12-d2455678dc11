<?php
// @codingStandardsIgnoreFile

/** @var $block \Magento\Checkout\Block\Cart\Sidebar */
?>


<div data-block="minicart" class="minicart-wrapper mini-cart">
    <a data-toggle="#mini-cart" class="showcart mini-cart__toggle" href="<?php /* @escapeNotVerified */ echo $block->getShoppingCartUrl(); ?>"
       data-bind="scope: 'minicart_content'" data-toggle-group="header" data-toggle-global>
       <span class="i-cart mini-cart__toggle-icon"></span>
        <span class="sr-only"><?php /* @escapeNotVerified */ echo __('My Cart'); ?></span>
        <!-- ko if: getCartParam('summary_count') > 0 -->
        <span class="counter qty empty"
              data-bind="css: { empty: !!getCartParam('summary_count') == false && !isLoading() }, blockLoader: isLoading">
            <span class="counter-number"><!-- ko text: getCartParam('summary_count') --><!-- /ko --></span>
        </span>
        <!-- /ko -->
    </a>

    <?php if ($block->getIsNeedToDisplaySideBar()): ?>
        <div class="block block-minicart  mini-cart__container" id="mini-cart" data-toggle-animate>

            <div id="minicart-content-wrapper" data-bind="scope: 'minicart_content'">
                <!-- ko template: getTemplate() --><!-- /ko -->
            </div>
            <?= $block->getChildHtml('minicart.addons') ?>
        </div>
        <?php else :?>
        <script>
            require(['jquery'], function ($) {
                $('a.action.showcart').click(function() {
                    $(document.body).trigger('processStart');
                });
            });
        </script>
    <?php endif ?>
    <script>
        window.checkout = <?= /* @noEscape */ $block->getSerializedConfig() ?>;
    </script>
    <script type="text/x-magento-init">
    {
        "[data-block='minicart']": {
            "Magento_Ui/js/core/app": <?= /* @noEscape */ $block->getJsLayout() ?>
        },
        "*": {
            "Magento_Ui/js/block-loader": "<?= $block->escapeJs(
                $block->escapeUrl($block->getViewFileUrl('images/loader-1.gif'))
            ) ?>"
        }
    }
    </script>
</div>
