<div class="footer-after">
    <div class="footer-after__container">
        <?= $block->getChildHtml('footer.after.shop.info') ?>
        <?= $block->getChildHtml('footer.after.shop.directions') ?>
        <div class="footer-after__price-infos" data-bind="scope: 'get-tax-price-info'">
            <!-- ko template: getTemplate() --><!-- /ko -->
        </div>
    </div>
</div>

<script type="text/x-magento-init">
{
    "*": {
        "Magento_Ui/js/core/app": {
            "components": {
                "get-tax-price-info": {
                    "component": "Diamond_Theme/js/taxPriceInfo",
                    "template" : "Diamond_Theme/taxPriceInfo"
                }
            }
        }
    }
}
</script>
