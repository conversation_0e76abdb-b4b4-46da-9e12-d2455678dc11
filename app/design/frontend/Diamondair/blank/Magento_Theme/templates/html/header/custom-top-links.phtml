<?php $loggedIn = $block->getLayout()->createBlock('Magento\Customer\Block\Account\AuthorizationLink')->isLoggedIn() ?>
<ul class="login__list">
    <?php if ($loggedIn) : ?>
        <li><a href="<?= $this->getBaseUrl() . "customer/account"; ?>"><?= __('My Account') ?></a></li>
        <li class="link wishlist" data-bind="scope: 'wishlist'">
            <a href="<?= $this->getBaseUrl() . "wishlist"; ?>"><?= __('My Wish List') ?></a>
        </li>
        <li class="authorization-link" data-label="or">
            <a href="<?= $this->getBaseUrl() . "customer/account/logout"; ?>"><?= __('Sign Out') ?> </a>
        </li>
    <?php else: ?>
        <li><a href="<?= $this->getBaseUrl() . "customer/account/create"; ?>"><?= __('Create an Account') ?></a></li>
        <li class="authorization-link" data-label="or"><a
                href="<?= $this->getBaseUrl() . "customer/account/login"; ?>"><?= __('Sign In') ?></a></li>
    <?php endif; ?>
</ul>
