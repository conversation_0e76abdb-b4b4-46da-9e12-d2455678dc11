<?php
/**
 * Pager template
 *
 * @see \Magento\Theme\Block\Html\Pager
 */
?>
<?php if ($block->getCollection()->getSize()) : ?>

    <?php if ($block->getUseContainer()) : ?>
        <div class="pager">
    <?php endif ?>

    <?php if ($block->getShowAmounts()) : ?>
        <p class="toolbar-amount">
            <span class="toolbar-number">
            <?php if ($block->getLastPageNum() > 1) : ?>
                <?= $block->escapeHtml(__('Items %1 to %2 of %3 total', $block->getFirstNum(), $block->getLastNum(), $block->getTotalNum())) ?>
            <?php elseif ($block->getTotalNum() == 1) : ?>
                <?= $block->escapeHtml(__('%1 Item', $block->getTotalNum())) ?>
            <?php else : ?>
                <?= $block->escapeHtml(__('%1 Item(s)', $block->getTotalNum())) ?>
            <?php endif; ?>
            </span>
        </p>
    <?php endif ?>

    <?php if ($block->getLastPageNum() > 1) : ?>
        <div class="pages">
            <strong class="label pages-label" id="paging-label"><?= $block->escapeHtml(__('Page')) ?></strong>
            <ul class="items pages-items" aria-labelledby="paging-label">
                <?php if (!$block->isFirstPage()) : ?>
                    <li class="item pages-item-previous">
                        <?php $text = $block->getAnchorTextForPrevious() ? $block->getAnchorTextForPrevious() : ''; ?>
                        <a class="<?= $block->escapeHtmlAttr($text ? 'link ' : 'action ') ?> previous"
                           href="<?= $block->escapeUrl($block->getPreviousPageUrl()) ?>"
                           title="<?= $block->escapeHtmlAttr($text ? $text : __('Previous')) ?>">
                            <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                            <span><?= $block->escapeHtml($text ? $text : __('Previous')) ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if ($block->canShowFirst()) : ?>
                    <li class="item">
                        <a class="page first" href="<?= $block->escapeUrl($block->getFirstPageUrl()) ?>">
                            <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                            <span>1</span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if ($block->canShowPreviousJump()) : ?>
                    <li class="item">
                        <a class="page previous jump"
                           title=""
                           href="<?= $block->escapeUrl($block->getPreviousJumpUrl()) ?>">
                            <span>...</span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php foreach ($block->getFramePages() as $_page) : ?>
                    <?php if ($block->isPageCurrent($_page)) : ?>
                        <li class="item current">
                            <strong class="page">
                                <span
                                    class="label"><?= $block->escapeHtml(__('You\'re currently reading page')) ?></span>
                                <span><?= $block->escapeHtml($_page) ?></span>
                            </strong>
                        </li>
                    <?php else : ?>
                        <li class="item">
                            <a href="<?= $block->escapeUrl($block->getPageUrl($_page)) ?>" class="page">
                                <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                                <span><?= $block->escapeHtml($_page) ?></span>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>

                <?php if ($block->canShowNextJump()) : ?>
                    <li class="item">
                        <a class="page next jump" title="" href="<?= $block->escapeUrl($block->getNextJumpUrl()) ?>">
                            <span>...</span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if ($block->canShowLast()) : ?>
                    <li class="item">
                        <a class="page last" href="<?= $block->escapeUrl($block->getLastPageUrl()) ?>">
                            <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                            <span><?= $block->escapeHtml($block->getLastPageNum()) ?></span>
                        </a>
                    </li>
                <?php endif; ?>

                <?php if (!$block->isLastPage()) : ?>
                    <li class="item pages-item-next">
                        <?php $text = $block->getAnchorTextForNext() ? $block->getAnchorTextForNext() : ''; ?>
                        <a class="<?= /* @noEscape */
                        $text ? 'link ' : 'action ' ?> next"
                           href="<?= $block->escapeUrl($block->getNextPageUrl()) ?>"
                           title="<?= $block->escapeHtmlAttr($text ? $text : __('Next')) ?>">
                            <span class="label"><?= $block->escapeHtml(__('Page')) ?></span>
                            <span><?= $block->escapeHtml($text ? $text : __('Next')) ?></span>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    <?php endif; ?>

    <?php if ($block->isShowPerPage()) : ?>
        <div class="limiter">
            <strong class="limiter-label"><?= $block->escapeHtml(__('Show')) ?></strong>
            <div class="limiter-wrapper">
                <select id="limiter" data-mage-init='{"redirectUrl": {"event":"change"}}' class="limiter-options">
                    <?php foreach ($block->getAvailableLimit() as $_key => $_limit) : ?>
                        <option value="<?= $block->escapeHtmlAttr($block->getLimitUrl($_key)) ?>"
                            <?php if ($block->isLimitCurrent($_key)) : ?>
                                selected="selected"<?php endif ?>>
                            <?= $block->escapeHtml($_limit) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <span class="limiter-text"><?= $block->escapeHtml(__('per page')) ?></span>
        </div>
    <?php endif ?>

    <?php if ($block->getUseContainer()) : ?>
        </div>
    <?php endif ?>

<?php endif ?>
