<?php $loggedIn = $block->getLayout()->createBlock('Magento\Customer\Block\Account\AuthorizationLink')->isLoggedIn() ?>
<?php if ($loggedIn) : ?>
    <div class="header-wishlist">
        <a href="<?= $block->escapeXssInUrl($block->getWishlistUrl()) ?>"
           class="header-wishlist__trigger"
           title="<?= $block->escapeHtml(__('Wishlist')); ?>"
        >
            <span class="i-favourite header-wishlist__icon"></span>
            <span class="sr-only"><?= $block->escapeHtml(__('Wishlist')); ?></span>
        </a>
        <span class="header-wishlist__counter wishlist-qty items-<?= (int)$this->getWishlistCount() ?>">
        <span class="counter-number">
            <?= (int)$this->getWishlistCount() ?>
        </span>
    </span>
    </div>
<?php endif; ?>
