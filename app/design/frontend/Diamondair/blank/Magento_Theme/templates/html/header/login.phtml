<?php
// @codingStandardsIgnoreFile
?>
<?php $loggedIn = $block->getLayout()->createBlock('Magento\Customer\Block\Account\AuthorizationLink')->isLoggedIn() ?>
<div class="login">
    <button type="button" data-toggle="#login" class="login__toggle" data-toggle-group="header" data-toggle-global>
        <span class="i-login login__toggle-icon"></span>
        <?php if ($loggedIn) : ?>
            <span class="login__label"><?php /* @escapeNotVerified */ echo __('Account'); ?></span>
        <?php else: ?>
            <span class="login__label"><?php /* @escapeNotVerified */ echo __('Login'); ?></span>
        <?php endif; ?>
    </button>
    <div class="login__container" id="login"  data-toggle-animate>
        <?php echo $block->getChildHtml() ?>
    </div>
</div>
