<?php

/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php
/**
 * Category layered navigation state
 *
 * @var $block \Magento\LayeredNavigation\Block\Navigation\State
 */
?>
<?php $_filters = $block->getActiveFilters() ?>
<?php if (!empty($_filters)) : ?>
    <div class="filter-current" role="tablist">
        <button class="block-subtitle layered-filter__button-link filter-current__subtitle" role="tab" aria-expanded="false" data-role="title" data-toggle-not-media data-toggle="#filter-current-dropdown" data-count="<?php echo count($_filters); ?>"><?php /* @escapeNotVerified */ echo __('Now Shopping by') ?>
        <span class="i-chevron-right filter-current__link-icon"></span>
        </button>

        <div class="filter-current__dropdown" id="filter-current-dropdown" data-toggle-animate>
            <ol class="filter-current__items layered-filter__items">
                <?php foreach ($_filters as $_filter) : ?>
                    <li class="filter-current__item layered-filter__item">
                        <span class="filter-current__label"><?php echo $block->escapeHtml(__($_filter->getName())); ?></span>
                        <span class="filter-current__value"><?php /* @escapeNotVerified */ echo $block->stripTags($_filter->getLabel()) ?></span>
                        <?php
                        $clearLinkUrl = $_filter->getClearLinkUrl();
                        $currentFilterName = $block->escapeHtml(__($_filter->getName())) . " " . $block->stripTags($_filter->getLabel());


                        if ($clearLinkUrl) :
                        ?>
                            <a class="filter-current__action filter-current__previous" href="<?php /* @escapeNotVerified */ echo $_filter->getRemoveUrl() ?>" title="<?php /* @escapeNotVerified */ echo __('Previous') ?>">
                                <span><?php /* @escapeNotVerified */ echo __('Previous') ?></span>
                            </a>
                            <a class="filter-current__action filter-current__remove" title="<?php echo $block->escapeHtml($_filter->getFilter()->getClearLinkText()) ?>" href="<?php /* @escapeNotVerified */ echo $clearLinkUrl ?>">
                            <span class="layered-filter__close">x</span>
                                <span class="sr-only"><?php echo $block->escapeHtml($_filter->getFilter()->getClearLinkText()) ?></span>
                            </a>
                        <?php else : ?>
                            <a class="filter-current__action filter-current__remove" href="<?php /* @escapeNotVerified */ echo $_filter->getRemoveUrl() ?>" title="<?php /* @escapeNotVerified */ echo $block->escapeHtml(__('Remove')) . " " . $currentFilterName; ?>">
                            <span class="layered-filter__close">x</span>
                                <span class="sr-only"><?php /* @escapeNotVerified */ echo __('Remove This Item') ?></span>
                            </a>
                        <?php endif; ?>

                    </li>
                <?php endforeach; ?>
            </ol>

            <?php if ($block->getLayer()->getState()->getFilters()) : ?>
                <div class="block-actions filter-actions filter-current__actions">
                    <a href="<?php /* @escapeNotVerified */ echo $block->getClearUrl() ?>" class="filter-clear filter-current__clear"><span><?php /* @escapeNotVerified */ echo __('Clear All') ?></span></a>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>
