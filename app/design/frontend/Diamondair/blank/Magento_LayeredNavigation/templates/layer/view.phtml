<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php
/**
 * Category layered navigation
 *
 * @var $block \Magento\LayeredNavigation\Block\Navigation
 */
?>

<?php if ($block->canShowBlock()): ?>
    <div class="layered-filter" id="layered-filter-block">
        <?php $filtered = count($block->getLayer()->getState()->getFilters()) ?>
        <button class="layered-filter__toggle" aria-expanded="false" data-count="<?php /* @escapeNotVerified */ echo $filtered; ?>" data-toggle-role="overlay"  data-toggle="#layered-filter-dropdown">
           <?php /* @escapeNotVerified */ echo __('Shop By') ?>
        </button>
        <?php echo $block->getChildHtml('state') ?>

        <div class="layered-filter__dropdown" id="layered-filter-dropdown">
            <button class="layered-filter__close" aria-expanded="false" data-count="<?php /* @escapeNotVerified */ echo $filtered; ?>"  data-toggle-back=".layered-filter">
                <span class="sr-only"><?php /* @escapeNotVerified */ echo __('Close') ?></span>
                <span class="layered-filter__close i-burger_close"></span>
            </button>
            <div class="layered-filter__content" data-toggle-next>
                <?php $wrapOptions = false; ?>
                <?php foreach ($block->getFilters() as $filter): ?>
                    <?php if ($filter->getItemsCount()): ?>
                        <?php if (!$wrapOptions): ?>
                            <strong role="heading" aria-level="2" class="sr-only-mobile layered-filter__subtitle">
                                <?php /* @escapeNotVerified */ echo __('Filter') ?>
                            </strong>
                        <?php $range = implode(' ', [0,1,2,4]); ?>
                            <div class="filter-options" id="narrow-by-list">
                        <?php  $wrapOptions = true; endif; ?>
                        <div data-role="collapsible" class="layered-filter__options-item">
                            <button data-role="title" class="layered-filter__button-link" data-toggle=".layered-filter__options" data-toggle-group="layered-filter__options" aria-expanded="false">
                                <?php /* @escapeNotVerified */ echo __($filter->getName()) ?>
                                <span class="i-chevron-right layered-filter__button-icon"></span>
                            </button>

                            <div data-role="content" class="layered-filter__options" data-toggle-animate>
                                <?php /* @escapeNotVerified */ echo $block->getChildBlock('renderer')->render($filter); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
                <?php if ($wrapOptions): ?>
                    </div>
                <?php else: ?>

            <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>
