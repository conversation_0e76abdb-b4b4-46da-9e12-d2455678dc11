<?php
/** @var \Magento\Store\Block\Switcher $block */
?>
<?php if (count($block->getStores())>1) : ?>
    <?php $id = $block->getIdModifier() ? '-' . $block->getIdModifier() : '' ?>
    <div class="switcher language-switcher">
        <div class="language-switcher__trigger"
                data-toggle="#languageNav"
                data-toggle-group="header"
                data-toggle-global>
            <a href="#" class="language-switcher__trigger-text"><?= $block->escapeHtml($block->getCurrentStoreCode()) ?></a>
        </div>
        <ul class="language-switcher__dropdown"
            id="languageNav"
            data-toggle-animate
            data-toggle-hidden="true">
            <?php foreach ($block->getStores() as $_lang) : ?>
                <?php if ($_lang->getId() != $block->getCurrentStoreId()) : ?>
                    <li class="language-switcher__option">
                        <a class="language-switcher__option-text" href="<?= $block->escapeUrl($block->getViewModel()->getTargetStoreRedirectUrl($_lang)) ?>">
                            <?= $block->escapeHtml($_lang->getCode()) ?>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>
