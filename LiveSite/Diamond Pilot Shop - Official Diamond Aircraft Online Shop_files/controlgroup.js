/*!
 * jQuery UI Controlgroup 1.13.2
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 */

//>>label: Controlgroup
//>>group: Widgets
//>>description: Visually groups form control widgets
//>>docs: http://api.jqueryui.com/controlgroup/
//>>demos: http://jqueryui.com/controlgroup/
//>>css.structure: ../../themes/base/core.css
//>>css.structure: ../../themes/base/controlgroup.css
//>>css.theme: ../../themes/base/theme.css

( function( factory ) {
    "use strict";

    if ( typeof define === "function" && define.amd ) {

        // AMD. Register as an anonymous module.
        define( [
            "jquery",
            "../widget"
        ], factory );
    } else {

        // Browser globals
        factory( jQuery );
    }
} )( function( $ ) {
    "use strict";

    var controlgroupCornerRegex = /ui-corner-([a-z]){2,6}/g;

    return $.widget( "ui.controlgroup", {
        version: "1.13.2",
        defaultElement: "<div>",
        options: {
            direction: "horizontal",
            disabled: null,
            onlyVisible: true,
            items: {
                "button": "input[type=button], input[type=submit], input[type=reset], button, a",
                "controlgroupLabel": ".ui-controlgroup-label",
                "checkboxradio": "input[type='checkbox'], input[type='radio']",
                "selectmenu": "select",
                "spinner": ".ui-spinner-input"
            }
        },

        _create: function() {
            this._enhance();
        },

        // To support the enhanced option in jQuery Mobile, we isolate DOM manipulation
        _enhance: function() {
            this.element.attr( "role", "toolbar" );
            this.refresh();
        },

        _destroy: function() {
            this._callChildMethod( "destroy" );
            this.childWidgets.removeData( "ui-controlgroup-data" );
            this.element.removeAttr( "role" );
            if ( this.options.items.controlgroupLabel ) {
                this.element
                    .find( this.options.items.controlgroupLabel )
                    .find( ".ui-controlgroup-label-contents" )
                    .contents().unwrap();
            }
        },

        _initWidgets: function() {
            var that = this,
                childWidgets = [];

            // First we iterate over each of the items options
            $.each( this.options.items, function( widget, selector ) {
                var labels;
                var options = {};

                // Make sure the widget has a selector set
                if ( !selector ) {
                    return;
                }

                if ( widget === "controlgroupLabel" ) {
                    labels = that.element.find( selector );
                    labels.each( function() {
                        var element = $( this );

                        if ( element.children( ".ui-controlgroup-label-contents" ).length ) {
                            return;
                        }
                        element.contents()
                            .wrapAll( "<span class='ui-controlgroup-label-contents'></span>" );
                    } );
                    that._addClass( labels, null, "ui-widget ui-widget-content ui-state-default" );
                    childWidgets = childWidgets.concat( labels.get() );
                    return;
                }

                // Make sure the widget actually exists
                if ( !$.fn[ widget ] ) {
                    return;
                }

                // We assume everything is in the middle to start because we can't determine
                // first / last elements until all enhancments are done.
                if ( that[ "_" + widget + "Options" ] ) {
                    options = that[ "_" + widget + "Options" ]( "middle" );
                } else {
                    options = { classes: {} };
                }

                // Find instances of this widget inside controlgroup and init them
                that.element
                    .find( selector )
                    .each( function() {
                        var element = $( this );
                        var instance = element[ widget ]( "instance" );

                        // We need to clone the default options for this type of widget to avoid
                        // polluting the variable options which has a wider scope than a single widget.
                        var instanceOptions = $.widget.extend( {}, options );

                        // If the button is the child of a spinner ignore it
                        // TODO: Find a more generic solution
                        if ( widget === "button" && element.parent( ".ui-spinner" ).length ) {
                            return;
                        }

                        // Create the widget if it doesn't exist
                        if ( !instance ) {
                            instance = element[ widget ]()[ widget ]( "instance" );
                        }
                        if ( instance ) {
                            instanceOptions.classes =
                                that._resolveClassesValues( instanceOptions.classes, instance );
                        }
                        element[ widget ]( instanceOptions );

                        // Store an instance of the controlgroup to be able to reference
                        // from the outermost element for changing options and refresh
                        var widgetElement = element[ widget ]( "widget" );
                        $.data( widgetElement[ 0 ], "ui-controlgroup-data",
                            instance ? instance : element[ widget ]( "instance" ) );

                        childWidgets.push( widgetElement[ 0 ] );
                    } );
            } );

            this.childWidgets = $( $.uniqueSort( childWidgets ) );
            this._addClass( this.childWidgets, "ui-controlgroup-item" );
        },

        _callChildMethod: function( method ) {
            this.childWidgets.each( function() {
                var element = $( this ),
                    data = element.data( "ui-controlgroup-data" );
                if ( data && data[ method ] ) {
                    data[ method ]();
                }
            } );
        },

        _updateCornerClass: function( element, position ) {
            var remove = "ui-corner-top ui-corner-bottom ui-corner-left ui-corner-right ui-corner-all";
            var add = this._buildSimpleOptions( position, "label" ).classes.label;

            this._removeClass( element, null, remove );
            this._addClass( element, null, add );
        },

        _buildSimpleOptions: function( position, key ) {
            var direction = this.options.direction === "vertical";
            var result = {
                classes: {}
            };
            result.classes[ key ] = {
                "middle": "",
                "first": "ui-corner-" + ( direction ? "top" : "left" ),
                "last": "ui-corner-" + ( direction ? "bottom" : "right" ),
                "only": "ui-corner-all"
            }[ position ];

            return result;
        },

        _spinnerOptions: function( position ) {
            var options = this._buildSimpleOptions( position, "ui-spinner" );

            options.classes[ "ui-spinner-up" ] = "";
            options.classes[ "ui-spinner-down" ] = "";

            return options;
        },

        _buttonOptions: function( position ) {
            return this._buildSimpleOptions( position, "ui-button" );
        },

        _checkboxradioOptions: function( position ) {
            return this._buildSimpleOptions( position, "ui-checkboxradio-label" );
        },

        _selectmenuOptions: function( position ) {
            var direction = this.options.direction === "vertical";
            return {
                width: direction ? "auto" : false,
                classes: {
                    middle: {
                        "ui-selectmenu-button-open": "",
                        "ui-selectmenu-button-closed": ""
                    },
                    first: {
                        "ui-selectmenu-button-open": "ui-corner-" + ( direction ? "top" : "tl" ),
                        "ui-selectmenu-button-closed": "ui-corner-" + ( direction ? "top" : "left" )
                    },
                    last: {
                        "ui-selectmenu-button-open": direction ? "" : "ui-corner-tr",
                        "ui-selectmenu-button-closed": "ui-corner-" + ( direction ? "bottom" : "right" )
                    },
                    only: {
                        "ui-selectmenu-button-open": "ui-corner-top",
                        "ui-selectmenu-button-closed": "ui-corner-all"
                    }

                }[ position ]
            };
        },

        _resolveClassesValues: function( classes, instance ) {
            var result = {};
            $.each( classes, function( key ) {
                var current = instance.options.classes[ key ] || "";
                current = String.prototype.trim.call( current.replace( controlgroupCornerRegex, "" ) );
                result[ key ] = ( current + " " + classes[ key ] ).replace( /\s+/g, " " );
            } );
            return result;
        },

        _setOption: function( key, value ) {
            if ( key === "direction" ) {
                this._removeClass( "ui-controlgroup-" + this.options.direction );
            }

            this._super( key, value );
            if ( key === "disabled" ) {
                this._callChildMethod( value ? "disable" : "enable" );
                return;
            }

            this.refresh();
        },

        refresh: function() {
            var children,
                that = this;

            this._addClass( "ui-controlgroup ui-controlgroup-" + this.options.direction );

            if ( this.options.direction === "horizontal" ) {
                this._addClass( null, "ui-helper-clearfix" );
            }
            this._initWidgets();

            children = this.childWidgets;

            // We filter here because we need to track all childWidgets not just the visible ones
            if ( this.options.onlyVisible ) {
                children = children.filter( ":visible" );
            }

            if ( children.length ) {

                // We do this last because we need to make sure all enhancment is done
                // before determining first and last
                $.each( [ "first", "last" ], function( index, value ) {
                    var instance = children[ value ]().data( "ui-controlgroup-data" );

                    if ( instance && that[ "_" + instance.widgetName + "Options" ] ) {
                        var options = that[ "_" + instance.widgetName + "Options" ](
                            children.length === 1 ? "only" : value
                        );
                        options.classes = that._resolveClassesValues( options.classes, instance );
                        instance.element[ instance.widgetName ]( options );
                    } else {
                        that._updateCornerClass( children[ value ](), value );
                    }
                } );

                // Finally call the refresh method on each of the child widgets.
                this._callChildMethod( "refresh" );
            }
        }
    } );
} );
