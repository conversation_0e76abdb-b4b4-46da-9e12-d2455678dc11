/*!
 * jQuery UI Effects 1.13.2
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 */

//>>label: Effects Core
//>>group: Effects
/* eslint-disable max-len */
//>>description: Extends the internal jQuery effects. Includes morphing and easing. Required by all other effects.
/* eslint-enable max-len */
//>>docs: http://api.jqueryui.com/category/effects-core/
//>>demos: http://jqueryui.com/effect/

( function( factory ) {
    "use strict";

    if ( typeof define === "function" && define.amd ) {

        // AMD. Register as an anonymous module.
        define( [
            "jquery",
            "./jquery-var-for-color",
            "./vendor/jquery-color/jquery.color",
            "./version"
        ], factory );
    } else {

        // Browser globals
        factory( jQuery );
    }
} )( function( $ ) {
    "use strict";

    var dataSpace = "ui-effects-",
        dataSpaceStyle = "ui-effects-style",
        dataSpaceAnimated = "ui-effects-animated";

    $.effects = {
        effect: {}
    };

    /******************************************************************************/
    /****************************** CLASS ANIMATIONS ******************************/
    /******************************************************************************/
    ( function() {

        var classAnimationActions = [ "add", "remove", "toggle" ],
            shorthandStyles = {
                border: 1,
                borderBottom: 1,
                borderColor: 1,
                borderLeft: 1,
                borderRight: 1,
                borderTop: 1,
                borderWidth: 1,
                margin: 1,
                padding: 1
            };

        $.each(
            [ "borderLeftStyle", "borderRightStyle", "borderBottomStyle", "borderTopStyle" ],
            function( _, prop ) {
                $.fx.step[ prop ] = function( fx ) {
                    if ( fx.end !== "none" && !fx.setAttr || fx.pos === 1 && !fx.setAttr ) {
                        jQuery.style( fx.elem, prop, fx.end );
                        fx.setAttr = true;
                    }
                };
            }
        );

        function camelCase( string ) {
            return string.replace( /-([\da-z])/gi, function( all, letter ) {
                return letter.toUpperCase();
            } );
        }

        function getElementStyles( elem ) {
            var key, len,
                style = elem.ownerDocument.defaultView ?
                    elem.ownerDocument.defaultView.getComputedStyle( elem, null ) :
                    elem.currentStyle,
                styles = {};

            if ( style && style.length && style[ 0 ] && style[ style[ 0 ] ] ) {
                len = style.length;
                while ( len-- ) {
                    key = style[ len ];
                    if ( typeof style[ key ] === "string" ) {
                        styles[ camelCase( key ) ] = style[ key ];
                    }
                }

                // Support: Opera, IE <9
            } else {
                for ( key in style ) {
                    if ( typeof style[ key ] === "string" ) {
                        styles[ key ] = style[ key ];
                    }
                }
            }

            return styles;
        }

        function styleDifference( oldStyle, newStyle ) {
            var diff = {},
                name, value;

            for ( name in newStyle ) {
                value = newStyle[ name ];
                if ( oldStyle[ name ] !== value ) {
                    if ( !shorthandStyles[ name ] ) {
                        if ( $.fx.step[ name ] || !isNaN( parseFloat( value ) ) ) {
                            diff[ name ] = value;
                        }
                    }
                }
            }

            return diff;
        }

// Support: jQuery <1.8
        if ( !$.fn.addBack ) {
            $.fn.addBack = function( selector ) {
                return this.add( selector == null ?
                    this.prevObject : this.prevObject.filter( selector )
                );
            };
        }

        $.effects.animateClass = function( value, duration, easing, callback ) {
            var o = $.speed( duration, easing, callback );

            return this.queue( function() {
                var animated = $( this ),
                    baseClass = animated.attr( "class" ) || "",
                    applyClassChange,
                    allAnimations = o.children ? animated.find( "*" ).addBack() : animated;

                // Map the animated objects to store the original styles.
                allAnimations = allAnimations.map( function() {
                    var el = $( this );
                    return {
                        el: el,
                        start: getElementStyles( this )
                    };
                } );

                // Apply class change
                applyClassChange = function() {
                    $.each( classAnimationActions, function( i, action ) {
                        if ( value[ action ] ) {
                            animated[ action + "Class" ]( value[ action ] );
                        }
                    } );
                };
                applyClassChange();

                // Map all animated objects again - calculate new styles and diff
                allAnimations = allAnimations.map( function() {
                    this.end = getElementStyles( this.el[ 0 ] );
                    this.diff = styleDifference( this.start, this.end );
                    return this;
                } );

                // Apply original class
                animated.attr( "class", baseClass );

                // Map all animated objects again - this time collecting a promise
                allAnimations = allAnimations.map( function() {
                    var styleInfo = this,
                        dfd = $.Deferred(),
                        opts = $.extend( {}, o, {
                            queue: false,
                            complete: function() {
                                dfd.resolve( styleInfo );
                            }
                        } );

                    this.el.animate( this.diff, opts );
                    return dfd.promise();
                } );

                // Once all animations have completed:
                $.when.apply( $, allAnimations.get() ).done( function() {

                    // Set the final class
                    applyClassChange();

                    // For each animated element,
                    // clear all css properties that were animated
                    $.each( arguments, function() {
                        var el = this.el;
                        $.each( this.diff, function( key ) {
                            el.css( key, "" );
                        } );
                    } );

                    // This is guarnteed to be there if you use jQuery.speed()
                    // it also handles dequeuing the next anim...
                    o.complete.call( animated[ 0 ] );
                } );
            } );
        };

        $.fn.extend( {
            addClass: ( function( orig ) {
                return function( classNames, speed, easing, callback ) {
                    return speed ?
                        $.effects.animateClass.call( this,
                            { add: classNames }, speed, easing, callback ) :
                        orig.apply( this, arguments );
                };
            } )( $.fn.addClass ),

            removeClass: ( function( orig ) {
                return function( classNames, speed, easing, callback ) {
                    return arguments.length > 1 ?
                        $.effects.animateClass.call( this,
                            { remove: classNames }, speed, easing, callback ) :
                        orig.apply( this, arguments );
                };
            } )( $.fn.removeClass ),

            toggleClass: ( function( orig ) {
                return function( classNames, force, speed, easing, callback ) {
                    if ( typeof force === "boolean" || force === undefined ) {
                        if ( !speed ) {

                            // Without speed parameter
                            return orig.apply( this, arguments );
                        } else {
                            return $.effects.animateClass.call( this,
                                ( force ? { add: classNames } : { remove: classNames } ),
                                speed, easing, callback );
                        }
                    } else {

                        // Without force parameter
                        return $.effects.animateClass.call( this,
                            { toggle: classNames }, force, speed, easing );
                    }
                };
            } )( $.fn.toggleClass ),

            switchClass: function( remove, add, speed, easing, callback ) {
                return $.effects.animateClass.call( this, {
                    add: add,
                    remove: remove
                }, speed, easing, callback );
            }
        } );

    } )();

    /******************************************************************************/
    /*********************************** EFFECTS **********************************/
    /******************************************************************************/

    ( function() {

        if ( $.expr && $.expr.pseudos && $.expr.pseudos.animated ) {
            $.expr.pseudos.animated = ( function( orig ) {
                return function( elem ) {
                    return !!$( elem ).data( dataSpaceAnimated ) || orig( elem );
                };
            } )( $.expr.pseudos.animated );
        }

        if ( $.uiBackCompat !== false ) {
            $.extend( $.effects, {

                // Saves a set of properties in a data storage
                save: function( element, set ) {
                    var i = 0, length = set.length;
                    for ( ; i < length; i++ ) {
                        if ( set[ i ] !== null ) {
                            element.data( dataSpace + set[ i ], element[ 0 ].style[ set[ i ] ] );
                        }
                    }
                },

                // Restores a set of previously saved properties from a data storage
                restore: function( element, set ) {
                    var val, i = 0, length = set.length;
                    for ( ; i < length; i++ ) {
                        if ( set[ i ] !== null ) {
                            val = element.data( dataSpace + set[ i ] );
                            element.css( set[ i ], val );
                        }
                    }
                },

                setMode: function( el, mode ) {
                    if ( mode === "toggle" ) {
                        mode = el.is( ":hidden" ) ? "show" : "hide";
                    }
                    return mode;
                },

                // Wraps the element around a wrapper that copies position properties
                createWrapper: function( element ) {

                    // If the element is already wrapped, return it
                    if ( element.parent().is( ".ui-effects-wrapper" ) ) {
                        return element.parent();
                    }

                    // Wrap the element
                    var props = {
                            width: element.outerWidth( true ),
                            height: element.outerHeight( true ),
                            "float": element.css( "float" )
                        },
                        wrapper = $( "<div></div>" )
                            .addClass( "ui-effects-wrapper" )
                            .css( {
                                fontSize: "100%",
                                background: "transparent",
                                border: "none",
                                margin: 0,
                                padding: 0
                            } ),

                        // Store the size in case width/height are defined in % - Fixes #5245
                        size = {
                            width: element.width(),
                            height: element.height()
                        },
                        active = document.activeElement;

                    // Support: Firefox
                    // Firefox incorrectly exposes anonymous content
                    // https://bugzilla.mozilla.org/show_bug.cgi?id=561664
                    try {
                        // eslint-disable-next-line no-unused-expressions
                        active.id;
                    } catch ( e ) {
                        active = document.body;
                    }

                    element.wrap( wrapper );

                    // Fixes #7595 - Elements lose focus when wrapped.
                    if ( element[ 0 ] === active || $.contains( element[ 0 ], active ) ) {
                        $( active ).trigger( "focus" );
                    }

                    // Hotfix for jQuery 1.4 since some change in wrap() seems to actually
                    // lose the reference to the wrapped element
                    wrapper = element.parent();

                    // Transfer positioning properties to the wrapper
                    if ( element.css( "position" ) === "static" ) {
                        wrapper.css( { position: "relative" } );
                        element.css( { position: "relative" } );
                    } else {
                        $.extend( props, {
                            position: element.css( "position" ),
                            zIndex: element.css( "z-index" )
                        } );
                        $.each( [ "top", "left", "bottom", "right" ], function( i, pos ) {
                            props[ pos ] = element.css( pos );
                            if ( isNaN( parseInt( props[ pos ], 10 ) ) ) {
                                props[ pos ] = "auto";
                            }
                        } );
                        element.css( {
                            position: "relative",
                            top: 0,
                            left: 0,
                            right: "auto",
                            bottom: "auto"
                        } );
                    }
                    element.css( size );

                    return wrapper.css( props ).show();
                },

                removeWrapper: function( element ) {
                    var active = document.activeElement;

                    if ( element.parent().is( ".ui-effects-wrapper" ) ) {
                        element.parent().replaceWith( element );

                        // Fixes #7595 - Elements lose focus when wrapped.
                        if ( element[ 0 ] === active || $.contains( element[ 0 ], active ) ) {
                            $( active ).trigger( "focus" );
                        }
                    }

                    return element;
                }
            } );
        }

        $.extend( $.effects, {
            version: "1.13.2",

            define: function( name, mode, effect ) {
                if ( !effect ) {
                    effect = mode;
                    mode = "effect";
                }

                $.effects.effect[ name ] = effect;
                $.effects.effect[ name ].mode = mode;

                return effect;
            },

            scaledDimensions: function( element, percent, direction ) {
                if ( percent === 0 ) {
                    return {
                        height: 0,
                        width: 0,
                        outerHeight: 0,
                        outerWidth: 0
                    };
                }

                var x = direction !== "horizontal" ? ( ( percent || 100 ) / 100 ) : 1,
                    y = direction !== "vertical" ? ( ( percent || 100 ) / 100 ) : 1;

                return {
                    height: element.height() * y,
                    width: element.width() * x,
                    outerHeight: element.outerHeight() * y,
                    outerWidth: element.outerWidth() * x
                };

            },

            clipToBox: function( animation ) {
                return {
                    width: animation.clip.right - animation.clip.left,
                    height: animation.clip.bottom - animation.clip.top,
                    left: animation.clip.left,
                    top: animation.clip.top
                };
            },

            // Injects recently queued functions to be first in line (after "inprogress")
            unshift: function( element, queueLength, count ) {
                var queue = element.queue();

                if ( queueLength > 1 ) {
                    queue.splice.apply( queue,
                        [ 1, 0 ].concat( queue.splice( queueLength, count ) ) );
                }
                element.dequeue();
            },

            saveStyle: function( element ) {
                element.data( dataSpaceStyle, element[ 0 ].style.cssText );
            },

            restoreStyle: function( element ) {
                element[ 0 ].style.cssText = element.data( dataSpaceStyle ) || "";
                element.removeData( dataSpaceStyle );
            },

            mode: function( element, mode ) {
                var hidden = element.is( ":hidden" );

                if ( mode === "toggle" ) {
                    mode = hidden ? "show" : "hide";
                }
                if ( hidden ? mode === "hide" : mode === "show" ) {
                    mode = "none";
                }
                return mode;
            },

            // Translates a [top,left] array into a baseline value
            getBaseline: function( origin, original ) {
                var y, x;

                switch ( origin[ 0 ] ) {
                    case "top":
                        y = 0;
                        break;
                    case "middle":
                        y = 0.5;
                        break;
                    case "bottom":
                        y = 1;
                        break;
                    default:
                        y = origin[ 0 ] / original.height;
                }

                switch ( origin[ 1 ] ) {
                    case "left":
                        x = 0;
                        break;
                    case "center":
                        x = 0.5;
                        break;
                    case "right":
                        x = 1;
                        break;
                    default:
                        x = origin[ 1 ] / original.width;
                }

                return {
                    x: x,
                    y: y
                };
            },

            // Creates a placeholder element so that the original element can be made absolute
            createPlaceholder: function( element ) {
                var placeholder,
                    cssPosition = element.css( "position" ),
                    position = element.position();

                // Lock in margins first to account for form elements, which
                // will change margin if you explicitly set height
                // see: http://jsfiddle.net/JZSMt/3/ https://bugs.webkit.org/show_bug.cgi?id=107380
                // Support: Safari
                element.css( {
                    marginTop: element.css( "marginTop" ),
                    marginBottom: element.css( "marginBottom" ),
                    marginLeft: element.css( "marginLeft" ),
                    marginRight: element.css( "marginRight" )
                } )
                    .outerWidth( element.outerWidth() )
                    .outerHeight( element.outerHeight() );

                if ( /^(static|relative)/.test( cssPosition ) ) {
                    cssPosition = "absolute";

                    placeholder = $( "<" + element[ 0 ].nodeName + ">" ).insertAfter( element ).css( {

                        // Convert inline to inline block to account for inline elements
                        // that turn to inline block based on content (like img)
                        display: /^(inline|ruby)/.test( element.css( "display" ) ) ?
                            "inline-block" :
                            "block",
                        visibility: "hidden",

                        // Margins need to be set to account for margin collapse
                        marginTop: element.css( "marginTop" ),
                        marginBottom: element.css( "marginBottom" ),
                        marginLeft: element.css( "marginLeft" ),
                        marginRight: element.css( "marginRight" ),
                        "float": element.css( "float" )
                    } )
                        .outerWidth( element.outerWidth() )
                        .outerHeight( element.outerHeight() )
                        .addClass( "ui-effects-placeholder" );

                    element.data( dataSpace + "placeholder", placeholder );
                }

                element.css( {
                    position: cssPosition,
                    left: position.left,
                    top: position.top
                } );

                return placeholder;
            },

            removePlaceholder: function( element ) {
                var dataKey = dataSpace + "placeholder",
                    placeholder = element.data( dataKey );

                if ( placeholder ) {
                    placeholder.remove();
                    element.removeData( dataKey );
                }
            },

            // Removes a placeholder if it exists and restores
            // properties that were modified during placeholder creation
            cleanUp: function( element ) {
                $.effects.restoreStyle( element );
                $.effects.removePlaceholder( element );
            },

            setTransition: function( element, list, factor, value ) {
                value = value || {};
                $.each( list, function( i, x ) {
                    var unit = element.cssUnit( x );
                    if ( unit[ 0 ] > 0 ) {
                        value[ x ] = unit[ 0 ] * factor + unit[ 1 ];
                    }
                } );
                return value;
            }
        } );

// Return an effect options object for the given parameters:
        function _normalizeArguments( effect, options, speed, callback ) {

            // Allow passing all options as the first parameter
            if ( $.isPlainObject( effect ) ) {
                options = effect;
                effect = effect.effect;
            }

            // Convert to an object
            effect = { effect: effect };

            // Catch (effect, null, ...)
            if ( options == null ) {
                options = {};
            }

            // Catch (effect, callback)
            if ( typeof options === "function" ) {
                callback = options;
                speed = null;
                options = {};
            }

            // Catch (effect, speed, ?)
            if ( typeof options === "number" || $.fx.speeds[ options ] ) {
                callback = speed;
                speed = options;
                options = {};
            }

            // Catch (effect, options, callback)
            if ( typeof speed === "function" ) {
                callback = speed;
                speed = null;
            }

            // Add options to effect
            if ( options ) {
                $.extend( effect, options );
            }

            speed = speed || options.duration;
            effect.duration = $.fx.off ? 0 :
                typeof speed === "number" ? speed :
                    speed in $.fx.speeds ? $.fx.speeds[ speed ] :
                        $.fx.speeds._default;

            effect.complete = callback || options.complete;

            return effect;
        }

        function standardAnimationOption( option ) {

            // Valid standard speeds (nothing, number, named speed)
            if ( !option || typeof option === "number" || $.fx.speeds[ option ] ) {
                return true;
            }

            // Invalid strings - treat as "normal" speed
            if ( typeof option === "string" && !$.effects.effect[ option ] ) {
                return true;
            }

            // Complete callback
            if ( typeof option === "function" ) {
                return true;
            }

            // Options hash (but not naming an effect)
            if ( typeof option === "object" && !option.effect ) {
                return true;
            }

            // Didn't match any standard API
            return false;
        }

        $.fn.extend( {
            effect: function( /* effect, options, speed, callback */ ) {
                var args = _normalizeArguments.apply( this, arguments ),
                    effectMethod = $.effects.effect[ args.effect ],
                    defaultMode = effectMethod.mode,
                    queue = args.queue,
                    queueName = queue || "fx",
                    complete = args.complete,
                    mode = args.mode,
                    modes = [],
                    prefilter = function( next ) {
                        var el = $( this ),
                            normalizedMode = $.effects.mode( el, mode ) || defaultMode;

                        // Sentinel for duck-punching the :animated pseudo-selector
                        el.data( dataSpaceAnimated, true );

                        // Save effect mode for later use,
                        // we can't just call $.effects.mode again later,
                        // as the .show() below destroys the initial state
                        modes.push( normalizedMode );

                        // See $.uiBackCompat inside of run() for removal of defaultMode in 1.14
                        if ( defaultMode && ( normalizedMode === "show" ||
                            ( normalizedMode === defaultMode && normalizedMode === "hide" ) ) ) {
                            el.show();
                        }

                        if ( !defaultMode || normalizedMode !== "none" ) {
                            $.effects.saveStyle( el );
                        }

                        if ( typeof next === "function" ) {
                            next();
                        }
                    };

                if ( $.fx.off || !effectMethod ) {

                    // Delegate to the original method (e.g., .show()) if possible
                    if ( mode ) {
                        return this[ mode ]( args.duration, complete );
                    } else {
                        return this.each( function() {
                            if ( complete ) {
                                complete.call( this );
                            }
                        } );
                    }
                }

                function run( next ) {
                    var elem = $( this );

                    function cleanup() {
                        elem.removeData( dataSpaceAnimated );

                        $.effects.cleanUp( elem );

                        if ( args.mode === "hide" ) {
                            elem.hide();
                        }

                        done();
                    }

                    function done() {
                        if ( typeof complete === "function" ) {
                            complete.call( elem[ 0 ] );
                        }

                        if ( typeof next === "function" ) {
                            next();
                        }
                    }

                    // Override mode option on a per element basis,
                    // as toggle can be either show or hide depending on element state
                    args.mode = modes.shift();

                    if ( $.uiBackCompat !== false && !defaultMode ) {
                        if ( elem.is( ":hidden" ) ? mode === "hide" : mode === "show" ) {

                            // Call the core method to track "olddisplay" properly
                            elem[ mode ]();
                            done();
                        } else {
                            effectMethod.call( elem[ 0 ], args, done );
                        }
                    } else {
                        if ( args.mode === "none" ) {

                            // Call the core method to track "olddisplay" properly
                            elem[ mode ]();
                            done();
                        } else {
                            effectMethod.call( elem[ 0 ], args, cleanup );
                        }
                    }
                }

                // Run prefilter on all elements first to ensure that
                // any showing or hiding happens before placeholder creation,
                // which ensures that any layout changes are correctly captured.
                return queue === false ?
                    this.each( prefilter ).each( run ) :
                    this.queue( queueName, prefilter ).queue( queueName, run );
            },

            show: ( function( orig ) {
                return function( option ) {
                    if ( standardAnimationOption( option ) ) {
                        return orig.apply( this, arguments );
                    } else {
                        var args = _normalizeArguments.apply( this, arguments );
                        args.mode = "show";
                        return this.effect.call( this, args );
                    }
                };
            } )( $.fn.show ),

            hide: ( function( orig ) {
                return function( option ) {
                    if ( standardAnimationOption( option ) ) {
                        return orig.apply( this, arguments );
                    } else {
                        var args = _normalizeArguments.apply( this, arguments );
                        args.mode = "hide";
                        return this.effect.call( this, args );
                    }
                };
            } )( $.fn.hide ),

            toggle: ( function( orig ) {
                return function( option ) {
                    if ( standardAnimationOption( option ) || typeof option === "boolean" ) {
                        return orig.apply( this, arguments );
                    } else {
                        var args = _normalizeArguments.apply( this, arguments );
                        args.mode = "toggle";
                        return this.effect.call( this, args );
                    }
                };
            } )( $.fn.toggle ),

            cssUnit: function( key ) {
                var style = this.css( key ),
                    val = [];

                $.each( [ "em", "px", "%", "pt" ], function( i, unit ) {
                    if ( style.indexOf( unit ) > 0 ) {
                        val = [ parseFloat( style ), unit ];
                    }
                } );
                return val;
            },

            cssClip: function( clipObj ) {
                if ( clipObj ) {
                    return this.css( "clip", "rect(" + clipObj.top + "px " + clipObj.right + "px " +
                        clipObj.bottom + "px " + clipObj.left + "px)" );
                }
                return parseClip( this.css( "clip" ), this );
            },

            transfer: function( options, done ) {
                var element = $( this ),
                    target = $( options.to ),
                    targetFixed = target.css( "position" ) === "fixed",
                    body = $( "body" ),
                    fixTop = targetFixed ? body.scrollTop() : 0,
                    fixLeft = targetFixed ? body.scrollLeft() : 0,
                    endPosition = target.offset(),
                    animation = {
                        top: endPosition.top - fixTop,
                        left: endPosition.left - fixLeft,
                        height: target.innerHeight(),
                        width: target.innerWidth()
                    },
                    startPosition = element.offset(),
                    transfer = $( "<div class='ui-effects-transfer'></div>" );

                transfer
                    .appendTo( "body" )
                    .addClass( options.className )
                    .css( {
                        top: startPosition.top - fixTop,
                        left: startPosition.left - fixLeft,
                        height: element.innerHeight(),
                        width: element.innerWidth(),
                        position: targetFixed ? "fixed" : "absolute"
                    } )
                    .animate( animation, options.duration, options.easing, function() {
                        transfer.remove();
                        if ( typeof done === "function" ) {
                            done();
                        }
                    } );
            }
        } );

        function parseClip( str, element ) {
            var outerWidth = element.outerWidth(),
                outerHeight = element.outerHeight(),
                clipRegex = /^rect\((-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto),?\s*(-?\d*\.?\d*px|-?\d+%|auto)\)$/,
                values = clipRegex.exec( str ) || [ "", 0, outerWidth, outerHeight, 0 ];

            return {
                top: parseFloat( values[ 1 ] ) || 0,
                right: values[ 2 ] === "auto" ? outerWidth : parseFloat( values[ 2 ] ),
                bottom: values[ 3 ] === "auto" ? outerHeight : parseFloat( values[ 3 ] ),
                left: parseFloat( values[ 4 ] ) || 0
            };
        }

        $.fx.step.clip = function( fx ) {
            if ( !fx.clipInit ) {
                fx.start = $( fx.elem ).cssClip();
                if ( typeof fx.end === "string" ) {
                    fx.end = parseClip( fx.end, fx.elem );
                }
                fx.clipInit = true;
            }

            $( fx.elem ).cssClip( {
                top: fx.pos * ( fx.end.top - fx.start.top ) + fx.start.top,
                right: fx.pos * ( fx.end.right - fx.start.right ) + fx.start.right,
                bottom: fx.pos * ( fx.end.bottom - fx.start.bottom ) + fx.start.bottom,
                left: fx.pos * ( fx.end.left - fx.start.left ) + fx.start.left
            } );
        };

    } )();

    /******************************************************************************/
    /*********************************** EASING ***********************************/
    /******************************************************************************/

    ( function() {

// Based on easing equations from Robert Penner (http://www.robertpenner.com/easing)

        var baseEasings = {};

        $.each( [ "Quad", "Cubic", "Quart", "Quint", "Expo" ], function( i, name ) {
            baseEasings[ name ] = function( p ) {
                return Math.pow( p, i + 2 );
            };
        } );

        $.extend( baseEasings, {
            Sine: function( p ) {
                return 1 - Math.cos( p * Math.PI / 2 );
            },
            Circ: function( p ) {
                return 1 - Math.sqrt( 1 - p * p );
            },
            Elastic: function( p ) {
                return p === 0 || p === 1 ? p :
                    -Math.pow( 2, 8 * ( p - 1 ) ) * Math.sin( ( ( p - 1 ) * 80 - 7.5 ) * Math.PI / 15 );
            },
            Back: function( p ) {
                return p * p * ( 3 * p - 2 );
            },
            Bounce: function( p ) {
                var pow2,
                    bounce = 4;

                while ( p < ( ( pow2 = Math.pow( 2, --bounce ) ) - 1 ) / 11 ) {}
                return 1 / Math.pow( 4, 3 - bounce ) - 7.5625 * Math.pow( ( pow2 * 3 - 2 ) / 22 - p, 2 );
            }
        } );

        $.each( baseEasings, function( name, easeIn ) {
            $.easing[ "easeIn" + name ] = easeIn;
            $.easing[ "easeOut" + name ] = function( p ) {
                return 1 - easeIn( 1 - p );
            };
            $.easing[ "easeInOut" + name ] = function( p ) {
                return p < 0.5 ?
                    easeIn( p * 2 ) / 2 :
                    1 - easeIn( p * -2 + 2 ) / 2;
            };
        } );

    } )();

    return $.effects;

} );
