/*!
 * jQuery UI Form Reset Mixin 1.13.2
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 */

//>>label: Form Reset Mixin
//>>group: Core
//>>description: Refresh input widgets when their form is reset
//>>docs: http://api.jqueryui.com/form-reset-mixin/

( function( factory ) {
    "use strict";

    if ( typeof define === "function" && define.amd ) {

        // AMD. Register as an anonymous module.
        define( [
            "jquery",
            "./form",
            "./version"
        ], factory );
    } else {

        // Browser globals
        factory( jQuery );
    }
} )( function( $ ) {
    "use strict";

    return $.ui.formResetMixin = {
        _formResetHandler: function() {
            var form = $( this );

            // Wait for the form reset to actually happen before refreshing
            setTimeout( function() {
                var instances = form.data( "ui-form-reset-instances" );
                $.each( instances, function() {
                    this.refresh();
                } );
            } );
        },

        _bindFormResetHandler: function() {
            this.form = this.element._form();
            if ( !this.form.length ) {
                return;
            }

            var instances = this.form.data( "ui-form-reset-instances" ) || [];
            if ( !instances.length ) {

                // We don't use _on() here because we use a single event handler per form
                this.form.on( "reset.ui-form-reset", this._formResetHandler );
            }
            instances.push( this );
            this.form.data( "ui-form-reset-instances", instances );
        },

        _unbindFormResetHandler: function() {
            if ( !this.form.length ) {
                return;
            }

            var instances = this.form.data( "ui-form-reset-instances" );
            instances.splice( $.inArray( this, instances ), 1 );
            if ( instances.length ) {
                this.form.data( "ui-form-reset-instances", instances );
            } else {
                this.form
                    .removeData( "ui-form-reset-instances" )
                    .off( "reset.ui-form-reset" );
            }
        }
    };

} );
