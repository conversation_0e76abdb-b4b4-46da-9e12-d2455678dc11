(this.webpackJsonp=this.webpackJsonp||[]).push([["main"],{"./app/design/frontend/Diamondair/blank/web/Resources/JavaScript/main.js":function(e,t,r){"use strict";r.r(t);r("./node_modules/core-js/features/array/includes.js"),r("./node_modules/core-js/features/array/from.js"),r("./node_modules/core-js/features/symbol/iterator.js"),Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(e){var t=this;do{if(t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null});var n=function(){var e=window.location.pathname.replace(".html","").split("/"),t=e[1].length>2||0===e[1].length;return{lang:t?"en":e[1],url:t?e.join("/"):e.slice(2).join("/")}};function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e){return function(e){if(Array.isArray(e))return s(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return s(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var c=function(e){return document.querySelector(e)},u=function(e){return document.querySelectorAll(e)},d=!1,f=!1,v=[],p=null,g=null,m=null,b=[],h=null,y=function(e){var t=e.code;return"ArrowDown"===t||"ArrowUp"===t||"Tab"===t||"Escape"===t||16===t},w=function(e,t,r,n){return function(o){var i=o.matches;!i||!1!==m&&null!==m||(m=!0,L(e,t,r,n)),i||!0!==m&&null!==m||(m=!1,L(e,t,r,n))}},L=function(e,t,r,n){document.addEventListener("pointerover",p,!1);var o=[].slice.call(u("".concat(e,".").concat(t)));if(0!==o.length){var i=o.map((function(e){return c("".concat(e.getAttribute(r),".").concat(t))}));o.forEach((function(e){return e.classList.remove(t)})),i.forEach((function(e){null!==e&&(e.classList.remove(t),e.classList.contains(n)&&e.classList.remove(n))}))}};function A(e,t,r,n,o,i,a,l){var s,c,d;v=[].slice.call(u(e)),s=function(e){return function(t){null!==g&&g===t.pointerType&&m&&"touch"===g||!m&&"mouse"===g||(g=m?"touch":t.pointerType,e(g),document.removeEventListener("pointerover",p,!0))}}(n),c=20,p=function(){var e=this,t=arguments,r=function(){d=!1};d||(s.apply(e,t),d=!0,setTimeout(r,c))},L(o,i,a),function(e,t){t&&"function"==typeof t||console.error("Must be a function");var r=window.matchMedia(e).matches,n=window.matchMedia(e);return n.addListener(t),t(n),r}(t,r(o,i,a,l))&&(m=!0)}var E=function(e){var t=window.getComputedStyle(e.value,null),r=t.getPropertyValue("transition-duration"),n=t.getPropertyValue("transition-property");return{exist:"drop"===e.type&&"0s"!==r,animation:n}},O=function(e,t){var r=e.nextElementSibling;if(!t)return r;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}},j=function(e,t,r){return e===t||e===c(t.getAttribute(r))},S=function(e,t,r,n,o,i,a,l){var s=t.classList.contains(n),c=!!t.hasAttribute(l)&&t.hasAttribute(l);return{type:e,value:t,role:r||"default",active:s,isAnimate:"drop"===e&&c,eventType:o,tabActive:"tab"===r&&s&&j(t,i,a)}},k=function(e,t,r,n,o,i,a,s,d){return[].slice.call(u("[".concat(i,'="').concat(r,'"]'))).filter((function(r){return r!==e&&r.classList.contains(t)})).reduce((function(r,i){var u=i.closest(s)?O(i,i.getAttribute(o)):c(i.getAttribute(o)),f=S("toggle",i,n,t,a,e,o),v=S("drop",u,n,t,a,e,o,d);return[].concat(l(r),[f,v])}),[])},P=function(e,t,r,n,o,i,a,l){return o?[S("toggle",e,n,t,a,e,i)]:[].slice.call(u("[".concat(i,'="').concat(r,'"]'))).map((function(r){return S("toggle",r,n,t,a,e,i)}))},x=function(e,t,r,n,o,i,a,l){return o?[S("drop",O(e,r),n,t,a,e,i,l)]:[].slice.call(u(r)).map((function(r){return S("drop",r,n,t,a,e,i,l)}))},q=function(e,t,r,n,o){function i(t){e.value.removeEventListener("transitionend",i),e.value.classList.remove(r),d=!1}function a(t){e.value.removeEventListener("transitionend",a),f=!1,e.value.enterLocked=!1,b.length>0&&F(e)&&V(o)}n===M||n!==M&&!e.active?function(e){f=!0,e.value.removeAttribute("data-toggle-hidden",!0),window.requestAnimationFrame((function(){e.value.classList.add(r),window.requestAnimationFrame((function(){e.value.classList.add(t),e.value.addEventListener("transitionend",a)}))}))}(e):(n===_||n!==M&&e.active)&&function(e){"tab"===e.role?(e.value.classList.remove(t),e.value.classList.remove(r)):(e.value.setAttribute("data-toggle-hidden",!0),e.value.addEventListener("transitionend",i),e.value.classList.remove(t),d=!0)}(e)},C=function(e,t,r,n){function o(t){t.target.removeEventListener("transitionend",o),l(t.target),b.length>0&&F(e)&&V(n)}function i(e){e.target.removeEventListener("transitionend",i),a(e.target)}var a=function(e){e.classList.remove(t),e.classList.remove(r),e.style.height=null,d=!1},l=function(e){e.classList.remove(t),e.style.height=null,f=!1};return e.active?function(e){d=!0;var r=e.value.scrollHeight;e.value.setAttribute("data-toggle-hidden",!0),window.requestAnimationFrame((function(){e.value.style.height=r+"px",e.value.classList.add(t),window.requestAnimationFrame((function(){e.value.addEventListener("transitionend",i),e.value.style.height="0px"}))}))}(e):function(e){f=!0,e.value.classList.add(r),e.value.classList.add(t);var n=e.value.scrollHeight;e.value.style.height=n+"px",e.value.removeAttribute("data-toggle-hidden",!0),e.value.addEventListener("transitionend",o)}(e)},T=function(e){if("toggle"!==e.type){e.value.setAttribute("style","position: absolute; visibility: hidden; display: block; pointer-events: none"),e.value.classList.remove("is--position-bottom","is--position-top","is--position-left","is--position-right");var t,r,n,o=(t=e.value,r=t.getBoundingClientRect(),(n={}).top=r.top<0,n.left=r.left<0,n.bottom=r.bottom>(window.innerHeight||document.documentElement.clientHeight),n.right=r.right>(window.innerWidth||document.documentElement.clientWidth),n.any=n.top||n.left||n.bottom||n.right,n.all=n.top&&n.left&&n.bottom&&n.right,n);e.value.removeAttribute("style"),o.top&&(e.value.classList.add("is--position-bottom"),e.value.classList.remove("is--position-top")),o.bottom&&(e.value.classList.add("is--position-top"),e.value.classList.remove("is--position-bottom")),o.left&&(e.value.classList.add("is--position-left"),e.value.classList.remove("is--position-right")),o.right&&(e.value.classList.add("is--position-right"),e.value.classList.remove("is--position-left"))}},D=window.PointerEvent?{end:"pointerup",enter:"pointerenter",leave:"pointerleave"}:{end:"touchend",enter:"mouseenter",leave:"mouseleave"},M=D.enter,_=D.leave,I=[M,_],H=function(e,t,r,n){var o=e.querySelector(r);return{item:o,type:t,active:o.classList.contains(n)&&t===M}},z=function(e,t,r){var n=!("drop"!==e.type||!e.value.hasAttribute(t))&&e.value.querySelectorAll("[required]");if(!n)return!1;if(n){var o=0!==[].slice.call(n).filter((function(e){return!e.checkValidity()})).length&&o;return o?(o[0].focus(),o[0].classList.add(r),setTimeout((function(){var e=o[0].getBoundingClientRect().top;window.scrollBy({top:e,left:0,behavior:"smooth"})}),250),!0):(n.forEach((function(e){return e.classList.contains(r)&&e.classList.remove(r)})),!1)}},B=function(e,t,r,n){if(b.forEach((function(e){return e.element.classList.contains(n)&&e.element.classList.remove(n)})),b.length>0&&!b[0].element.contains(t))for(var o=0;o<b.length;o++)b.pop();if(e.active){if(e.active)for(var i=0;i<b.length;i++)if(b[i].element===e.value){1===b.length&&b[0].element.removeEventListener("keydown",r),b.splice(i,1);break}}else{var a={active:t,element:e.value};b.push(a)}},V=function(e){var t=b.length;b[t-1].element.classList.add(e),(h=[].slice.call(b[0].element.querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex="0"], [contenteditable], audio[controls], video[controls], summary')).filter((function(e){return function e(t){var r=window.getComputedStyle(t);return"none"!==r.display&&"hidden"!==r.visibility&&(!t.parentElement||e(t.parentElement))}(e)}))).unshift(b[0].active),1===t&&h.length>1&&h[1].focus()},F=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.eventType;return("click"===t||"keydown"===t)&&"drop"===e.type&&"tab"!==e.role&&"accordion"!==e.role},G={selectorToggle:"[data-toggle]",selectorTogglePrevent:"[data-toggle-prevent]",selectorGlobal:"[data-toggle-global]",selectorGroup:"[data-toggle-group]",selectorValidate:"[data-toggle-validate]",selectorRole:"[data-toggle-role]",selectorBack:"[data-toggle-back]",selectorNext:"[data-toggle-next]",selectorAnimate:"[data-toggle-animate]",selectorHover:"[data-toggle-hover]",toggleActiveClass:"is--active",toggleErrorClass:"is--error",toggleCollapseClass:"is--collapsing",toggleShowClass:"is--show",toggleCurrentClass:"is--current",onHover:!1,onMediaQuery:"(max-width: 992px)",disableIfMedia:"[data-toggle-media]",disableIfNotMedia:"[data-toggle-not-media]",stopVideo:!0,callbackOpen:!1,callbackClose:!1,callbackToggle:!1},R=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=i(i({},G),e),r=t.selectorToggle,n=t.selectorTogglePrevent,o=t.selectorGlobal,a=t.selectorGroup,s=t.selectorValidate,p=t.selectorRole,L=t.selectorBack,O=t.selectorNext,j=t.selectorAnimate,S=t.selectorHover,D=t.toggleActiveClass,R=t.toggleErrorClass,N=t.toggleCollapseClass,J=t.toggleShowClass,U=t.toggleCurrentClass,Q=t.onHover,W=t.onMediaQuery,K=t.disableIfMedia,Y=t.disableIfNotMedia,$=(t.stopVideo,t.callbackOpen),X=t.callbackClose,Z=t.callbackToggle,ee=t.splitselectorToggle,te=void 0===ee?r.replace(/\[|\]/g,""):ee,re=t.splitselectorTogglePrevent,ne=(void 0===re&&n.replace(/\[|\]/g,""),t.splitselectorValidate),oe=void 0===ne?s.replace(/\[|\]/g,""):ne,ie=t.splitselectorGroup,ae=void 0===ie?a.replace(/\[|\]/g,""):ie,le=t.splitselectorAnimate,se=void 0===le?j.replace(/\[|\]/g,""):le,ce=t.splitselectorHover,ue=void 0===ce?S.replace(/\[|\]/g,""):ce,de=t.splitselectorRole,fe=void 0===de?p.replace(/\[|\]/g,""):de,ve=t.splitselectorBack,pe=void 0===ve?L.replace(/\[|\]/g,""):ve,ge=c("body"),me=!!navigator.userAgent.match(/(iPad|iPhone|iPod)/g),be=function(){ye(),me&&ge.classList.add("is--ios"),Q&&A(S,W,w,je,o,D,te,J),we(),he()};function he(){var e=document.querySelector("body");new MutationObserver((function(e){0!==e.filter((function(e){return!!(e.target&&e.target.children&&e.target.children.length>0&&"true"===e.target.children[0].dataset.toggleHover)})).length&&A(S,W,w,je,o,D,te,J)})).observe(e,{childList:!0,subtree:!0})}var ye=function(){me&&ge.classList.remove("is--ios"),document.removeEventListener("click",Ae,!1),document.removeEventListener("keydown",ke,!1),Q&&m&&v.map((function(e){I.map((function(t){e.removeEventListener(t,Oe)}))}))},we=function(){document.addEventListener("click",Ae,!1),document.addEventListener("keydown",ke,!1)},Le=function(e){return"mouse"===g&&e.target.closest(r)&&e.target.closest(r).parentElement.hasAttribute(ue)||e.target.closest(K)&&m||e.target.closest(Y)&&!m},Ae=function(e){Le(e)||(e.target.closest(r)||e.target.closest(L)?(e.preventDefault(),f||d||xe(e)):Me(e))},Ee=function(e){return!e.target.matches(S)||e.target.closest(K)&&m||e.target.closest(Y)&&!m};function Oe(e){if(!Ee(e)&&(e.type===M&&(this.enterLocked=!0),this.enterLocked||e.type!==M)){var t=H(e.target,e.type,r,D);t.active||xe(t)}}var je=function(e){"touch"===e&&v.map((function(e){e.removeEventListener(M,Oe,!1),e.removeEventListener(_,Oe,!1)})),"mouse"===e&&v.map((function(e){e.addEventListener(M,Oe,!1),e.addEventListener(_,Oe,!1)}))},Se=function(e){return!e.target.closest(r)||!e.target.closest(S)||"Enter"!==e.code||e.target.closest(K)&&m||e.target.closest(Y)&&!m},ke=function(e){f||d||Se(e)&&!y(e)||(document.activeElement,Se(e)?y(e)&&b.length>0&&De(e):(e.preventDefault(),e.stopPropagation(),xe(e)))},Pe=function(e){if(!e.closest(L))return!1;var t=e.closest(L);return t.closest(t.getAttribute(pe)).querySelector(r)},xe=function(e){var t=e.target?e.target.closest(r)||Pe(e.target):e.item,n=!!e.type&&e.type,o=t.getAttribute(te),i=t.getAttribute(ae),a=t.getAttribute(fe),s=t.closest(O),c=i?k(t,D,i,a,te,ae,n,O,se):[],u=P(t,D,o,a,s,te,n),d=x(t,D,o,a,s,te,n,se),f=[].concat(l(c),l(u),l(d));if(Z&&Z(t,f,m),!u[0].tabActive){var v=f.filter((function(e){return e.active&&"drop"===e.type&&e.value.hasAttribute(oe)}));if(v)if(z(v,oe,R))return;for(var p=0;p<f.length;p++)qe(f[p],N,D,n,t)}},qe=function(e,t,r,n,o){var i=e.isAnimate,a=E(e),l=a.exist,s=a.animation.match(/height/gi);F(e,n)&&B(e,o,De,U),i&&s?C(e,t,r,U):i&&l?q(e,J,r,n,U):e.active?Te(e):Ce(e)},Ce=function(e){$&&$(e),"tooltip"===e.role&&T(e),e.value.classList.add(D),"toggle"===e.type&&e.value.setAttribute("aria-expanded",!0),"drop"===e.type&&e.value.removeAttribute("data-toggle-hidden",!0),"overlay"===e.role&&document.querySelector("body").classList.add("is--overlay"),b.length>0&&F(e)&&V(U)},Te=function(e){X&&X(e),e.value.classList.remove(D),"toggle"===e.type&&e.value.setAttribute("aria-expanded",!1),"drop"===e.type&&e.value.setAttribute("data-toggle-hidden",!0),"overlay"===e.role&&document.querySelector("body").classList.remove("is--overlay")},De=function(e){document.activeElement;var t={item:b[0].active,type:e.type},r=h[1],n=h[h.length-1];"Tab"===e.code?e.shiftKey?document.activeElement===r&&xe(t):document.activeElement===n&&xe(t):"Escape"===e.code&&(h[0].focus(),xe(t))},Me=function(e){var t=[].slice.call(u("".concat(o,".").concat(D)));if(0!==t.length&&(!e||null===e.target.closest(t[0].getAttribute(te)))){var r=t.map((function(e){return c("".concat(e.getAttribute(te),".").concat(D))}));t.forEach((function(e){return e.classList.remove(D)})),r.forEach((function(e){null!==e&&(e.classList.remove(D),e.classList.contains(J)&&e.classList.remove(J))}))}};be()};function N(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function J(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var U=function(e){var t=[].slice.call(document.querySelectorAll(".lazyload")),r=function(e){var t=e.getAttribute("data-src"),r=e.getAttribute("data-srcset"),n=e.getAttribute("data-background");(t||r)&&function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;t&&(e.src=t,e.setAttribute("src",t)),r&&e.setAttribute("srcset",r),e.classList.remove("lazyload"),e.classList.add("lazyloaded")}(e,t,r),n&&function(e,t){e.style.backgroundImage="url(".concat(t.trim(),")"),e.classList.remove("lazyload"),e.classList.add("lazyloaded")}(e,n)};if("IntersectionObserver"in window){var n=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?N(Object(r),!0).forEach((function(t){J(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({threshold:0,rootMargin:"50px 0px 0px 50px"},e),o=new IntersectionObserver((function(e,t){e.forEach((function(e){e.isIntersecting&&(r(e.target),t.unobserve(e.target))}))}),n);t.forEach((function(e){return o.observe(e)}))}else t.forEach((function(e){return r(e)}))};var Q=function(e){U(e);var t=document.querySelector("body");new MutationObserver((function(t){0!==t.filter((function(e){return e.addedNodes.length>0&&e.target.querySelector(".lazyload")})).length&&U(e)})).observe(t,{childList:!0,subtree:!0})},W=r("./node_modules/tiny-slider/src/tiny-slider.js");function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Y(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var $=function(e,t){var r=[].slice.call(document.querySelectorAll(e||"[tns-slider]"));(r||0!==r.length)&&r.forEach((function(e){var r=!!e.querySelector(".slider-controls")&&e.parentElement.querySelector(".slider-controls"),n=e.getAttribute("items-per-slide")?e.getAttribute("items-per-slide"):1,o=e.getAttribute("gutter-width")?e.getAttribute("gutter-width"):0;Object(W.a)(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach((function(t){Y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({container:e.querySelector(".slider-list"),controlsContainer:r,loop:!0,arrowKeys:!0,nav:!1,gutter:o,items:n,lazyload:!0,lazyloadSelector:".lazyload"},t));r&&setTimeout((function(){r.querySelector('[data-controls="prev"]').setAttribute("tabindex","0"),r.querySelector('[data-controls="next"]').setAttribute("tabindex","0")}),1e3)}))};function X(e){return function(e){if(Array.isArray(e))return Z(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Z(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Z(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Z(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ee=function(){var e=X(document.querySelectorAll(".sidebar-nav__parent"));if(0!==e.length){var t=n(),r=(t.lang,t.url),o=e.filter((function(e){var t=e.textContent.trim().toLowerCase().replace(" & ","-").replace(/\s/g,"-");if(function(e,t){return e.split("/").includes(t)}(r,t))return e}));o.length>0&&o[0].closest(".sidebar-nav__category").classList.add("is--active")}},te=function(){if(document.querySelector(".header + .slider-main")){var e=document.querySelector("body"),t=document.querySelector(".header");e.classList.add("header-transparent"),t.classList.add("header-transparent")}},re={en:'<p>You are using an old browser version of Internet Explorer. For best user experience within our Diamond Pilot Shop please use a current <strong class="text--primary">browser</strong> like <a href="https://www.google.com/intl/de/chrome/" target="_blank" rel="nofollow">Google Chrome</a>, <a href="https://www.mozilla.org/firefox/new/" target="_blank" rel="nofollow">Mozilla Firefox</a> or <a href="https://www.microsoft.com/de-at/windows/microsoft-edge" target="_blank" rel="nofollow">Microsoft Edge</a>.</p>',de:'<p>Unser Webshop ist nicht mehr für die Darstellung mit Internet Explorer 11 optimiert. Bitte wechseln Sie zu einem aktuellen <strong class="text--primary">Browser</strong> wie <a href="https://www.google.com/intl/de/chrome/" target="_blank" rel="nofollow">Google Chrome</a>, <a href="https://www.mozilla.org/firefox/new/" target="_blank" rel="nofollow">Mozilla Firefox</a> oder <a href="https://www.microsoft.com/de-at/windows/microsoft-edge" target="_blank" rel="nofollow">Microsoft Edge</a>.</p>'},ne=function(e,t){return function(){document.body.removeChild(e),document.body.removeChild(t),sessionStorage.setItem("skipIESupportHint","true")}},oe=function(){var e,t,r,o,i=window.navigator.userAgent.indexOf("Trident/"),a=sessionStorage.getItem("skipIESupportHint");-1===i||a||(!function(e,t){var r='<div class="'.concat(e,'">\n').concat(re[t],'\n<button><span class="i-burger_close"></span></button>\n</div><div class="').concat(e,'__overlay"></div>');document.body.insertAdjacentHTML("beforeend",r)}("ie-notification",n().lang),e="ie-notification",t=document.querySelector(".".concat(e)),r=document.querySelector(".".concat(e,"__overlay")),o=t.querySelector("button"),t&&(console.log("content",t),o.addEventListener("click",ne(t,r))))};r("./node_modules/es6-promise/dist/es6-promise.js").polyfill(),oe(),ee(),window.addEventListener("load",(function(){$(".product-slider",{mouseDrag:!0,items:2,controlsContainer:".product-slider .product-list__controls",responsive:{640:{items:3},1024:{items:4}}}),$(".product-slider--full",{mouseDrag:!0,items:2,responsive:{640:{items:3},1024:{items:4}}}),$(".product-slider--half",{mouseDrag:!0,items:2}),$(".slider-main",{mouseDrag:!0,items:1,nav:!0,navAsThumbnails:!0,controls:!1,autoplay:!0,autoplayTimeout:4e3}),$(".ads-slider",{mouseDrag:!0,items:2,responsive:{640:{items:3},1024:{items:4}}})})),R({onHover:!0,onnHoverMediaQuery:"(max-width: 768px)"}),Q(),te(),function(){var e=document.querySelector(".to-the-top-button");if(e){var t,r,n,o=(t=function(t){window.pageYOffset>=300?e.classList.add("to-the-top-button--show"):e.classList.remove("to-the-top-button--show")},r=100,function(){for(var e=this,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];clearTimeout(n),n=setTimeout((function(){return t.apply(e,i)}),r)});window.addEventListener("scroll",o);e.addEventListener("click",(function(){window.scrollTo({top:0,behavior:"smooth"})}))}}()},0:function(e,t,r){e.exports=r("./app/design/frontend/Diamondair/blank/web/Resources/JavaScript/main.js")}},[[0,"manifest-diamondairblank","vendors-diamondairblank"]]]);