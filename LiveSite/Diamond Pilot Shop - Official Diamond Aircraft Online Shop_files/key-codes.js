/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * @api
 */
define([], function () {
    'use strict';

    return {
        13: 'enterKey',
        27: 'escapeKey',
        40: 'pageDownKey',
        38: 'pageUpKey',
        32: 'spaceKey',
        9:  'tabKey',
        37: 'pageLeftKey',
        39: 'pageRightKey',
        17: 'ctrlKey',
        18: 'altKey',
        16: 'shiftKey',
        191: 'forwardSlashKey',
        66: 'bKey',
        73: 'iKey',
        85: 'uKey'
    };
});
