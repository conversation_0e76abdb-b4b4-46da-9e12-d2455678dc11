var e=Object.freeze({initialize:function({modulePath:e=".",importFunctionName:t="__import__"}={}){try{self[t]=new Function("u","return import(u)")}catch(n){const r=new URL(e,location),i=e=>{URL.revokeObjectURL(e.src),e.remove()};self[t]=e=>new Promise(((n,s)=>{const o=new URL(e,r);if(self[t].moduleMap[o])return n(self[t].moduleMap[o]);const a=new Blob([`import * as m from '${o}';`,`${t}.moduleMap['${o}']=m;`],{type:"text/javascript"}),c=Object.assign(document.createElement("script"),{type:"module",src:URL.createObjectURL(a),onerror(){s(new Error(`Failed to import: ${e}`)),i(c)},onload(){n(self[t].moduleMap[o]),i(c)}});document.head.appendChild(c)})),self[t].moduleMap={}}}});function t(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function n(){return n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},n.apply(null,arguments)}function r(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var n=1;n<arguments.length;n++){var i=null!=arguments[n]?arguments[n]:{};n%2?r(Object(i),!0).forEach((function(n){t(e,n,i[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):r(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function s(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(r=0;r<s.length;r++)n=s[r],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function a(e,t,n,r,i,s,o){try{var a=e[s](o),c=a.value}catch(e){return void n(e)}a.done?t(c):Promise.resolve(c).then(r,i)}function c(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var s=e.apply(t,n);function o(e){a(s,r,i,o,c,"next",e)}function c(e){a(s,r,i,o,c,"throw",e)}o(void 0)}))}}function u(e,t,n){return t=v(t),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,E()?Reflect.construct(t,n||[],v(e).constructor):t.apply(e,n))}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,b(r.key),r)}}function p(e,t,n){return t&&d(e.prototype,t),n&&d(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function f(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=O(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){a=!0,s=e},f:function(){try{o||null==n.return||n.return()}finally{if(a)throw s}}}}function h(e,t,n){return(t=b(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(){return g="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=function(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=v(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(arguments.length<3?e:n):i.value}},g.apply(null,arguments)}function v(e){return v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},v(e)}function S(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&y(e,t)}function E(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(E=function(){return!!e})()}function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){h(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function I(){I=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},s="function"==typeof Symbol?Symbol:{},o=s.iterator||"@@iterator",a=s.asyncIterator||"@@asyncIterator",c=s.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function l(e,t,n,r){var s=t&&t.prototype instanceof S?t:S,o=Object.create(s.prototype),a=new L(r||[]);return i(o,"_invoke",{value:O(e,n,a)}),o}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=l;var p="suspendedStart",f="suspendedYield",h="executing",g="completed",v={};function S(){}function E(){}function _(){}var m={};u(m,o,(function(){return this}));var y=Object.getPrototypeOf,C=y&&y(y(D([])));C&&C!==n&&r.call(C,o)&&(m=C);var T=_.prototype=S.prototype=Object.create(m);function b(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function A(e,t){function n(i,s,o,a){var c=d(e[i],e,s);if("throw"!==c.type){var u=c.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?t.resolve(l.__await).then((function(e){n("next",e,o,a)}),(function(e){n("throw",e,o,a)})):t.resolve(l).then((function(e){u.value=e,o(u)}),(function(e){return n("throw",e,o,a)}))}a(c.arg)}var s;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return s=s?s.then(i,i):i()}})}function O(t,n,r){var i=p;return function(s,o){if(i===h)throw Error("Generator is already running");if(i===g){if("throw"===s)throw o;return{value:e,done:!0}}for(r.method=s,r.arg=o;;){var a=r.delegate;if(a){var c=N(a,r);if(c){if(c===v)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===p)throw i=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=h;var u=d(t,n,r);if("normal"===u.type){if(i=r.done?g:f,u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(i=g,r.method="throw",r.arg=u.arg)}}}function N(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,N(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var s=d(i,t.iterator,n.arg);if("throw"===s.type)return n.method="throw",n.arg=s.arg,n.delegate=null,v;var o=s.arg;return o?o.done?(n[t.resultName]=o.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function w(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function R(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(w,this),this.reset(!0)}function D(t){if(t||""===t){var n=t[o];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,s=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return s.next=s}}throw new TypeError(typeof t+" is not iterable")}return E.prototype=_,i(T,"constructor",{value:_,configurable:!0}),i(_,"constructor",{value:E,configurable:!0}),E.displayName=u(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===E||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,c,"GeneratorFunction")),e.prototype=Object.create(T),e},t.awrap=function(e){return{__await:e}},b(A.prototype),u(A.prototype,a,(function(){return this})),t.AsyncIterator=A,t.async=function(e,n,r,i,s){void 0===s&&(s=Promise);var o=new A(l(e,n,r,i),s);return t.isGeneratorFunction(n)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},b(T),u(T,c,"Generator"),u(T,o,(function(){return this})),u(T,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=D,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(R),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return a.type="throw",a.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var s=this.tryEntries.length-1;s>=0;--s){var o=this.tryEntries[s],a=o.completion;if("root"===o.tryLoc)return i("end");if(o.tryLoc<=this.prev){var c=r.call(o,"catchLoc"),u=r.call(o,"finallyLoc");if(c&&u){if(this.prev<o.catchLoc)return i(o.catchLoc,!0);if(this.prev<o.finallyLoc)return i(o.finallyLoc)}else if(c){if(this.prev<o.catchLoc)return i(o.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return i(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var s=i;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var o=s?s.completion:{};return o.type=e,o.arg=t,s?(this.method="next",this.next=s.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),R(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;R(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:D(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function y(e,t){return y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},y(e,t)}function C(e,t,n,r){var i=g(v(1&r?e.prototype:e),t,n);return 2&r&&"function"==typeof i?function(e){return i.apply(n,e)}:i}function T(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||O(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}function A(e){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},A(e)}function O(e,t){if(e){if("string"==typeof e)return o(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}function N(e){var t="function"==typeof Map?new Map:void 0;return N=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return function(e,t,n){if(E())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,t);var i=new(e.bind.apply(e,r));return n&&y(i,n.prototype),i}(e,arguments,v(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),y(n,e)},N(e)}var w=function(e,t){return w=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},w(e,t)};function R(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}w(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var L,D,P,V,U,k,M,F,x,G,B,H,j,$,Y,W,z,K=function(){return K=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},K.apply(this,arguments)};function J(e,t,n,r){return new(n||(n=Promise))((function(i,s){function o(e){try{c(r.next(e))}catch(e){s(e)}}function a(e){try{c(r.throw(e))}catch(e){s(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(o,a)}c((r=r.apply(e,t||[])).next())}))}function q(e,t){var n,r,i,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},o=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return o.next=a(0),o.throw=a(1),o.return=a(2),"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(a){return function(c){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;o&&(o=0,a[0]&&(s=0)),s;)try{if(n=1,r&&(i=2&a[0]?r.return:a[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,a[1])).done)return i;switch(r=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,r=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((i=(i=s.trys).length>0&&i[i.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){s.label=a[1];break}if(6===a[0]&&s.label<i[1]){s.label=i[1],i=a;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(a);break}i[2]&&s.ops.pop(),s.trys.pop();continue}a=t.call(e,s)}catch(e){a=[6,e],r=0}finally{n=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function X(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,s=n.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=s.next()).done;)o.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=s.return)&&n.call(s)}finally{if(i)throw i.error}}return o}function Q(e,t,n){if(n||2===arguments.length)for(var r,i=0,s=t.length;i<s;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}function Z(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(n){t[n]=e[n]&&function(t){return new Promise((function(r,i){!function(e,t,n,r){Promise.resolve(r).then((function(t){e({value:t,done:n})}),t)}(r,i,(t=e[n](t)).done,t.value)}))}}}"function"==typeof SuppressedError&&SuppressedError,function(e){e.CONSENT_RESPONSE="consent-response",e.CONSENT_UI="consent-ui"}(L||(L={})),function(e){e.ACCEPT="accept",e.REJECT="reject",e.DISMISS="dismiss",e.FULLSCREEN="enter-fullscreen",e.READY="ready"}(D||(D={})),function(e){e[e.TCF_V2=2]="TCF_V2",e[e.CCPA=3]="CCPA"}(P||(P={})),function(e){e[e.FALSE=0]="FALSE",e[e.TRUE=1]="TRUE"}(V||(V={})),function(e){e[e.DATA_LAYER=0]="DATA_LAYER",e[e.WINDOW_EVENT=1]="WINDOW_EVENT"}(U||(U={})),function(e){e[e.MAJOR=0]="MAJOR",e[e.MINOR=1]="MINOR",e[e.PATCH=2]="PATCH"}(k||(k={})),function(e){e.CALL_INIT="Usercentrics: You have to call the init method before!",e.DATA_LOCAL="Usercentrics: disableServerConsents and controllerId should not be present at the same time in the InitOptions!",e.UNKNOWN_VARIANT="Usercentrics: Unknown variant",e.NOT_CCPA="Usercentrics: CCPA was not initialized",e.NOT_DEFAULT="Usercentrics: GDPR was not initialized",e.NOT_TCF="Usercentrics: TCF was not initialized"}(M||(M={})),function(e){e.BANNER="BANNER",e.WALL="WALL"}(F||(F={})),function(e){e.CENTER="CENTER",e.SIDE="SIDE"}(x||(x={})),function(e){e[e.DARK=0]="DARK",e[e.LIGHT=1]="LIGHT"}(G||(G={})),function(e){e.LINK="LINK",e.BUTTON="BUTTON",e.MORE_LINK_BUTTON="MORE_LINK_BUTTON"}(B||(B={})),function(e){e.LEFT="LEFT",e.RIGHT="RIGHT"}(H||(H={})),function(e){e.HORIZONTAL="HORIZONTAL",e.VERTICAL="VERTICAL"}(j||(j={})),function(e){e.ALL="ALL",e.SHORT="SHORT"}($||($={})),function(e){e.CROSS_DEVICE_DATA_NOT_AVAILABLE="Usercentrics: Cross Device Consents data is not available",e.CROSS_DEVICE_TCF_DATA_NOT_AVAILABLE="Usercentrics: Cross Device TCF data is not available",e.CROSS_DEVICE_FEATURE_DISABLED="Usercentrics: The `Cross-Device Consent Sharing` feature is not enabled. Please contact the support team in order to enable this feature for your configuration"}(Y||(Y={})),function(e){e.CROSS_DOMAIN_DATA_NOT_AVAILABLE="Usercentrics: Cross Domain Consents data is not available",e.CROSS_DOMAIN_TCF_DATA_NOT_AVAILABLE="Usercentrics: Cross Domain TCF data is not available",e.CROSS_DOMAIN_FEATURE_NOT_AVAILABLE="Usercentrics: Cross Domain Consent Sharing is not available.",e.CROSS_DOMAIN_LANGUAGE_NOT_AVAILABLE="Usercentrics: Cross Domain Consent language is not available",e.CROSS_DOMAIN_SET_DATA_FAILURE="Usercentrics: Unable to set Cross Domain data",e.CROSS_DOMAIN_SET_TCF_DATA_FAILURE="Usercentrics: Unable to set Cross Domain TCF data",e.CROSS_DOMAIN_IFRAME_ERROR="Usercentrics: Iframe error",e.CROSS_DOMAIN_IFRAME_NOT_FOUND="Usercentrics: Cross Domain iFrame not found",e.CROSS_DOMAIN_IFRAME_LOAD_ERROR="Usercentrics: IFrame did not load"}(W||(W={})),function(e){e.CLEAR="clear",e.GET_CROSS_DOMAIN_LANGUAGE="getCrossDomainLanguage",e.GET_CROSS_DOMAIN_SESSION_DATA="getCrossDomainSessionData",e.GET_CROSS_DOMAIN_TCF_DATA="getCrossDomainTcfData",e.GET_CROSS_DOMAIN_CCPA_DATA="getCrossDomainCcpaData",e.GET_TC_STRING="getTCString",e.PING="ping",e.SET_CROSS_DOMAIN_DATA="setCrossDomainData",e.SET_CROSS_DOMAIN_TCF_DATA="setCrossDomainTcfData",e.SET_CROSS_DOMAIN_CCPA_DATA="setCrossDomainCcpaData",e.SET_TC_STRING="setTCString"}(z||(z={}));var ee,te,ne="ucMobileSdk",re=["*.usercentrics-sandbox.eu","*.usercentrics.eu"],ie="4.47.0",se=1,oe="1---",ae="__uspapiLocator",ce=/^[1][nNyY-][nNyY-][nNyY-]$/,ue="__uspapi";!function(e){e.CCPA="uc_usprivacy",e.CCPA_DATA="uc_ccpa",e.CONSENTS_BUFFER="uc_consents_buffer",e.CONSENTS_V2_BUFFER="uc_consents_v2_buffer",e.GCM_DATA="uc_gcm",e.LEGACY_SETTINGS="ucSettings",e.SERVICES="uc_services",e.SETTINGS="uc_settings",e.TCF="uc_tcf",e.USER_INTERACTION="uc_user_interaction",e.AB_TEST_VARIANT="uc_ab_variant"}(ee||(ee={})),(te||(te={})).USER_COUNTRY="uc_user_country";var le,de,pe,fe,he="uc-cross-domain-bridge",ge="".concat("https://app.usercentrics.eu","/browser-sdk/").concat(ie,"/cross-domain-bridge.html"),ve="".concat("https://app.eu.usercentrics.eu","/browser-sdk/").concat(ie,"/cross-domain-bridge.html"),Se=function(){function e(){}return e.setDomainBridgeUri=function(t){var n="";t&&(t.crossDomainConsentSharingIFrame?n=t.crossDomainConsentSharingIFrame:t.app&&(n="".concat(t.app,"/browser-sdk/").concat(ie,"/cross-domain-bridge.html")));var r=""!==n?n:ge,i=ve;e.domainBridgeUri=e.useEuCdn?i:r},e.getDomainBridgeUri=function(){return e.domainBridgeUri},e.init=function(t,n){return J(this,void 0,void 0,(function(){return q(this,(function(r){return e.setDomainBridgeUri(n),e.setUseEuCdn((null==t?void 0:t.useEuCdn)||!1),e.setDomainBridgeUri(n),[2,this.initIFrame(e.getDomainBridgeUri(),he)]}))}))},e.initIFrame=function(t,n){return J(this,void 0,void 0,(function(){var r=this;return q(this,(function(i){return[2,new Promise((function(i,s){var o=e.createIFrame(t,n);o.onload=function(){return J(r,void 0,void 0,(function(){var r;return q(this,(function(a){return r=setTimeout((function(){o={},s(new Error(W.CROSS_DOMAIN_IFRAME_LOAD_ERROR))}),1e3),e.queryIFrame(t,n,z.PING).then((function(){clearTimeout(r),i(!0)})).catch((function(e){clearTimeout(r),s(e)})),[2]}))}))},o.onerror=function(){return J(r,void 0,void 0,(function(){return q(this,(function(e){return s(new Error(W.CROSS_DOMAIN_IFRAME_ERROR)),[2]}))}))},e.appendIFrame(o)}))]}))}))},e.setIsCrossDomainAvailable=function(t){e.crossDomainAvailable=t},e.isCookieBridgeAvailable=function(){return e.cookieBridgeAvailable},e.setIsCookieBridgeAvailable=function(t){e.cookieBridgeAvailable=t},e.isCrossDomainAvailable=function(){return e.crossDomainAvailable},e.getCrossDomainId=function(){return e.crossDomainId},e.setCrossDomainId=function(t){e.crossDomainId="".concat("uc_cross_domain_data","_").concat(t)},e.setCookieBridgeDomain=function(e,t){this.cookieBridgeDomain="".concat(e).concat(t)},e.setUseEuCdn=function(t){e.useEuCdn=t},e.createIFrame=function(t,n){e.removeIFrame(n);var r=document.createElement("iframe");return r.style.display="none",r.id=n,r.src=t,r},e.removeIFrame=function(e){var t=document.getElementById(e);t&&t.parentNode&&t.parentNode.removeChild(t)},e.queryIFrame=function(e,t,n,r,i){return J(this,void 0,void 0,(function(){var s;return q(this,(function(o){if(!(s=document.getElementById(t))||!s.id)throw new Error(W.CROSS_DOMAIN_IFRAME_NOT_FOUND);return[2,new Promise((function(t,o){var a=JSON.stringify({crossDomainId:i,method:n,payload:r}),c=new MessageChannel;c.port1.onmessage=function(e){var n=JSON.parse(e.data),r=n.success,i=n.data;r?t(i):o(e)},s&&s.contentWindow&&s.contentWindow.postMessage(a,e,[c.port2])}))]}))}))},e.appendIFrame=function(e){try{document.body?document.body.appendChild(e):document.addEventListener("DOMContentLoaded",(function(){document.body.appendChild(e)}))}catch(e){return new Error(W.CROSS_DOMAIN_IFRAME_ERROR)}return null},e.query=function(t,n){return J(this,void 0,void 0,(function(){return q(this,(function(r){return[2,e.queryIFrame(e.getDomainBridgeUri(),he,t,n,e.crossDomainId)]}))}))},e.getCrossDomainLanguage=function(){return e.query(z.GET_CROSS_DOMAIN_LANGUAGE)},e.setCrossDomainData=function(t){return J(this,void 0,void 0,(function(){return q(this,(function(n){return[2,e.query(z.SET_CROSS_DOMAIN_DATA,(r=t,{consents:r.services.map((function(e){return e.history.map((function(t){return{action:t.action,consentId:"",settingsVersion:t.versions.settings,status:t.status,templateId:e.id,timestamp:"".concat(t.timestamp),updatedBy:t.type}}))})).reduce((function(e,t){return e.concat(t)}),[]).sort((function(e,t){return parseInt(e.timestamp,10)-parseInt(t.timestamp,10)})),controllerId:r.controllerId,language:r.language}))];var r}))}))},e.setCrossDomainCcpaData=function(t){return J(this,void 0,void 0,(function(){return q(this,(function(n){return[2,e.query(z.SET_CROSS_DOMAIN_CCPA_DATA,t)]}))}))},e.getCrossDomainCcpaData=function(){return J(this,void 0,void 0,(function(){return q(this,(function(t){return[2,e.query(z.GET_CROSS_DOMAIN_CCPA_DATA)]}))}))},e.getCrossDomainSessionData=function(){return J(this,void 0,void 0,(function(){return q(this,(function(t){return[2,e.query(z.GET_CROSS_DOMAIN_SESSION_DATA)]}))}))},e.getCrossDomainTcfData=function(){return J(this,void 0,void 0,(function(){return q(this,(function(t){return[2,e.query(z.GET_CROSS_DOMAIN_TCF_DATA)]}))}))},e.setCrossDomainTcfData=function(t){return J(this,void 0,void 0,(function(){return q(this,(function(n){return[2,e.query(z.SET_CROSS_DOMAIN_TCF_DATA,t)]}))}))},e.clearCrossDomainStorage=function(){return J(this,void 0,void 0,(function(){return q(this,(function(t){return[2,e.query(z.CLEAR)]}))}))},e.crossDomainId="",e.cookieBridgeDomain="",e.domainBridgeUri=ge,e.useEuCdn=!1,e.cookieBridgeAvailable=!1,e.crossDomainAvailable=!1,e}(),Ee=function(){function e(){this.length=0,this.data={}}return e.prototype.updateLength=function(){this.length=Object.keys(this.data).length},e.prototype.key=function(e){var t=Object.keys(this.data)[e];return void 0===A(t)?null:t},e.prototype.getItem=function(e){return void 0!==this.data[e]?this.data[e]:null},e.prototype.setItem=function(e,t){this.data[e]=String(t),this.updateLength()},e.prototype.removeItem=function(e){delete this.data[e],this.updateLength()},e.prototype.clear=function(){this.data={},this.updateLength()},e}();!function(e){e.COOKIE_BRIDGE_NOT_AVAILABLE="Usercentrics: Cookie bridge is not available.",e.COOKIE_BRIDGE_OPTIONS_NOT_SET="Usercentrics: Cookie bridge options are not set",e.GET_GLOBAL_TC_STRING_FAILURE="Usercentrics: Unable to get the Global TC string",e.INIT_TCF_ERROR="Usercentrics: Unable to init TCF",e.RESET_GVL_FAILURE="Usercentrics: Unable to reset Global Vendor List",e.SET_GLOBAL_TC_STRING_FAILURE="Usercentrics: Unable to set the Global TC string",e.VENDOR_REMOVED="Usercentrics: The following vendor is not part of the official vendors list anymore: ",e.TC_MODEL_UNDEFINED="Usercentrics: tcModel can not be null.",e.SELECTED_LANGUAGE_UNDEFINED="Usercentrics: Selected language can not be undefined"}(le||(le={})),function(e){e.LEGITIMATE_INTEREST="legIntPurposes",e.PURPOSES="purposes"}(de||(de={})),function(e){e[e.ID=0]="ID",e[e.LEGITIMATE_INTEREST=1]="LEGITIMATE_INTEREST",e[e.PURPOSES=2]="PURPOSES",e[e.SPECIAL_PURPOSES=3]="SPECIAL_PURPOSES"}(pe||(pe={})),function(e){e[e.FIRST_LAYER=1]="FIRST_LAYER",e[e.SECOND_LAYER=2]="SECOND_LAYER"}(fe||(fe={}));var _e=function(e){switch(e){case"onAcceptAllBtnClick":case"onSpecialFunctionAcceptAllConsentTrigger":return"onAcceptAllServices";case"onDenyAllAnchorClick":case"onDenyAllBtnClick":return"onDenyAllServices";case"onNonEURegion":return"onNonEURegion";case"onInitialPageLoad":case"onCountdownFinished":default:return"onInitialPageLoad";case"onToggleCategory":case"onToggleConsent":case"onToggleSelectAll":return"onEssentialChange";case"onWindowFunctionUpdateConsent":case"bySettingsUpdate":case"onSaveBtnClick":return"onUpdateServices"}},me=function(e){switch(e){case"update":case"implicit":default:return"implicit";case"explicit":return"explicit"}},Ie="RAMDOM_KEY_LOCAL_STORAGE",ye=function(){function e(){this.localStorage=null,this.sessionStorage=null,this.storeServiceIdToNameMapping=!1}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){e.instance.localStorage=null,e.instance.sessionStorage=null},e.prototype.setStoreServiceIdToNameMapping=function(e){this.storeServiceIdToNameMapping=e},e.prototype.getStoreServiceIdToNameMapping=function(){return this.storeServiceIdToNameMapping},e.prototype.init=function(){try{localStorage.setItem(Ie,Ie),localStorage.removeItem(Ie),this.localStorage=localStorage}catch(e){this.localStorage=new Ee}try{sessionStorage.setItem(Ie,Ie),sessionStorage.removeItem(Ie),this.sessionStorage=sessionStorage}catch(e){this.sessionStorage=new Ee}},e.appendToConsentsBuffer=function(t){var n,r=e.fetchConsentsBuffer(),i=(new Date).valueOf();r.push({consents:t,timestamp:i}),null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.CONSENTS_BUFFER,JSON.stringify(r))},e.appendToConsentsV2Buffer=function(t){var n,r=e.fetchConsentsV2Buffer(),i=(new Date).valueOf();r.push({consents:t,timestamp:i}),null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.CONSENTS_V2_BUFFER,JSON.stringify(r))},e.clearOnNewSettingsId=function(t){t!==e.fetchSettingsId()&&e.clear()},e.findBufferItem=function(t){return e.fetchConsentsBuffer().slice().find((function(e){return JSON.stringify(e.consents)===JSON.stringify(t)}))},e.findV2BufferItem=function(t){return e.fetchConsentsV2Buffer().slice().find((function(e){return JSON.stringify(e.consents)===JSON.stringify(t)}))},e.removeConsentsBufferItem=function(t){var n,r,i=e.fetchConsentsBuffer(),s=i.length+0;(i=i.filter((function(e){return JSON.stringify(e)!==JSON.stringify(t)}))).length&&i.length!==s?null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.CONSENTS_BUFFER,JSON.stringify(i)):null===(r=e.getInstance().localStorage)||void 0===r||r.removeItem(ee.CONSENTS_BUFFER)},e.removeConsentsV2BufferItem=function(t){var n,r,i=e.fetchConsentsV2Buffer(),s=i.length+0;(i=i.filter((function(e){return JSON.stringify(e)!==JSON.stringify(t)}))).length&&i.length!==s?null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.CONSENTS_V2_BUFFER,JSON.stringify(i)):null===(r=e.getInstance().localStorage)||void 0===r||r.removeItem(ee.CONSENTS_V2_BUFFER)},e.getCcpaString=function(){var t,n=null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.CCPA);return n&&ce.test(n)?n:oe},e.getCcpaData=function(){var t,n=null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.CCPA_DATA);return n?JSON.parse(n):null},e.fetchConsentsBuffer=function(){var t,n=null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.CONSENTS_BUFFER);return n?JSON.parse(n):[]},e.fetchConsentsV2Buffer=function(){var t,n=null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.CONSENTS_V2_BUFFER);return n?JSON.parse(n):[]},e.fetchControllerId=function(){var t=e.fetchSettings();return t?t.controllerId:""},e.fetchLanguage=function(){var t=e.fetchSettings();return t?t.language:""},e.fetchServices=function(){var t=e.fetchSettings();return t?t.services:[]},e.fetchLegacySettings=function(){var t,n=null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.LEGACY_SETTINGS);return n?JSON.parse(n):null},e.fetchSettings=function(){var t,n=null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.SETTINGS);return n?JSON.parse(n):{}},e.fetchSettingsId=function(){var t=e.fetchSettings();return t?t.id:""},e.fetchSettingsVersion=function(){var t=e.fetchSettings();return t?t.version:""},e.fetchTCFData=function(){var t,n=null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.TCF),r=n?JSON.parse(n):{acString:"",tcString:"",timestamp:Date.now(),vendors:[]};return r.vendors||(r.vendors=[]),r},e.fetchTCFVendorsDisclosedObject=function(t){var n,r=e.fetchTCFData(),i=r.acString,s=r.tcString,o=r.vendors,a=r.vendorsDisclosed;if(!o&&!a)return{};if((a||!a&&!o.length&&s)&&t){var c=Object.keys(t).map(Number);n=(a||c).filter((function(e){return t[e]})).map((function(e){var n=t[e];return[n.id,n.legIntPurposes,n.purposes,n.specialPurposes]})),e.saveTCFData({acString:i,tcString:s,timestamp:Date.now(),vendors:n})}else n=o;return n.reduce((function(e,t){var n;return K(K({},e),((n={})[t[pe.ID]]=!0,n))}),{})},e.fetchTCString=function(){return e.fetchTCFData().tcString||""},e.fetchACString=function(){return e.fetchTCFData().acString||""},e.fetchUserActionPerformed=function(){var t;return"true"===(null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.USER_INTERACTION))},e.fetchUserCountryResponse=function(){var t;try{return JSON.parse((null===(t=e.getInstance().sessionStorage)||void 0===t?void 0:t.getItem(te.USER_COUNTRY))||"null")}catch(e){return null}},e.setUserCountryResponse=function(t){var n;null===(n=e.getInstance().sessionStorage)||void 0===n||n.setItem(te.USER_COUNTRY,JSON.stringify(t))},e.mapServices=function(e){return e.map((function(e){return{history:e.consent.history,id:e.id,processorId:e.processorId,status:e.consent.status}}))},e.mapSettings=function(t,n){return{controllerId:t.controllerId,id:t.id,language:t.selectedLanguage,services:e.mapServices(n),version:t.version}},e.migrateLegacySettings=function(t){if(!e.settingsExist()){var n,r,i=e.fetchLegacySettings();if(e.clearLegacySettings(),null==i?void 0:i[t]){var s=(n=i[t])&&Ae(n.ucConsents.consents)?((r={})[ee.SETTINGS]={controllerId:n.ucConsents.consents[0].controllerId,id:n.usercentrics.settings.settingsId,language:n.usercentrics.settings.language,services:n.ucConsents.consents.map((function(e){return{history:e.history.map((function(e){return{action:_e(e.action),language:e.language,status:e.consentStatus,timestamp:e.updatedAt,type:me(e.updatedBy),versions:{application:e.appVersion,service:e.consentTemplateVersion,settings:e.settingsVersion}}})),id:e.templateId,processorId:e.processorId,status:e.consentStatus}})),version:n.usercentrics.settings.version},r[ee.USER_INTERACTION]=n.usercentrics.firstUserInteraction.stateSaved,r):null;s&&(e.saveSettings(s[ee.SETTINGS]),s[ee.USER_INTERACTION]&&e.setUserActionPerformed(!0))}}},e.saveSettings=function(t,n){var r,i;if(null===(r=e.getInstance().localStorage)||void 0===r||r.setItem(ee.SETTINGS,JSON.stringify(t)),e.getInstance().getStoreServiceIdToNameMapping()&&n&&n.length){var s=n.reduce((function(e,t){return e[t.id]=t.name,e}),{});null===(i=e.getInstance().localStorage)||void 0===i||i.setItem(ee.SERVICES,JSON.stringify(s))}Se.isCrossDomainAvailable()&&Se.setCrossDomainData(t).catch((function(){console.warn(W.CROSS_DOMAIN_SET_DATA_FAILURE)}))},e.saveTCFData=function(t){var n;null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.TCF,JSON.stringify(t)),Se.isCrossDomainAvailable()&&Se.setCrossDomainTcfData(t).catch((function(){console.warn(W.CROSS_DOMAIN_SET_TCF_DATA_FAILURE)}))},e.fetchGcmData=function(){var t,n=null===(t=e.getInstance().localStorage)||void 0===t?void 0:t.getItem(ee.GCM_DATA);return n?JSON.parse(n):null},e.saveGcmData=function(t){var n;null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.GCM_DATA,JSON.stringify(t))},e.saveTCString=function(t){var n=e.fetchTCFData();this.saveTCFData(K(K({},n),{tcString:t}))},e.setCcpaTimeStamp=function(t){var n,r=t||{ccpaString:this.getCcpaString()||"",timestamp:(new Date).getTime()};null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.CCPA_DATA,JSON.stringify(r))},e.getCcpaTimeStamp=function(){var e=this.getCcpaData();return e&&e.timestamp?e.timestamp:(new Date).getTime()},e.setCcpaString=function(t){var n;null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.CCPA,t),this.setCcpaTimeStamp()},e.settingsExist=function(){return Oe(e.fetchSettings())},e.setUserActionPerformed=function(t){var n;null===(n=e.getInstance().localStorage)||void 0===n||n.setItem(ee.USER_INTERACTION,JSON.stringify(t))},e.clearCcpa=function(){var t;this.clearCcpaData(),null===(t=e.getInstance().localStorage)||void 0===t||t.removeItem(ee.CCPA)},e.clearCcpaData=function(){var t;null===(t=e.getInstance().localStorage)||void 0===t||t.removeItem(ee.CCPA_DATA)},e.clearTcf=function(){var t;null===(t=e.getInstance().localStorage)||void 0===t||t.removeItem(ee.TCF)},e.clearGcm=function(){var t;null===(t=e.getInstance().localStorage)||void 0===t||t.removeItem(ee.GCM_DATA)},e.clear=function(){var t,n;e.clearCcpa(),e.clearTcf(),e.clearGcm(),null===(t=e.getInstance().localStorage)||void 0===t||t.removeItem(ee.SETTINGS),null===(n=e.getInstance().localStorage)||void 0===n||n.removeItem(ee.USER_INTERACTION)},e.clearAll=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){switch(e.label){case 0:return this.clear(),Se.isCrossDomainAvailable()?[4,Se.clearCrossDomainStorage()]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},e.fetchAbTestVariant=function(t){var n,r,i=(null===(n=e.getInstance().localStorage)||void 0===n?void 0:n.getItem(ee.AB_TEST_VARIANT))||"";if(t.includes(i))return i;var s=t[Math.floor(Math.random()*t.length)];return null===(r=e.getInstance().localStorage)||void 0===r||r.setItem(ee.AB_TEST_VARIANT,s),s},e.clearLegacySettings=function(){var t;null===(t=e.getInstance().localStorage)||void 0===t||t.removeItem(ee.LEGACY_SETTINGS)},e}(),Ce=function(e,t){return-1!==e.indexOf(t)},Te=function(){return"SDK-".concat(ie)},be=function(){return parseInt("3",10)},Ae=function(e){return Array.isArray(e)&&e.length>0},Oe=function(e){return"object"===A(e)&&null!==e&&Object.keys(e).length>0},Ne=function(e,t){if(void 0===t)throw new Error("altElement of nullishOperation can not be undefined");return null!=e?e:t},we=function(e,t,n,r,i,s){return{applicationVersion:Te(),consent:{action:n,status:t.consent.status,type:r},service:{categorySlug:i||"",id:t.id,name:t.name,processorId:t.processorId,version:t.version},settings:{controllerId:e.controllerId,id:e.id,language:e.selectedLanguage,referrerControllerId:Ne(null==s?void 0:s.referrerControllerId,""),version:e.version},timestamp:Ne(null==s?void 0:s.timestamp,(new Date).valueOf())}},Re=function(e){var t=e.dataTransferSettings,n=e.services,r=void 0===n?[]:n,i=e.consentAction,s=e.consentString,o=e.isCcpa,a=e.isTcf,c=e.isAnalyticsEnabled,u=e.isConsentXDeviceEnabled,l=r.map((function(e){return{consentStatus:e.consent.status,consentTemplateId:e.id,consentTemplateVersion:e.version}})),d="",p="",f="";if(a){var h=ye.fetchTCFData(),g=h.acString,v=h.tcString,S=h.timestamp,E=h.vendors,_=h.vendorsDisclosed;p=Ne(p?null==s?void 0:s.TCF2:v,""),f=JSON.stringify({timestamp:S,vendors:E,vendorsDisclosed:_}),g&&(d=g)}else o&&(p=Ne(null==s?void 0:s.CCPA,""));return K(K(K(K(K({action:i,analytics:Ne(c,!1),appVersion:Te().replace("SDK-","")},f&&{consentMeta:f}),{consents:l}),p&&{consentString:p}),d&&{acString:d}),{controllerId:t.controllerId,language:t.selectedLanguage,settingsId:t.id,settingsVersion:t.version,xdevice:Ne(u,!1)})},Le=function(e,t){return e.reduce((function(e,n){var r=t[n];if(!(null==r?void 0:r.name))return e;var i=r.name;return Q(Q([],X(e),!1),[{id:n,name:i}],!1)}),[])};function De(e){return Q([],X(new Set(e)),!1)}var Pe,Ve,Ue,ke,Me,Fe,xe,Ge,Be,He,je=function(e){return e.filter((function(e){return["onAcceptAllServices","onDenyAllServices","onUpdateServices"].indexOf(e.action)>-1})).sort((function(e,t){return t.timestamp-e.timestamp}))},$e=function(e){return e&&"object"===A(e)},Ye=function e(t,n,r){void 0===r&&(r=!1);var i=K({},n);if(!$e(i))throw new Error("Source param should be an object");return $e(t)?(Object.keys(t).forEach((function(n){var s,o,a,c,u,l=t[n],d=i[n];void 0!==d&&(i=Array.isArray(l)&&Array.isArray(d)?K(K({},i),r?((s={})[n]=(u=l.concat(d)).filter((function(e,t){return u.indexOf(e)===t})),s):((o={})[n]=l,o)):$e(l)&&$e(d)?K(K({},i),((a={})[n]=e(K({},l),d),a)):K(K({},i),((c={})[n]=l,c)))})),i):i},We=function(e,t,n,r){return void 0===n&&(n=2e4),void 0===r&&(r=5),new Promise((function(i,s){var o=setInterval((function(){e()&&(clearTimeout(a),clearInterval(o),i())}),r),a=setTimeout((function(){clearTimeout(a),clearInterval(o),s(new Error(t))}),n)}))},ze=function(e,t){for(var n=e.split("."),r=t.split("."),i=Math.min(n.length,r.length),s=0;s<i;s+=1){var o=Number(n[s])||0,a=Number(r[s])||0;if(o!==a)return o>a?1:-1}return r.length-n.length};!function(e){e[e.COOKIE=0]="COOKIE",e[e.WEB=1]="WEB",e[e.APP=2]="APP"}(Pe||(Pe={})),function(e){e.LEFT="LEFT",e.CENTER="CENTER",e.RIGHT="RIGHT"}(Ve||(Ve={})),function(e){e.CAT="CAT",e.SRV="SRV"}(Ue||(Ue={})),function(e){e.AVAILABLE_LANGUAGES_NOT_FOUND="Unable to find available languages using given settingsId and version.",e.FETCH_ACM_VENDORS="Something went wrong while fetching the atp vendors.",e.FETCH_AVAILABLE_LANGUAGES="Something went wrong while fetching the available languages.",e.FETCH_DATA_PROCESSING_SERVICES="Something went wrong while fetching the data processing services.",e.FETCH_LEGAL_BASIS="Something went wrong while fetching the legal data translations.",e.FETCH_SETTINGS="Something went wrong while fetching the settings.",e.FETCH_USER_CONSENTS="Something went wrong while fetching the user's consents.",e.FETCH_USER_COUNTRY="Something went wrong while fetching the user's country.",e.FETCH_USER_TCF_DATA="Something went wrong while fetching the user's tcf data.",e.GENERATE_DATA_PROCESSING_SERVICES="Something went wrong while generating the data processing services.",e.RULESET_NOT_FOUND="Config Map not found!",e.TAGLOGGER="Tag logger API is being called just before browser unload, some browsers like firefox cancel the api call and throw the error.",e.SAVE_CONSENTS="Something went wrong while saving user consents.",e.SAVE_CONSENTS_RETRY="Number of retries exceeded for saving user consents.",e.SETTINGS_NOT_FOUND="Unable to find settings using given settingsId and version.",e.CUSTOM_REFERRER_NOT_VALID="A custom referrer was passed to setTrackingPixel but it is not a valid URL. Origin and pathname will be passed instead."}(ke||(ke={})),function(e){e.US_CA_ONLY="US_CA_ONLY",e.US="US"}(Me||(Me={})),function(e){e[e.FIRST_LAYER=1]="FIRST_LAYER",e[e.SECOND_LAYER=3]="SECOND_LAYER"}(Fe||(Fe={})),function(e){e[e.DATA_LAYER=1]="DATA_LAYER",e[e.WINDOW_EVENT=4]="WINDOW_EVENT"}(xe||(xe={})),function(e){e.DATA_COLLECTED_LIST="dataCollectedList",e.DATA_PURPOSES_LIST="dataPurposesList",e.DATA_RECIPIENTS_LIST="dataRecipientsList",e.TECHNOLOGY_USED="technologyUsed"}(Ge||(Ge={})),function(e){e.MAJOR="major",e.MINOR="minor",e.PATCH="patch"}(Be||(Be={})),function(e){e.ICON="ICON",e.LINK="LINK"}(He||(He={}));var Ke,Je={acceptAllButton:"Accept All",ccpaButton:"Agree to CCPA",ccpaMoreInformation:"More Information",closeButton:"Close",collapse:"Collapse",cookiePolicyButton:"Open Cookie Policy",copyControllerId:"Copy Controller ID",denyAllButton:"Deny all",expand:"Expand",fullscreenButton:"Enter full screen",imprintButton:"Open Imprint",languageSelector:"Select language",privacyButton:"Open",privacyPolicyButton:"Open Privacy Policy",saveButton:"Save",serviceInCategoryDetails:"View Service details",servicesInCategory:"List of Services in this category",tabButton:"Tab",usercentricsCard:"Card",usercentricsCMPButtons:"Footer including buttons",usercentricsCMPContent:"Content",usercentricsCMPHeader:"Header including language selection and external links",usercentricsCMPUI:"Consent Management Platform Interface",usercentricsList:"List",vendorConsentToggle:"Consent",vendorDetailedStorageInformation:"Detailed Storage Information",vendorLegIntToggle:"Legitimate Interest"},qe=function(e,t){return"boolean"==typeof e?e:t},Xe=function(e){if(!e)return null;var t=e.startsWith("#")?e:"#".concat(e);return Qe(t)?t:"#0045A5"},Qe=function(e){return!(""===e||!e)&&/^#(?:[0-9a-fA-F]{3}){1,2}$/.test(e)},Ze=function(e){if(null!=e.firstLayer.isOverlayEnabled)return e.firstLayer.isOverlayEnabled;var t=e.backgroundOverlay.find((function(e){var t;return(null===(t=e.target)||void 0===t?void 0:t[0])===Fe.FIRST_LAYER}));return!!t&&(null==t?void 0:t.darken)>0},et=function(e){if(null!=e.secondLayer.isOverlayEnabled)return e.secondLayer.isOverlayEnabled;var t=e.backgroundOverlay.find((function(e){var t;return(null===(t=e.target)||void 0===t?void 0:t[0])===Fe.SECOND_LAYER}));return!!t&&(null==t?void 0:t.darken)>0},tt=function(e){return"number"==typeof e||"string"==typeof e&&!e.includes("px")?"".concat(e,"px"):e},nt=function(e){switch(e){case Ve.CENTER:return"center";case Ve.RIGHT:return"right";case Ve.LEFT:default:return"left"}},rt={description:"",id:"",legalBasis:[],name:""},it=function(e,t){return e.reduce((function(e,n){var r=t.find((function(e){return e.id===n.id}));return r?Q(Q([],X(e),!1),[{categorySlug:n.categorySlug,consent:n.consent,id:n.id,language:n.language,name:r.name,processorId:n.processorId,version:n.version}],!1):Q([],X(e),!1)}),[])},st=function e(t,n){return t.map((function(t){var r=n.find((function(e){return e.id===t.id}));return K(K(K({},t),r||rt),{description:t.description||(null==r?void 0:r.description)||rt.description,id:t.id,subServices:e(t.subServices,n)})}))},ot=function(){return"https://www.usercentrics.com/consent-management-platform-powered-by-usercentrics/"},at=function(e){return null!=e&&null!=e.region},ct=function(e){return null!=e&&null!=e.changedPurposes},ut=function(e){var t;return null!=e&&null!=(null===(t=e.buttons)||void 0===t?void 0:t.optOutNotice)},lt=function(e){return null!=e&&null!=e.vendor},dt=function(e){var t;return null!=e&&null!=(null===(t=e.buttons)||void 0===t?void 0:t.showSecondLayer)},pt=function(e){return null!=e&&null!=e.privacyButton},ft=function(e){return null!=e&&(ut(e)||dt(e)||lt(e))},ht=function(e){var t,n;return null!=e&&null!=(null===(t=e.firstLayer)||void 0===t?void 0:t.showShortDescriptionOnMobile)&&null==(null===(n=e.firstLayer)||void 0===n?void 0:n.isCategoryTogglesEnabled)},gt=function(e){var t;return null!=e&&null!=(null===(t=e.firstLayer)||void 0===t?void 0:t.hideNonIabPurposes)},vt=function(e){var t;return null!=e&&null!=(null===(t=e.firstLayer)||void 0===t?void 0:t.isCategoryTogglesEnabled)},St=function(e){return null!=e&&!ht(e)&&!vt(e)&&!gt(e)},Et=function(){function e(){this.ampEnabled=!1}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){e.instance.ampEnabled=!1},e.prototype.isAmpEnabled=function(){return this.ampEnabled},e.prototype.setIsAmpEnabled=function(e){this.ampEnabled=e},e}(),_t="latest",mt={EU_URI:{AGGREGATOR:"https://aggregator.eu.usercentrics.eu/aggregate/",CDN:"https://config.eu.usercentrics.eu",FETCH_CONSENTS:"https://consents.eu.usercentrics.eu/consentsHistory",FETCH_CONSENTS_V2:"https://consent-rt-ret.service.consent.eu1.usercentrics.eu",FETCH_TCF_DATA:"https://consents.eu.usercentrics.eu/consentsHistoryTCF",FETCH_TCF_DATA_V2:"https://consents.eu.usercentrics.eu/consentState",GRAPHQL:"https://api.eu.usercentrics.eu/graphql",SAVE_CONSENTS_V2:"https://consent-api.service.consent.eu1.usercentrics.eu/consent",TRACK_EVENT:"https://uct.eu.usercentrics.eu/uct",TRACK_SESSION:"https://app.eu.usercentrics.eu/session/1px.png"},FOLDER:{RULESET:"ruleSet",SETTINGS:"settings",TEMPLATES:"consent-templates",TRANSLATIONS:"translations"},URI:{AGGREGATOR:"https://aggregator.service.usercentrics.eu/aggregate/",CDN:"https://api.usercentrics.eu",FETCH_CONSENTS:"https://consents.usercentrics.eu/consentsHistory",FETCH_CONSENTS_V2:"https://consent-rt-ret.service.consent.usercentrics.eu",FETCH_TCF_DATA:"https://consents.usercentrics.eu/consentsHistoryTCF",FETCH_TCF_DATA_V2:"https://consents.usercentrics.eu/consentState",GRAPHQL:"https://graphql.usercentrics.eu/graphql",RULESET:"https://api.usercentrics.eu",SAVE_CONSENTS_V2:"https://consent-api.service.consent.usercentrics.eu/consent",TRACK_EVENT:"https://uct.service.usercentrics.eu/uct",TRACK_SESSION:"https://app.usercentrics.eu/session/1px.png"}},It={EU_URI:{AGGREGATOR:"https://aggregator.service.usercentrics-sandbox.eu/aggregate/",CDN:"https://api.usercentrics-sandbox.eu",FETCH_CONSENTS:"https://api-consent-sandbox-dot-usercentrics-playground.nw.r.appspot.com/consentsHistory",FETCH_CONSENTS_V2:"https://consent-rt-ret.service.consent.eu1.usercentrics-staging.eu",FETCH_TCF_DATA:"https://api-consent-sandbox-dot-usercentrics-playground.nw.r.appspot.com/consentsHistoryTCF",FETCH_TCF_DATA_V2:"https://api-consent-sandbox-dot-usercentrics-playground.nw.r.appspot.com/consentState",GRAPHQL:"https://api-v2-sandbox-consent-dot-usercentrics-playground.nw.r.appspot.com/",SAVE_CONSENTS_V2:"https://consent-api.service.consent.eu1.usercentrics-staging.eu/consent",TRACK_EVENT:"https://uct.eu.usercentrics.eu/uct",TRACK_SESSION:"https://app.usercentrics-sandbox.eu/session/1px.png"},FOLDER:{RULESET:"ruleSet",SETTINGS:"settings",TEMPLATES:"consent-templates",TRANSLATIONS:"translations"},URI:{AGGREGATOR:"https://aggregator.service.usercentrics-sandbox.eu/aggregate/",CDN:"https://api.usercentrics-sandbox.eu",FETCH_CONSENTS:"https://api-consent-sandbox-dot-usercentrics-playground.nw.r.appspot.com/consentsHistory",FETCH_CONSENTS_V2:"https://consent-rt-ret.service.consent.usercentrics-staging.eu",FETCH_TCF_DATA:"https://api-consent-sandbox-dot-usercentrics-playground.nw.r.appspot.com/consentsHistoryTCF",FETCH_TCF_DATA_V2:"https://api-consent-sandbox-dot-usercentrics-playground.nw.r.appspot.com/consentState",GRAPHQL:"https://api-v2-sandbox-consent-dot-usercentrics-playground.nw.r.appspot.com/",RULESET:"https://api.usercentrics-sandbox.eu",SAVE_CONSENTS_V2:"https://consent-api.service.consent.usercentrics-staging.eu/consent",TRACK_EVENT:"https://uct.service.usercentrics.eu/uct",TRACK_SESSION:"https://app.usercentrics-sandbox.eu/session/1px.png"}},yt=["onEssentialChange","onInitialPageLoad","onNonEURegion"],Ct=["onSessionRestored","onMobileSessionRestore"],Tt=function(e,t,n){return J(void 0,void 0,void 0,(function(){return q(this,(function(r){return[2,At(e,t,null,n)]}))}))},bt=function(e,t,n,r){return J(void 0,void 0,void 0,(function(){return q(this,(function(i){return[2,At(e,n,t,r)]}))}))},At=function(e,t,n,r){return J(void 0,void 0,void 0,(function(){var i,s;return q(this,(function(o){return i={"Content-Type":"application/json"},s=K(K({},r),{headers:i,method:n?"POST":"GET"}),n&&(s.body=JSON.stringify(n)),(null==r?void 0:r.headers)&&(s.headers=K(K({},i),r.headers)),[2,fetch(e,s).then((function(e){return J(void 0,void 0,void 0,(function(){return q(this,(function(n){if(e.ok)return[2,Nt(e)];throw Ot(t,e.status)}))}))}))]}))}))},Ot=function(e,t){return{errorMessage:e,statusCode:t}},Nt=function(e){return J(void 0,void 0,void 0,(function(){return q(this,(function(t){switch(t.label){case 0:return[4,wt(e)];case 1:return[2,{data:t.sent(),location:e.headers.get("x-client-geo-location"),statusCode:e.status}]}}))}))},wt=function(e){return J(void 0,void 0,void 0,(function(){var t,n;return q(this,(function(r){switch(r.label){case 0:return[4,e.text()];case 1:return t=r.sent(),[2,(n=""===t?{}:JSON.parse(t)).data||n]}}))}))};!function(e){e[e.RESOURCE_NOT_FOUND=403]="RESOURCE_NOT_FOUND"}(Ke||(Ke={}));var Rt,Lt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},Dt={exports:{}},Pt={exports:{}};Pt.exports=(Rt=Rt||function(e){var t;if("undefined"!=typeof window&&window.crypto&&(t=window.crypto),"undefined"!=typeof self&&self.crypto&&(t=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(t=globalThis.crypto),!t&&"undefined"!=typeof window&&window.msCrypto&&(t=window.msCrypto),!t&&void 0!==Lt&&Lt.crypto&&(t=Lt.crypto),!t)try{t=require("crypto")}catch(e){}var n=function(){if(t){if("function"==typeof t.getRandomValues)try{return t.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof t.randomBytes)try{return t.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},r=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),i={},s=i.lib={},o=s.Base={extend:function(e){var t=r(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=s.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,i=e.sigBytes;if(this.clamp(),r%4)for(var s=0;s<i;s++){var o=n[s>>>2]>>>24-s%4*8&255;t[r+s>>>2]|=o<<24-(r+s)%4*8}else for(var a=0;a<i;a+=4)t[r+a>>>2]=n[a>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=o.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(n());return new a.init(t,e)}}),c=i.enc={},u=c.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var s=t[i>>>2]>>>24-i%4*8&255;r.push((s>>>4).toString(16)),r.push((15&s).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new a.init(n,t/2)}},l=c.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],i=0;i<n;i++){var s=t[i>>>2]>>>24-i%4*8&255;r.push(String.fromCharCode(s))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new a.init(n,t)}},d=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(l.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return l.parse(unescape(encodeURIComponent(e)))}},p=s.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,r=this._data,i=r.words,s=r.sigBytes,o=this.blockSize,c=s/(4*o),u=(c=t?e.ceil(c):e.max((0|c)-this._minBufferSize,0))*o,l=e.min(4*u,s);if(u){for(var d=0;d<u;d+=o)this._doProcessBlock(i,d);n=i.splice(0,u),r.sigBytes-=l}return new a.init(n,l)},clone:function(){var e=o.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});s.Hasher=p.extend({cfg:o.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new f.HMAC.init(e,n).finalize(t)}}});var f=i.algo={};return i}(Math),Rt),Dt.exports=function(e){return function(t){var n=e,r=n.lib,i=r.WordArray,s=r.Hasher,o=n.algo,a=[],c=[];!function(){function e(e){for(var n=t.sqrt(e),r=2;r<=n;r++)if(!(e%r))return!1;return!0}function n(e){return 4294967296*(e-(0|e))|0}for(var r=2,i=0;i<64;)e(r)&&(i<8&&(a[i]=n(t.pow(r,.5))),c[i]=n(t.pow(r,1/3)),i++),r++}();var u=[],l=o.SHA256=s.extend({_doReset:function(){this._hash=new i.init(a.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],s=n[2],o=n[3],a=n[4],l=n[5],d=n[6],p=n[7],f=0;f<64;f++){if(f<16)u[f]=0|e[t+f];else{var h=u[f-15],g=(h<<25|h>>>7)^(h<<14|h>>>18)^h>>>3,v=u[f-2],S=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;u[f]=g+u[f-7]+S+u[f-16]}var E=r&i^r&s^i&s,_=(r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22),m=p+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&l^~a&d)+c[f]+u[f];p=d,d=l,l=a,a=o+m|0,o=s,s=i,i=r,r=m+(_+E)|0}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+s|0,n[3]=n[3]+o|0,n[4]=n[4]+a|0,n[5]=n[5]+l|0,n[6]=n[6]+d|0,n[7]=n[7]+p|0},_doFinalize:function(){var e=this._data,n=e.words,r=8*this._nDataBytes,i=8*e.sigBytes;return n[i>>>5]|=128<<24-i%32,n[14+(i+64>>>9<<4)]=t.floor(r/4294967296),n[15+(i+64>>>9<<4)]=r,e.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=s.clone.call(this);return e._hash=this._hash.clone(),e}});n.SHA256=s._createHelper(l),n.HmacSHA256=s._createHmacHelper(l)}(Math),e.SHA256}(Pt.exports);var Vt,Ut=Dt.exports,kt=new Uint8Array(16);function Mt(){if(!Vt&&!(Vt="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Vt(kt)}for(var Ft=[],xt=0;xt<256;++xt)Ft.push((xt+256).toString(16).slice(1));var Gt={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function Bt(e,t,n){if(Gt.randomUUID&&!t&&!e)return Gt.randomUUID();var r=(e=e||{}).random||(e.rng||Mt)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var i=0;i<16;++i)t[n+i]=r[i];return t}return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Ft[e[t+0]]+Ft[e[t+1]]+Ft[e[t+2]]+Ft[e[t+3]]+"-"+Ft[e[t+4]]+Ft[e[t+5]]+"-"+Ft[e[t+6]]+Ft[e[t+7]]+"-"+Ft[e[t+8]]+Ft[e[t+9]]+"-"+Ft[e[t+10]]+Ft[e[t+11]]+Ft[e[t+12]]+Ft[e[t+13]]+Ft[e[t+14]]+Ft[e[t+15]]}(r)}var Ht=function(){function e(){this.needsSessionRestore=!1,this.controllerId=""}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){e.instance.controllerId=""},Object.defineProperty(e.prototype,"value",{get:function(){return this.controllerId},set:function(e){this.controllerId=e},enumerable:!1,configurable:!0}),e.prototype.setControllerIdByResolvedSettingsId=function(e,t){var n=t.find((function(t){var n=t.settingsId;return e===n}));n&&(this.value=n.controllerId)},e.prototype.setNeedSessionRestore=function(){this.needsSessionRestore=""!==this.controllerId},e.prototype.init=function(){if(""===this.controllerId){var e=this.getStorageControllerId();this.controllerId=e||this.generateControllerId()}},e.prototype.generateControllerId=function(){return"".concat(Ut(Bt()))},e.prototype.getStorageControllerId=function(){return ye.fetchControllerId()},e}(),jt=["AT","BE","BG","CY","CZ","DE","DK","EE","ES","FI","FR","GR","HR","HU","IE","IT","IS","LI","LT","LU","LV","MT","NL","NO","PL","PT","RO","SE","SI","SK"],$t=function(){function e(){this.userCountryData={},this.convertUserCountryString=function(e){var t=null==e?void 0:e.split(","),n=t&&t[0]?t[0]:"DE";return{countryCode:n,countryName:n||"Germany (default)",regionCode:t&&t[1]&&t[1].length>=3?t[1].substring(2):""}}}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){e.instance.userCountryData={}},e.prototype.setUserCountryData=function(e){this.userCountryData=e},e.prototype.getUserCountryData=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){return[2,this.userCountryData]}))}))},e.prototype.getIsUserInCalifornia=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){return[2,this.getIsUserInUS("CA")]}))}))},e.prototype.getIsUserInEU=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return[4,this.getUserCountryData()];case 1:return e=t.sent(),[2,Ce(jt,e.code.toUpperCase())]}}))}))},e.prototype.getIsUserInUS=function(e){return J(this,void 0,void 0,(function(){var t,n;return q(this,(function(r){switch(r.label){case 0:return[4,this.getUserCountryData()];case 1:return t=r.sent(),(n=t.regionCode).length>2&&(n=n.slice(2)),[2,"US"===t.code&&(!e||n===e)]}}))}))},e.prototype.resolveLocation=function(t){return J(this,void 0,void 0,(function(){var n,r;return q(this,(function(i){switch(i.label){case 0:return n=this.convertUserCountryString(t),[4,e.getInstance().getUserCountryData()];case 1:return r=i.sent(),Oe(r)||(ye.setUserCountryResponse(n),this.setUserCountryData({code:n.countryCode,name:n.countryName,regionCode:n.regionCode})),[2]}}))}))},e.mapUserCountryData=function(e){return{countryCode:e.code,countryName:e.name,regionCode:e.regionCode}},e}(),Yt={numOfAttempts:7,onFirstFail:function(){},onFirstRetriedSuccess:function(){},startingDelay:100,timeMultiple:2};function Wt(e,t){return void 0===t&&(t={}),J(this,void 0,void 0,(function(){var n;return q(this,(function(r){switch(r.label){case 0:return n=function(e){var t=K(K({},Yt),e);return t.numOfAttempts<1&&(t.numOfAttempts=1),t}(t),[4,new Kt(e,n).execute()];case 1:return[2,r.sent()]}}))}))}var zt,Kt=function(){function e(e,t){this.request=e,this.options=t,this.attemptNumber=0}return e.prototype.execute=function(){return J(this,void 0,void 0,(function(){var e,t;return q(this,(function(n){switch(n.label){case 0:if(this.attemptLimitReached)return[3,7];n.label=1;case 1:return n.trys.push([1,5,,6]),this.attemptNumber?[4,this.wait(this.delay)]:[3,3];case 2:n.sent(),n.label=3;case 3:return[4,this.request()];case 4:return e=n.sent(),this.attemptNumber&&this.options.onFirstRetriedSuccess(),[2,e];case 5:if(t=n.sent(),this.attemptNumber||this.options.onFirstFail(),this.attemptNumber+=1,this.attemptLimitReached)throw t;return[3,6];case 6:return[3,0];case 7:throw new Error("Something went wrong.")}}))}))},Object.defineProperty(e.prototype,"attemptLimitReached",{get:function(){return this.attemptNumber>=this.options.numOfAttempts},enumerable:!1,configurable:!0}),e.prototype.wait=function(e){return new Promise((function(t){return setTimeout(t,e)}))},Object.defineProperty(e.prototype,"delay",{get:function(){var e=this.options.startingDelay,t=this.options.timeMultiple,n=this.attemptNumber;return e*Math.pow(t,n)},enumerable:!1,configurable:!0}),e}(),Jt=function(e,t){return{id:e,version:t}},qt=function(e){if(!e)return[];var t,n,r=e.reduce((function(e,t){return Q(Q([],X(e),!1),[Jt(t.templateId,t.version)],!1)}),[]);return function(e){return e.sort((function(e,t){return e.id>t.id?1:-1}))}((t=function(e,t){return e.id===t.id&&e.version===t.version},n=[],r.forEach((function(e){-1===n.findIndex((function(n){return t(e,n)}))&&n.push(e)})),n))},Xt=function(){function e(){this.API=mt,this.abTestVariant="",this.controllerIdInstance=Ht.getInstance(),this.jsonCacheBustingString="",this.jsonFileLanguage="",this.jsonFileVersion=_t,this.settingsId="",this.rulesetId="",this.useEuCdn=!1,this.disableServerConsents=!1,this.aggregatedServicesCache=null,this.translationsCache=null}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){e.instance.jsonCacheBustingString="",e.instance.jsonFileLanguage="",e.instance.jsonFileVersion=_t,e.instance.settingsId="",e.instance.disableServerConsents=!1},e.prototype.resetAggregatedServicesCache=function(){this.aggregatedServicesCache=null},e.prototype.resetTranslationsCache=function(){this.translationsCache=null},e.prototype.getAbTestVariant=function(){return this.abTestVariant},e.prototype.getJsonFileLanguage=function(){return this.jsonFileLanguage},e.prototype.getJsonFileVersion=function(){return this.jsonFileVersion},e.prototype.getSettingsId=function(){return this.settingsId},e.prototype.getRulesetId=function(){return this.rulesetId},e.prototype.getDisableServerConsents=function(){return this.disableServerConsents},e.prototype.setJsonCacheBustingString=function(e){this.jsonCacheBustingString=e},e.prototype.setJsonFileLanguage=function(e){this.jsonFileLanguage=e},e.prototype.setJsonFileVersion=function(e){this.jsonFileVersion=e},e.prototype.setDisableServerConsents=function(e){this.disableServerConsents=e},e.prototype.setDomains=function(e,t){this.API=e?It:t?{EU_URI:{AGGREGATOR:mt.EU_URI.AGGREGATOR,CDN:mt.EU_URI.CDN,FETCH_CONSENTS:mt.EU_URI.FETCH_CONSENTS,FETCH_CONSENTS_V2:mt.EU_URI.FETCH_CONSENTS_V2,FETCH_TCF_DATA:mt.EU_URI.FETCH_TCF_DATA,FETCH_TCF_DATA_V2:mt.EU_URI.FETCH_TCF_DATA_V2,GRAPHQL:mt.EU_URI.GRAPHQL,SAVE_CONSENTS_V2:mt.EU_URI.SAVE_CONSENTS_V2,TRACK_EVENT:mt.EU_URI.TRACK_EVENT,TRACK_SESSION:mt.EU_URI.TRACK_SESSION},FOLDER:{RULESET:"ruleSet",SETTINGS:"settings",TEMPLATES:"consent-templates",TRANSLATIONS:"translations"},URI:K(K({AGGREGATOR:""!==t.aggregator?"".concat(t.aggregator,"/aggregate/"):mt.URI.AGGREGATOR,CDN:""!==t.cdn?t.cdn:mt.URI.CDN,RULESET:mt.URI.RULESET},""!==t.consents?{FETCH_CONSENTS:"".concat(t.consents,"/consentsHistory"),FETCH_CONSENTS_V2:"".concat(t.consents),FETCH_TCF_DATA:"".concat(t.consents,"/consentsHistoryTCF"),FETCH_TCF_DATA_V2:"".concat(t.consents,"/consentState")}:{FETCH_CONSENTS:mt.URI.FETCH_CONSENTS,FETCH_CONSENTS_V2:mt.URI.FETCH_CONSENTS_V2,FETCH_TCF_DATA:mt.URI.FETCH_TCF_DATA,FETCH_TCF_DATA_V2:mt.URI.FETCH_TCF_DATA_V2}),{GRAPHQL:""!==t.graphql?"".concat(t.graphql,"/graphql"):mt.URI.GRAPHQL,SAVE_CONSENTS_V2:""!==t.consentsV2?"".concat(t.consentsV2,"/consent"):mt.URI.SAVE_CONSENTS_V2,TRACK_EVENT:""!==t.trackingEvent?"".concat(t.trackingEvent,"/uct"):mt.URI.TRACK_EVENT,TRACK_SESSION:""!==t.app?"".concat(t.app,"/session/1px.png"):mt.URI.TRACK_SESSION})}:mt},e.prototype.setSettingsId=function(e){this.settingsId=e},e.prototype.setRulesetId=function(e){this.rulesetId=e},e.prototype.setEuMode=function(e){this.useEuCdn=e},e.prototype.isEuMode=function(){return this.useEuCdn},e.prototype.getAggregatorUri=function(){return this.isEuMode()?this.API.EU_URI.AGGREGATOR:this.API.URI.AGGREGATOR},e.prototype.getCdnUri=function(){return this.isEuMode()?this.API.EU_URI.CDN:this.API.URI.CDN},e.prototype.getGraphQLUri=function(){return this.isEuMode()?this.API.EU_URI.GRAPHQL:this.API.URI.GRAPHQL},e.prototype.getTcfDataV2Uri=function(){return this.isEuMode()?this.API.EU_URI.FETCH_TCF_DATA_V2:this.API.URI.FETCH_TCF_DATA_V2},e.prototype.fetchAggregatedServices=function(e,t){return void 0===t&&(t=!0),J(this,void 0,void 0,(function(){var n,r;return q(this,(function(i){switch(i.label){case 0:return this.aggregatedServicesCache&&t?[2,this.aggregatedServicesCache]:(n="".concat(this.getAggregatorUri()).concat(this.jsonFileLanguage,"?templates=").concat(e.map((function(e){return"".concat(e.id,"@").concat(e.version)})).join(",")),[4,Tt(n,ke.GENERATE_DATA_PROCESSING_SERVICES)]);case 1:return r=i.sent(),t&&(this.aggregatedServicesCache=r.data.templates),[2,r.data.templates]}}))}))},e.prototype.fetchRuleset=function(){var e;return J(this,void 0,void 0,(function(){var t,n,r,i;return q(this,(function(s){switch(s.label){case 0:return s.trys.push([0,2,,3]),t=this.createRulesetUrl(),[4,Tt(t,ke.RULESET_NOT_FOUND)];case 1:return n=s.sent(),r=null===(e=n.location)||void 0===e?void 0:e.split(","),[2,{defaultRule:n.data.defaultRule,description:n.data.description,location:{code:r?r[0]:"",name:n.location||"",regionCode:r&&r[1]?r[1]:""},rules:n.data.rules}];case 2:throw(i=s.sent()).statusCode&&i.statusCode===Ke.RESOURCE_NOT_FOUND&&(i.errorMessage=ke.RULESET_NOT_FOUND),i;case 3:return[2]}}))}))},e.prototype.fetchAvailableLanguages=function(){return J(this,void 0,void 0,(function(){var e,t,n;return q(this,(function(r){switch(r.label){case 0:return r.trys.push([0,3,,4]),e=this.createAvailableLanguagesUrl(),[4,Tt(e,ke.FETCH_AVAILABLE_LANGUAGES)];case 1:return t=r.sent(),[4,$t.getInstance().resolveLocation(t.location)];case 2:return r.sent(),[2,t.data.languagesAvailable];case 3:throw(n=r.sent()).statusCode&&n.statusCode===Ke.RESOURCE_NOT_FOUND&&(n.errorMessage=ke.AVAILABLE_LANGUAGES_NOT_FOUND),n;case 4:return[2]}}))}))},e.prototype.fetchTranslations=function(){return J(this,void 0,void 0,(function(){var e,t,n;return q(this,(function(r){switch(r.label){case 0:if(this.translationsCache)return[2,this.translationsCache];r.label=1;case 1:return r.trys.push([1,3,,4]),e=this.createLanguagesUrl(),[4,Tt(e,ke.FETCH_LEGAL_BASIS)];case 2:return t=r.sent(),this.translationsCache=t.data,[2,t.data];case 3:return(n=r.sent()).statusCode&&n.statusCode===Ke.RESOURCE_NOT_FOUND&&(n.errorMessage=ke.FETCH_LEGAL_BASIS),[2,null];case 4:return[2]}}))}))},e.prototype.mergeAbVariant=function(e){return J(this,void 0,void 0,(function(){var t,n,r;return q(this,(function(i){switch(i.label){case 0:if(t=JSON.parse(e.data.variants.experiments),"UC"===e.data.variants.activateWith)return n=Object.keys(t),this.abTestVariant=ye.fetchAbTestVariant(n),[2,Ye(t[this.abTestVariant],e.data)];i.label=1;case 1:return i.trys.push([1,3,,4]),[4,We((function(){return!!window.UC_AB_VARIANT}),"window.UC_AB_VARIANT is not defined",2e3)];case 2:return i.sent(),[3,4];case 3:return r=i.sent(),console.warn(r),[3,4];case 4:return window.UC_AB_VARIANT&&t&&t[window.UC_AB_VARIANT]?(this.abTestVariant=window.UC_AB_VARIANT,[2,Ye(t[window.UC_AB_VARIANT],e.data)]):[2,e.data]}}))}))},e.prototype.fetchAcmVendors=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return e=this.createAcmVendorsJsonUrl(),[4,Tt(e,ke.FETCH_ACM_VENDORS)];case 1:return[2,t.sent().data]}}))}))},e.prototype.fetchSettingsJson=function(){return J(this,void 0,void 0,(function(){var e,t,n;return q(this,(function(r){switch(r.label){case 0:return r.trys.push([0,4,,5]),e=this.createSettingsJsonUrl(),[4,Tt(e,ke.FETCH_SETTINGS)];case 1:return t=r.sent(),["ccpa","firstLayer","secondLayer"].forEach((function(e){t.data[e]||(t.data[e]={})})),t.data.tcf2&&(t.data.tcf2.selectedVendorIds=(t.data.tcf2.selectedVendorIds||[]).sort((function(e,t){return e-t}))),t.data.consentTemplates&&(t.data.consentTemplates=t.data.consentTemplates.reduce((function(e,t){return t.isDeactivated||e.push(t),e}),[])),t.data.variants&&t.data.variants.enabled&&function(e){try{JSON.parse(e)}catch(t){return console.warn("Invalid JSON string from ".concat("A/B Testing",': "').concat(e,'"')),!1}return!0}(t.data.variants.experiments)?[4,this.mergeAbVariant(t)]:[3,3];case 2:return[2,r.sent()];case 3:return[2,t.data];case 4:throw(n=r.sent()).statusCode&&n.statusCode===Ke.RESOURCE_NOT_FOUND&&(n.errorMessage=ke.SETTINGS_NOT_FOUND),n;case 5:return[2]}}))}))},e.prototype.fetchUserConsents=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return this.getDisableServerConsents()?[3,2]:(e=this.createFetchUserConsentsUrl(),[4,Tt(e,ke.FETCH_USER_CONSENTS)]);case 1:return[2,t.sent().data.reverse()];case 2:return[2,[]]}}))}))},e.prototype.fetchUserConsentsV2=function(e,t){return J(this,void 0,void 0,(function(){var n,r,i,s,o,a,c,u;return q(this,(function(l){switch(l.label){case 0:return n=function(e){return Q(Q([],X(yt),!1),X(Ct),!1).includes(e)?"implicit":"explicit"},this.getDisableServerConsents()?[3,2]:(r=this.createFetchUserConsentsV2Url(),[4,Tt(r,ke.FETCH_USER_CONSENTS)]);case 1:return i=l.sent(),s=i.data,o=null,a=null,e&&(c=JSON.parse(s.consentMeta),o={acString:s.acString||"",tcString:s.consentString,timestamp:c.timestamp,vendors:c.vendors,vendorsDisclosed:c.vendorsDisclosed}),t&&(a={ccpaString:s.consentString,timestamp:s.timestamp||Date.now()}),u=s.consents.reduce((function(e,t){return Q(Q([],X(e),!1),[{action:s.action,consentId:"",settingsVersion:s.settingsVersion,status:t.consentStatus,templateId:t.consentTemplateId,timestamp:s.timestamp||Date.now(),updatedBy:n(s.action)}],!1)}),[]),[2,K(K({consents:u},a&&{ccpa:a}),o&&{tcf:o})];case 2:return[2,null]}}))}))},e.prototype.fetchUserTcfData=function(){return J(this,void 0,void 0,(function(){var e,t,n,r,i;return q(this,(function(s){switch(s.label){case 0:return this.getDisableServerConsents()?[3,2]:(e=this.createFetchUserTcfDataUrl(),[4,Tt(e,ke.FETCH_USER_TCF_DATA)]);case 1:return t=s.sent(),n=t.data,r=n.tcString,i=n.meta,[2,K({tcString:r},JSON.parse(i)||{})];case 2:return[2,null]}}))}))},e.prototype.fetchUserTcfDataV2=function(){return J(this,void 0,void 0,(function(){var e,t,n,r,i;return q(this,(function(s){switch(s.label){case 0:return e=this.createFetchUserTcfDataV2Url(),[4,Tt(e,ke.FETCH_USER_TCF_DATA)];case 1:if((t=s.sent()).data&&t.data.tcf2)return n=t.data.tcf2,r=n.tcString,i=n.meta,[2,K({tcString:r},JSON.parse(i)||{})];throw new Error(ke.FETCH_USER_TCF_DATA)}}))}))},e.prototype.fetchVendorsList=function(e,t){return J(this,void 0,void 0,(function(){return q(this,(function(n){return[2,fetch(e).then((function(e){return e.json()})).then((function(e){return(null==t?void 0:t.length)&&(e.purposes=Object.entries(e.purposes).reduce((function(e,n){var r,i=X(n,2),s=i[0],o=i[1];return K(K({},e),-1===t.indexOf(parseInt(s,10))&&((r={})[s]=o,r))}),{}),e.vendors=Object.entries(e.vendors).reduce((function(e,n){var r,i=X(n,2),s=i[0],o=i[1];return K(K({},e),((r={})[s]=K(K({},o),{flexiblePurposes:o.flexiblePurposes.filter((function(e){return-1===t.indexOf(e)})),legIntPurposes:o.legIntPurposes.filter((function(e){return-1===t.indexOf(e)})),purposes:o.purposes.filter((function(e){return-1===t.indexOf(e)}))}),r))}),{})),e}))]}))}))},e.prototype.saveTCFConsents=function(e,t,n,r,i,s){return J(this,void 0,void 0,(function(){var o,a,c,u,l,d,p;return q(this,(function(f){switch(f.label){case 0:return o={consent:{status:!0},id:"tcf2",name:"tcf2",processorId:"abcd",version:"1.0.0"},a=t.tcString,c=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}(t,["tcString"]),u=[],s&&(l=we(e,o,n,r),d={consentMeta:{TCF2:JSON.stringify(c)},consentString:{TCF2:a},dataTransferObjects:[l]},u.push(this.saveConsentsV1_DEPRECATED(d))),i&&(p=Re({consentAction:n,consentString:{TCF2:a},dataTransferSettings:e,isAnalyticsEnabled:!0===i.isAnalyticsEnabled,isCcpa:!1,isConsentXDeviceEnabled:!0===i.isConsentAPIv2Enabled,isTcf:!0}),u.push(this.saveConsents(p))),[4,Promise.all(u)];case 1:return f.sent(),[2]}}))}))},e.prototype.saveConsentsV1_DEPRECATED=function(e){return J(this,void 0,void 0,(function(){var t,n,r,i=this;return q(this,(function(s){switch(s.label){case 0:if(this.getDisableServerConsents())return[3,4];t=Qt(),n=Zt(e),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,Wt((function(){return bt(i.getGraphQLUri(),n,ke.SAVE_CONSENTS,t)}),{onFirstFail:function(){return ye.appendToConsentsBuffer(e)},onFirstRetriedSuccess:function(){var t=ye.findBufferItem(e);t&&ye.removeConsentsBufferItem(t)}})];case 2:return s.sent(),[3,4];case 3:return(r=s.sent())instanceof Error&&console.error("".concat(ke.SAVE_CONSENTS_RETRY,": ").concat(r.message)),console.error(ke.SAVE_CONSENTS_RETRY),[3,4];case 4:return[2]}}))}))},e.prototype.saveConsents=function(e){return J(this,void 0,void 0,(function(){var t,n,r,i;return q(this,(function(s){switch(s.label){case 0:if(this.getDisableServerConsents())return[3,4];t=this.createSaveConsentsV2Url(),n=e,r=Qt(),s.label=1;case 1:return s.trys.push([1,3,,4]),[4,Wt((function(){return bt(t,n,ke.SAVE_CONSENTS,r)}),{onFirstFail:function(){return ye.appendToConsentsV2Buffer(e)},onFirstRetriedSuccess:function(){var t=ye.findV2BufferItem(e);t&&ye.removeConsentsV2BufferItem(t)}})];case 2:return s.sent(),[3,4];case 3:return(i=s.sent())instanceof Error&&console.error("".concat(ke.SAVE_CONSENTS_RETRY,": ").concat(i.message)),console.error(ke.SAVE_CONSENTS_RETRY),[3,4];case 4:return[2]}}))}))},e.prototype.sendConsents=function(e,t){return bt(this.getGraphQLUri(),e,ke.SAVE_CONSENTS,t)},e.prototype.sendConsentsV2=function(e,t){return bt(this.createSaveConsentsV2Url(),e,ke.SAVE_CONSENTS,t)},e.prototype.saveConsentsFromBuffer=function(){var e,t,n,r;return J(this,void 0,void 0,(function(){var i,s,o,a,c,u,l=this;return q(this,(function(d){switch(d.label){case 0:if(this.getDisableServerConsents())return[3,13];if(i=ye.fetchConsentsBuffer(),!Array.isArray(i)||!i.length)return[3,13];d.label=1;case 1:d.trys.push([1,7,8,13]),s=function(){var e,t,n,i;return q(this,(function(s){switch(s.label){case 0:r=c.value,o=!1,s.label=1;case 1:s.trys.push([1,,6,7]),e=r,t=Qt(),n=Zt(e.consents),s.label=2;case 2:return s.trys.push([2,4,,5]),[4,Wt((function(){return l.sendConsents(n,t)}))];case 3:return s.sent(),ye.removeConsentsBufferItem(e),[3,5];case 4:return(i=s.sent())instanceof Error&&console.error("".concat(ke.SAVE_CONSENTS_RETRY,": ").concat(i.message)),console.error(ke.SAVE_CONSENTS_RETRY),[3,5];case 5:return[3,7];case 6:return o=!0,[7];case 7:return[2]}}))},o=!0,a=Z(i),d.label=2;case 2:return[4,a.next()];case 3:return c=d.sent(),(e=c.done)?[3,6]:[5,s()];case 4:d.sent(),d.label=5;case 5:return[3,2];case 6:return[3,13];case 7:return u=d.sent(),t={error:u},[3,13];case 8:return d.trys.push([8,,11,12]),o||e||!(n=a.return)?[3,10]:[4,n.call(a)];case 9:d.sent(),d.label=10;case 10:return[3,12];case 11:if(t)throw t.error;return[7];case 12:return[7];case 13:return[2]}}))}))},e.prototype.saveConsentsV2FromBuffer=function(){var e,t,n,r;return J(this,void 0,void 0,(function(){var i,s,o,a,c,u,l=this;return q(this,(function(d){switch(d.label){case 0:if(this.getDisableServerConsents())return[3,13];if(i=ye.fetchConsentsV2Buffer(),!Array.isArray(i)||!i.length)return[3,13];d.label=1;case 1:d.trys.push([1,7,8,13]),s=function(){var e,t,n,i;return q(this,(function(s){switch(s.label){case 0:r=c.value,o=!1,s.label=1;case 1:s.trys.push([1,,6,7]),e=r,t=Qt(),n=e.consents,s.label=2;case 2:return s.trys.push([2,4,,5]),[4,Wt((function(){return l.sendConsentsV2(n,t)}))];case 3:return s.sent(),ye.removeConsentsV2BufferItem(e),[3,5];case 4:return(i=s.sent())instanceof Error&&console.error("".concat(ke.SAVE_CONSENTS_RETRY,": ").concat(i.message)),console.error(ke.SAVE_CONSENTS_RETRY),[3,5];case 5:return[3,7];case 6:return o=!0,[7];case 7:return[2]}}))},o=!0,a=Z(i),d.label=2;case 2:return[4,a.next()];case 3:return c=d.sent(),(e=c.done)?[3,6]:[5,s()];case 4:d.sent(),d.label=5;case 5:return[3,2];case 6:return[3,13];case 7:return u=d.sent(),t={error:u},[3,13];case 8:return d.trys.push([8,,11,12]),o||e||!(n=a.return)?[3,10]:[4,n.call(a)];case 9:d.sent(),d.label=10;case 10:return[3,12];case 11:if(t)throw t.error;return[7];case 12:return[7];case 13:return[2]}}))}))},e.prototype.setTrackingPixel=function(e){var t,n="".concat(document.location.origin).concat(document.location.pathname);e.referrer?function(e){try{return Boolean(new URL(e))}catch(e){return!1}}(e.referrer)?t=e.referrer:(console.warn(ke.CUSTOM_REFERRER_NOT_VALID),t=n):t=n;var r=(new Date).getTime(),i=encodeURIComponent(t),s="".concat(this.isEuMode()?this.API.EU_URI.TRACK_EVENT:this.API.URI.TRACK_EVENT,"?v=").concat(1,"&sid=").concat(this.settingsId,"&t=").concat(e.eventType,"&abv=").concat(e.abTestVariant,"&r=").concat(i,"&cb=").concat(r);(new Image).src=s},e.prototype.updateTagLoggerData=function(e){var t=this.settingsId,n=window.location.href,r=en({settingsId:t,source:n,targets:e});this.saveTagLoggerData(r)},e.prototype.addJsonCacheBustingString=function(e){return this.jsonCacheBustingString?"".concat(e,"?c=").concat(this.jsonCacheBustingString):e},e.prototype.createAvailableLanguagesUrl=function(){return this.addJsonCacheBustingString("".concat(this.getCdnUri(),"/").concat(this.API.FOLDER.SETTINGS,"/").concat(this.settingsId,"/").concat(this.jsonFileVersion,"/languages.json"))},e.prototype.createLanguagesUrl=function(){return this.addJsonCacheBustingString("".concat(this.getCdnUri(),"/").concat(this.API.FOLDER.TRANSLATIONS,"/translations-").concat(this.jsonFileLanguage,".json"))},e.prototype.getSettingsUrl=function(){return"".concat(this.getCdnUri(),"/").concat(this.API.FOLDER.SETTINGS,"/").concat(this.settingsId,"/").concat(this.jsonFileVersion)},e.prototype.createSettingsJsonUrl=function(){return this.addJsonCacheBustingString("".concat(this.getSettingsUrl(),"/").concat(this.jsonFileLanguage,".json"))},e.prototype.createAcmVendorsJsonUrl=function(){return this.addJsonCacheBustingString("".concat(this.getCdnUri(),"/tcfac/acp.json"))},e.prototype.createSessionTrackingUrl=function(){return"".concat(this.isEuMode()?this.API.EU_URI.TRACK_SESSION:this.API.URI.TRACK_SESSION,"?settingsId=").concat(this.settingsId)},e.prototype.createFetchUserConsentsUrl=function(){return"".concat(this.isEuMode()?this.API.EU_URI.FETCH_CONSENTS:this.API.URI.FETCH_CONSENTS,"?controllerId=").concat(this.controllerIdInstance.value)},e.prototype.createFetchUserConsentsV2Url=function(){return"".concat(this.isEuMode()?this.API.EU_URI.FETCH_CONSENTS_V2:this.API.URI.FETCH_CONSENTS_V2,"?settingsId=").concat(this.getSettingsId(),"&controllerId=").concat(this.controllerIdInstance.value)},e.prototype.createFetchUserTcfDataUrl=function(){return"".concat(this.API.URI.FETCH_TCF_DATA,"?controllerId=").concat(this.controllerIdInstance.value)},e.prototype.createFetchUserTcfDataV2Url=function(){return"".concat(this.getTcfDataV2Uri(),"?controllerId=").concat(this.controllerIdInstance.value,"&tcf2=true&settingsId=").concat(this.settingsId)},e.prototype.createRulesetUrl=function(){return"".concat(this.API.URI.RULESET,"/").concat(this.API.FOLDER.RULESET,"/").concat(this.rulesetId,".json")},e.prototype.createSaveConsentsV2Url=function(){return"".concat(this.isEuMode()?this.API.EU_URI.SAVE_CONSENTS_V2:this.API.URI.SAVE_CONSENTS_V2,"/uw/").concat(3)},e.prototype.saveTagLoggerData=function(e){try{bt(this.getGraphQLUri(),e,"",K(K({},Qt()),{keepalive:!0}))}catch(e){console.warn(ke.TAGLOGGER,e)}},e}(),Qt=function(){return{credentials:"omit",headers:{Accept:"application/json","Access-Control-Allow-Origin":"*","X-Request-ID":Bt()},mode:"cors"}},Zt=function(e){var t=e.dataTransferObjects,n=e.consentMeta,r=e.consentString;return{query:"mutation saveConsents($consents: [NewCreateConsentInput], $consentMeta: ConsentMeta, $consentString: ConsentString)\n      {\n        saveConsents(consents: $consents, consentMeta: $consentMeta, consentString: $consentString) { data { consentId } }\n      }",variables:{consentMeta:n,consents:tn(t),consentString:r}}},en=function(e){return{operationName:"saveTagLoggerData",query:"mutation saveTagLoggerData($settingsId: String, $source: String, $targets: [String])\n        {\n          saveTagLoggerData(settingsId: $settingsId, source: $source, targets: $targets)\n        }",variables:{settingsId:e.settingsId,source:e.source,targets:e.targets}}},tn=function(e){return e.map((function(e){return{action:e.consent.action,appVersion:e.applicationVersion,consentStatus:e.consent.status?"1":"0",consentTemplateId:e.service.id,consentTemplateVersion:e.service.version,controllerId:e.settings.controllerId,language:e.settings.language,processorId:e.service.processorId,referrerControllerId:e.settings.referrerControllerId,settingsId:e.settings.id,settingsVersion:e.settings.version,updatedBy:e.consent.type}}))};!function(e){e[e.NO=0]="NO",e[e.YES=1]="YES",e[e.NOT_SET=2]="NOT_SET"}(zt||(zt={}));var nn=function(){function e(){this.isBotEnabled=!1,this.isBot=zt.NOT_SET}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){e.instance.isBot=zt.NOT_SET},e.prototype.isRobot=function(){if(this.isBot===zt.NOT_SET)if(this.isBotEnabled){var e=window.navigator.userAgent,t=new RegExp("AdsBot-Google|AdsBot-Google-Mobile|bingbot/|BingPreview/|Chrome-Lighthouse|DuckDuckBot/|Feedfetcher-Google|Googlebot/|Google Favicon|Googlebot-Image/|Googlebot-Mobile/|Googlebot-News|Googlebot-Video/|Google Page Speed Insights|Google PP|Google-Read-Aloud|Google Search Console|Google-SearchByImage|Google-Speakr|Google-Structured-Data-Testing-Tool|Google Web Preview|Slurp/|Storebot-Google|YahooSeeker|YahooCacheSystem|Yahoo! Site Explorer Feed Validator|Yahoo! Slurp","i");this.isBot=t.test(e)?zt.YES:zt.NO}else this.isBot=zt.NO;return this.isBot===zt.YES},e}(),rn="UC_SDK_EVENT",sn=["BkeKqEjuoZQ","dUzxiHb6Q","dsS7z9Hv4","l5j0HxFqd","2AMblLg3I","jzMEq56vW","00xb_vkGq"];var on,an=["LykAT-gy","UekC8ye4S","9V8bg4D63","ByzZ5EsOsZX","S1_9Vsuj-Q","dqFgQeZH","twMyStLkn","B1Hk_zoTX","pxiRY9112","dyHOCwp5Y","DHS2sEi4b"],cn=["HkocEodjb7","87JYasXPF"],un=function(){function e(){this.dataLayerNames=[],this.windowEventNames=[],this.dataLayer=new Map,this.blockDataLayerPush=!1,this.isUniversalEventTrackingDisabled=!1}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.prototype.setBlockDataLayerPush=function(e){this.blockDataLayerPush=e},e.prototype.setUetDisabled=function(e){this.isUniversalEventTrackingDisabled=e},e.prototype.shouldBlockDataLayerPush=function(){return this.blockDataLayerPush},e.resetInstance=function(){e.instance.dataLayerNames=[],e.instance.windowEventNames=[],e.instance.dataLayer=new Map},e.prototype.init=function(e){var t=this;e.forEach((function(e){e.type===U.DATA_LAYER?t.dataLayerNames=Ne(e.names,[]):e.type===U.WINDOW_EVENT&&(t.windowEventNames=Ne(e.names,[]))})),this.windowEventNames.includes(rn)||this.windowEventNames.push(rn)},e.prototype.setDataLayer=function(e,t){this.dataLayer.set(t,e)},e.prototype.getDataLayer=function(){return this.dataLayer},e.prototype.dispatch=function(e,t,n,r){Ae(e)&&(this.pushEventsToDataLayer(e,t,n,r),this.dispatchWindowEvents(e,r))},e.prototype.isValidDataLayer=function(e){return window[e]&&(Array.isArray(window[e])||Object.prototype.hasOwnProperty.call(window[e],"push"))},e.prototype.pushEventsToDataLayer=function(e,t,n,r){var i=this;Ae(this.dataLayerNames)&&this.dataLayerNames.forEach((function(s){if(window[s]=Ne(window[s],[]),!i.isValidDataLayer(s))throw Error("DataLayer: ".concat(s," is not of a valid type!"));if(function(e,t){if(!e){var n,r,i,s=(n=t.filter((function(e){var t=e.consent,n=t.type,r=t.action;return sn.includes(e.service.id)&&("explicit"===n||"implicit"===n&&"onNonEURegion"===r)})),r=n.length>0,i=n.every((function(e){return e.consent.status})),{adStorageStatus:r&&i?"granted":"denied",shouldUpdate:r});s.shouldUpdate&&function(e){var t={adStorage:e};(window.uetq||[]).push("consent","update",{ad_storage:e}),window.dispatchEvent(new CustomEvent("UC_UET_UPDATE",{detail:t}))}(s.adStorageStatus)}}(i.isUniversalEventTrackingDisabled,e),n&&"dataLayer"===s){var o=!1,a=!1,c=!1,u=!1,l=!1;dn("set","developer_id.dOThhZD",!0),e.forEach((function(e){var t=e.service.id,n=e.consent,r=n.status,i=n.type,s=n.action;cn.includes(t)&&("explicit"===i||"implicit"===i&&"onNonEURegion"===s)&&(o=o||r,l=!0),an.includes(t)&&("explicit"===i||"implicit"===i&&"onNonEURegion"===s)&&(u=!0,l=!0,r||(c=!0))})),t?a=!!t.consent:u&&!c&&(a=!0);var d={adsDataRedaction:!a};if(l){d=K(K({},d),{adPersonalization:a?"granted":"denied",adStorage:a?"granted":"denied",adUserData:a?"granted":"denied",analyticsStorage:o?"granted":"denied"}),dn("consent","update",{ad_personalization:d.adPersonalization,ad_storage:d.adStorage,ad_user_data:d.adUserData,analytics_storage:d.analyticsStorage}),ye.saveGcmData(d);var p=new window.CustomEvent("UC_GCM_UPDATE",{detail:d});window.dispatchEvent(p)}dn("set","ads_data_redaction",d.adsDataRedaction)}window[s].push(ln(e,r)),i.setDataLayer(window[s],s),i.shouldBlockDataLayerPush()||"explicit"===e[0].consent.type&&e.forEach((function(e){e.consent.status||window[s].push({event:"".concat(e.service.name," EXPLICIT_DENY")})}))}))},e.prototype.dispatchWindowEvents=function(e,t){if(Ae(this.windowEventNames)){var n=ln(e,t);this.windowEventNames.forEach((function(e){var t=new window.CustomEvent(e,{detail:n});window.dispatchEvent(t)}))}},e}(),ln=function(e,t){var n={action:null!=t?t:e[0].consent.action,event:"consent_status",type:e[0].consent.type,ucCategory:{}};return e.forEach((function(e){var t,r,i,s=(null===(r=e.service.categorySlug)||void 0===r?void 0:r.length)&&Object.prototype.hasOwnProperty.call(n.ucCategory,e.service.categorySlug);n=K(K({},n),((t={})[e.service.name]=e.consent.status,t)),(null===(i=e.service.categorySlug)||void 0===i?void 0:i.length)&&(n.ucCategory[e.service.categorySlug]=s&&n.ucCategory[e.service.categorySlug]!==e.consent.status?null:e.consent.status)})),n},dn=function(){window.dataLayer.push(arguments)},pn="en",fn="https://api.usercentrics.eu/tcf2/",hn="https://api.usercentrics.eu/gvl/v3/",gn="https://config.eu.usercentrics.eu/tcf2/",vn="https://config.eu.usercentrics.eu/gvl/v3/",Sn="[LANG].json",En="[LANG].json",_n="en-v2.json",mn="en.json";!function(e){e.TEXT_JAVASCRIPT="text/javascript",e.TEXT_PLAIN="text/plain"}(on||(on={}));var In,yn="data-usercentrics",Cn=function(){function e(){}return e.enableScriptsForServicesWithConsent=function(t){var n=e.getDisabledScripts();Array.prototype.forEach.call(n,(function(n){e.disabledScriptHasConsent(t,n)&&e.enableScript(n)}))},e.getDisabledScripts=function(){return document.querySelectorAll("script[".concat(yn,'][type="').concat(on.TEXT_PLAIN,'"]'))},e.disabledScriptHasConsent=function(e,t){return e.some((function(e){return e.name===t.getAttribute(yn)}))},e.enableScript=function(t){var n,r,i;r=t.src?e.createSrcScriptTag(t):e.createInlineScriptTag(t),(i=(n=t).parentNode)&&i.replaceChild(r,n)},e.createSrcScriptTag=function(t){var n=e.cloneScriptTag(t);return n.removeAttribute(yn),n.type=on.TEXT_JAVASCRIPT,n},e.createInlineScriptTag=function(t){var n=e.cloneScriptTag(t);n.removeAttribute(yn);var r=document.createTextNode(t.text);return n.appendChild(r),n.type=on.TEXT_JAVASCRIPT,n},e.cloneScriptTag=function(e){var t=document.createElement("script");return Array.from(e.attributes).forEach((function(e){t.setAttribute(e.name,e.value)})),t},e}(),Tn=function(e,t,n){var r,i;this.anyDomain=e.anyDomain||"any domain (ex. first party cookie)",this.cookieRefresh=(null==t?void 0:t.COOKIE_REFRESH)||(null===(r=null==n?void 0:n.labels)||void 0===r?void 0:r.COOKIE_REFRESH)||"Cookie Refresh",this.cookieStorage=(null==t?void 0:t.COOKIE_STORAGE)||(null===(i=null==n?void 0:n.labels)||void 0===i?void 0:i.COOKIE_STORAGE)||"Cookie Storage",this.day=e.day,this.days=e.days,this.description=e.storageInformationDescription||"Below you can see the longest potential duration for storage on a device, as set when using the cookie method of storage and if there are any other methods used.",this.domain=e.domain||"Domain",this.duration=e.duration||"Duration",this.error=e.informationLoadingNotPossible||"Sorry; we could not load the required information.",this.hour=e.hour,this.hours=e.hours,this.identifier=e.identifier||"Identifier",this.loading=e.loadingStorageInformation||"Loading storage information",this.maximumAge=e.maximumAgeCookieStorage||"Maximum age of cookie storage",this.minute=e.minute,this.minutes=e.minutes,this.month=e.month,this.months=e.months,this.multipleDomains=e.multipleDomains||"multiple subdomains may exist",this.name=e.name||"Name",this.no=e.no||"no",this.nonCookieStorage=e.nonCookieStorage||"Non-cookie storage",this.purposes=e.purposes||"Purposes",this.second=e.second||"second",this.seconds=e.seconds||"seconds",this.session=e.session||"Session",this.storedInformation=e.storedInformation||"Stored Information",this.storedInformationDescription=e.storedInformationDescription||"This service uses different means of storing information on a user’s device as listed below.",this.title=e.storageInformation||"Storage Information",this.titleDetailed=e.detailedStorageInformation||"Detailed Storage Information",this.tryAgain=e.tryAgain||"Try again?",this.type=e.type||"Type",this.year=e.year,this.years=e.years,this.yes=e.yes||"yes"},bn=function(e,t){var n=e.defaultConsentStatus;return{history:[],status:!!(null==t?void 0:t.isEssential)||n}},An=function(e,t){return(null==e?void 0:e.description)||(null==t?void 0:t.descriptionOfService)||(null==t?void 0:t.description)||""},On=function(e,t,n){var r,i=null==n?void 0:n.find((function(t){return e.templateId===t.templateId&&e.version===t.version})),s=(null==t?void 0:t.legalBasis)&&e.legalBasisList?e.legalBasisList.reduce((function(e,n){return(null==t?void 0:t.legalBasis[n])?Q(Q([],X(e),!1),[null==t?void 0:t.legalBasis[n]],!1):e}),[]):[];this.description=An(e,i),this.id=e.templateId,this.legalBasis=i&&!e.disableLegalBasis?function(e,t){var n=e.legalBasisList,r=e.legalGround;return t.length>0?t:function(e,t){return Ae(e)?e:[t]}(n,r)}(i,s):[],this.name=(null===(r=e._meta)||void 0===r?void 0:r.name)||(null==i?void 0:i.dataProcessor)||(null==i?void 0:i.dataProcessors[0])||""},Nn=function(e){function t(t,n,r,i){var s,o,a,c,u,l,d,p,f=this;f=e.call(this)||this;var h=t.labels;if(f.ariaLabels=K(K({},(null==r?void 0:r.labelsAria)||Je),{closeButton:h.btnClose}),f.categories=n.categories.map((function(e){return{description:e.description,label:e.label,slug:e.categorySlug}})),f.cookieInformation=new Tn(h,null,r),f.general={back:(null===(s=null==r?void 0:r.labels)||void 0===s?void 0:s.BACK)||"Back",consentGiven:h.accepted,consentNotGiven:h.denied,consentType:h.consentType,controllerId:(null===(o=null==r?void 0:r.labels)||void 0===o?void 0:o.CID_TITLE)||"Controller ID",copied:h.copied,copy:h.copy,date:h.date,decision:h.decision,details:(null===(a=null==r?void 0:r.labels)||void 0===a?void 0:a.DETAILS)||"Details",explicit:h.explicit,gpcSignalHonored:(null===(c=null==r?void 0:r.labels)||void 0===c?void 0:c.GPC_SIGNAL_HONORED)||h.gpcSignalHonored||"The GPC signal is honored",implicit:h.implicit,implicitNo:h.noImplicit,implicitYes:h.yesImplicit,languageChange:(null===(u=null==r?void 0:r.labels)||void 0===u?void 0:u.LANGUAGE_CHANGE)||"Selecting an option will immediately change the language",privacyButton:h.btnChipName,showLess:h.readLess,showMore:h.btnBannerReadMore||h.showMore,subservice:(null===(l=null==r?void 0:r.labels)||void 0===l?void 0:l.SUB_SERVICE)||"Subservice",subservices:(null===(d=null==r?void 0:r.labels)||void 0===d?void 0:d.SUB_SERVICES)||"Subservices",subservicesDescription:(null===(p=null==r?void 0:r.labels)||void 0===p?void 0:p.SUB_SERVICES_DESCRIPTION)||"Below you can find all the services that are subordinate to this service. The current consent status of this service applies to all subservices."},f.links={cookiePolicy:{ariaLabel:Je.cookiePolicyButton,label:Ne(h.cookiePolicyLinkText,""),url:t.cookiePolicyUrl},imprint:{ariaLabel:Je.imprintButton,label:h.imprintLinkText||null,url:t.imprintUrl},privacyPolicy:{ariaLabel:Je.privacyPolicyButton,label:h.privacyPolicyLinkText,url:t.privacyPolicyUrl}},t.integrations&&t.integrations.mine){var g={mine:{ariaLabel:"",label:t.integrations.mine[0].privacyCenterLabel,url:t.integrations.mine[0].privacyCenterUrl}};f.integrationLinks=K(K({},f.integrationLinks),g)}return f.poweredBy={label:"Powered by",partnerUrlLabel:Ne(h.partnerPoweredByLinkText,null),urlLabel:"Usercentrics Consent Management"},f.service={dataCollected:{description:h.dataCollectedInfo,title:h.dataCollectedList},dataDistribution:{processingLocationDescription:h.locationofProcessingInfo,processingLocationTitle:h.locationOfProcessing,thirdPartyCountriesDescription:h.transferToThirdCountriesInfo,thirdPartyCountriesTitle:h.transferToThirdCountries},dataProtectionOfficer:{description:h.dataProtectionOfficerInfo,title:h.dataProtectionOfficer},dataPurposes:{description:h.dataPurposesInfo,title:h.dataPurposes},dataRecipients:{description:h.dataRecipientsListInfo,title:h.dataRecipientsList},descriptionTitle:h.descriptionOfService,history:{description:null,title:h.history},legalBasis:{description:h.legalBasisInfo,title:h.legalBasisList},processingCompanyTitle:h.processingCompany,retentionPeriod:{description:h.retentionPeriodInfo,title:h.retentionPeriod},technologiesUsed:{description:h.technologiesUsedInfo,title:h.technologiesUsed},urls:{cookiePolicyTitle:h.cookiePolicyInfo,optOutTitle:h.optOut,privacyPolicyTitle:h.policyOf}},f.services=n.consentTemplates.reduce((function(e,t){var n=new On(t,r,i);return Q(Q([],X(e),!1),[n],!1)}),[]),f}return R(t,e),t}((function(){this.ariaLabels=Je})),wn=function(e){function t(t,n,r,i){var s=e.call(this,t,n,r,i)||this;return s.buttons={optOutNotice:t.ccpa.optOutNoticeLabel||"Do not sell my personal information",save:t.ccpa.btnSave||"okay",showSecondLayer:t.ccpa.btnMoreInfo||t.labels.btnMore},s.firstLayer={description:{default:t.ccpa.firstLayerDescription||"",short:t.ccpa.firstLayerMobileDescription||"",shortDesktop:t.ccpa.firstLayerShortMessage||"",shortMobile:t.ccpa.firstLayerMobileDescription||""},title:t.ccpa.firstLayerTitle||""},s.secondLayer={categoryTab:t.secondLayer.tabsCategoriesLabel,description:t.ccpa.secondLayerDescription||"",serviceTab:t.secondLayer.tabsServicesLabel,title:t.ccpa.secondLayerTitle||""},s}return R(t,e),t}(Nn),Rn=function(e,t){var n,r,i;this.acceptAllImplicitlyOutsideEU=e.displayOnlyForEU,this.consentAnalytics=Ne(e.consentAnalytics,!1),this.consentAPIv2=Ne(e.consentAPIv2,!1),this.consentXDevice=Ne(e.consentXDevice,!1),this.consentSharingIFrameIsActive=e.consentSharingIFrameIsActive,this.dataExchangeSettings=e.dataExchangeOnPage.reduce((function(e,t){return t.type===xe.DATA_LAYER?e.push({names:t.names,type:U.DATA_LAYER}):t.type===xe.WINDOW_EVENT&&e.push({names:t.names,type:U.WINDOW_EVENT}),e}),[]),this.googleConsentMode=e.googleConsentMode,this.id=e.settingsId,this.isCcpaEnabled=e.framework?["CCPA","UCPA","CTDPA","VCDPA","CPRA","CPA"].includes(e.framework):null!==(r=null===(n=e.ccpa)||void 0===n?void 0:n.isActive)&&void 0!==r&&r,this.isEmbeddingsEnabled=!0,this.isTagLoggerActive=e.tagLoggerIsActive,this.isTcfEnabled=e.framework?"TCF2"===e.framework:null!==(i=e.tcf2Enabled)&&void 0!==i&&i,this.language={available:e.languagesAvailable,selected:t},this.reshowBanner=Ne(e.reshowBanner,-1),this.renewConsentsTimestamp=Ne(e.renewConsentsTimestamp,null),this.showFirstLayerOnVersionChange=e.showInitialViewForVersionChange.map((function(e){switch(e){case Be.MAJOR:return k.MAJOR;case Be.MINOR:return k.MINOR;case Be.PATCH:default:return k.PATCH}})),this.version=e.version},Ln=function(e){function t(t,n,r,i){var s,o,a,c=this;return(c=e.call(this,t,n,r,i)||this).buttons={acceptAll:t.labels.btnAcceptAll,cnilDeny:(null===(s=null==r?void 0:r.labels)||void 0===s?void 0:s.CNIL_DENY_LINK_TEXT)||"Continuer sans accepter",denyAll:t.labels.btnDeny,save:t.labels.btnSave,showSecondLayer:t.labels.btnMore},c.firstLayer={description:{default:t.bannerMessage||"",short:t.bannerMobileDescription||"",shortDesktop:t.firstLayer.shortMessage||"",shortMobile:t.bannerMobileDescription||""},title:Ne(t.labels.firstLayerTitle,"Privacy Settings")},c.secondLayer={acceptButtonLabel:t.secondLayer.acceptButtonText,categoryTab:t.secondLayer.tabsCategoriesLabel,dataTransferFilter:{all:(null===(o=null==r?void 0:r.labels)||void 0===o?void 0:o.ALL)||"All",thirdCountry:(null===(a=null==r?void 0:r.labels)||void 0===a?void 0:a.THIRD_COUNTRY_TRANSFER)||"Data Transfer to Third Countries"},denyButtonLabel:t.secondLayer.denyButtonText,description:t.labels.titleCorner,serviceTab:t.secondLayer.tabsServicesLabel,title:t.labels.headerCorner},c}return R(t,e),t}(Nn),Dn=function(e,t,n){this.privacyButton=Ne(null==n?void 0:n.labelsAria.privacyButton,""),this.services=e.consentTemplates.reduce((function(e,r){var i=new On(r,n,t);return Q(Q([],X(e),!1),[i],!1)}),[])},Pn=function(){function e(){this.needsSessionRestore=!1,this.apiInstance=Xt.getInstance(),this.locationInstance=$t.getInstance()}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){delete e.instance.noShow},e.prototype.unsetNoShow=function(){delete this.noShow},e.prototype.getIsUsingNoShow=function(){return void 0!==this.noShow},e.prototype.getNoShow=function(){return!0===this.noShow},e.prototype.setNoShow=function(e){this.noShow=e},e.prototype.resolveSettingsId=function(){return J(this,void 0,void 0,(function(){var e,t,n,r,i,s,o,a;return q(this,(function(c){switch(c.label){case 0:return[4,this.apiInstance.fetchRuleset()];case 1:return e=c.sent(),n="",r=e.location,i=e.rules,s=e.defaultRule,o=s.settingsId,a=s.noShow,r&&r.code&&(t=r.regionCode&&i.find((function(e){var t;return null===(t=e.locations)||void 0===t?void 0:t.includes(r.regionCode)}))||i.find((function(e){var t;return null===(t=e.locations)||void 0===t?void 0:t.includes(r.code)})),this.locationInstance.setUserCountryData(r),ye.setUserCountryResponse($t.mapUserCountryData(r))),this.unsetNoShow(),t?(n=t.settingsId,void 0!==t.noShow&&this.setNoShow(!0===t.noShow)):(n=o,this.setNoShow(!0===a)),[2,{name:"Resolved Settings Id",noShow:this.getNoShow(),settingsId:n}]}}))}))},e}(),Vn=function(e){this.cookieMaxAgeSeconds=null,this.cookieRefresh=null,this.dataCollected=[],this.dataDistribution=null,this.dataProtectionOfficer=null,this.dataPurposes=[],this.dataRecipients=[],this.deviceStorage=null,this.deviceStorageDisclosureUrl=null,this.language=null,this.processingCompany=null,this.retentionPeriodDescription=null,this.technologiesUsed=[],this.urls=null,this.usesCookies=null,this.usesNonCookieAccess=null,this.cookieMaxAgeSeconds=Ne(null==e?void 0:e.cookieMaxAgeSeconds,null),this.cookieRefresh=Ne(null==e?void 0:e.cookieRefresh,null),this.dataCollected=e?Un(e,Ge.DATA_COLLECTED_LIST):[],this.dataDistribution=e?Mn(e):null,this.dataProtectionOfficer=Ne(null==e?void 0:e.dataProtectionOfficer,null),this.dataPurposes=e?kn(e):[],this.dataRecipients=e?Un(e,Ge.DATA_RECIPIENTS_LIST):[],this.description=An(null,e),this.deviceStorage=Ne(null==e?void 0:e.deviceStorage,null),this.deviceStorageDisclosureUrl=Ne(null==e?void 0:e.deviceStorageDisclosureUrl,null),this.language=e?Fn(e):null,this.processingCompany=e?xn(e):null,this.retentionPeriodDescription=e?Gn(e):null,this.technologiesUsed=e?Un(e,Ge.TECHNOLOGY_USED):[],this.usesCookies=Ne(null==e?void 0:e.usesCookies,null),this.usesNonCookieAccess=Ne(null==e?void 0:e.usesNonCookieAccess,null),this.urls=e?Bn(e):null},Un=function(e,t){var n;return Ae(e[t])?e[t]:(null===(n=e[t])||void 0===n?void 0:n.length)>0?[e[t]]:[]},kn=function(e){var t=Un(e,Ge.DATA_PURPOSES_LIST);return Ae(t)?t:e.dataPurposes},Mn=function(e){return{processingLocation:e.locationOfProcessing,thirdPartyCountries:e.thirdCountryTransfer?e.thirdCountryTransfer.split(","):[]}},Fn=function(e){return{available:e.languagesAvailable,selected:e.language}},xn=function(e){return{address:e.addressOfProcessingCompany,dataProtectionOfficer:e.dataProtectionOfficer,name:e.nameOfProcessingCompany||e.processingCompany}},Gn=function(e){var t;return e.retentionPeriodDescription||(null===(t=e.retentionPeriodList)||void 0===t?void 0:t[0])||""},Bn=function(e){return{cookiePolicy:e.cookiePolicyURL,dataProcessingAgreement:e.linkToDpa,optOut:e.optOutUrl,privacyPolicy:e.privacyPolicyURL||e.policyOfProcessorUrl}},Hn=function(e){function t(t,n,r,i,s){var o,a,c,u,l,d=this;d=e.call(this,t,n,i,s)||this;var p=t.labels,f=t.tcf2;return d.cookieInformation.purposes=f.labelsPurposes,d.buttons={acceptAll:f.buttonsAcceptAllLabel,denyAll:f.buttonsDenyAllLabel,manageSettings:f.linksManageSettingsLabel,save:f.buttonsSaveLabel,showVendorTab:f.linksVendorListLinkLabel},d.firstLayer={description:{additionalInfo:f.firstLayerAdditionalInfo||null,dataSharedOutsideEUText:f.showDataSharedOutsideEUText&&f.dataSharedOutsideEUText?f.dataSharedOutsideEUText:null,default:f.firstLayerDescription.replace("%VENDOR_COUNT%",r.toString()),resurfaceNote:f.firstLayerNoteResurface||null},disclaimer:{serviceScope:f.firstLayerNoteService},title:f.firstLayerTitle},d.secondLayer={dataSharedOutsideEU:{text:(null===(o=null==i?void 0:i.labels)||void 0===o?void 0:o.VENDORS_OUTSIDE_EU)||null,title:p.transferToThirdCountries},dataTransferFilter:{all:(null===(a=null==i?void 0:i.labels)||void 0===a?void 0:a.ALL)||"All",thirdCountry:(null===(c=null==i?void 0:i.labels)||void 0===c?void 0:c.THIRD_COUNTRY_TRANSFER)||"Data Transfer to Third Countries"},description:f.secondLayerDescription,maxStorageDurationText:((null===(u=null==i?void 0:i.labels)||void 0===u?void 0:u.WEB_TCF_MAX_STORAGE_DURATION_TEXT)||"The choices you make regarding the purposes and entities listed are saved in the local storage %TC_STRING_LOCATION%, for a maximum of 13 months. After 13 months, the CMP will prompt again.").replace("%TC_STRING_LOCATION%","uc_tcf"),maxStorageDurationTitle:(null===(l=null==i?void 0:i.labels)||void 0===l?void 0:l.WEB_TCF_MAX_STORAGE_DURATION_TITLE)||"CMP Storage and Access",purposesTab:f.tabsPurposeLabel,title:f.secondLayerTitle,vendorsTab:f.tabsVendorsLabel},d.titles={acmVendors:f.atpListTitle,examples:f.examplesLabel||"Examples",features:f.labelsFeatures,iabVendors:f.labelsIabVendors,nonIabPurposes:f.labelsNonIabPurposes,nonIabVendors:f.labelsNonIabVendors,purposes:f.labelsPurposes},d.toggles={consent:t.tcf2.togglesConsentToggleLabel,legitimateInterest:t.tcf2.togglesLegIntToggleLabel,specialFeaturesToggle:{offLabel:t.tcf2.togglesSpecialFeaturesToggleOff,onLabel:t.tcf2.togglesSpecialFeaturesToggleOn}},d.vendor={dataCategories:f.categoriesOfDataLabel,dataRetention:f.dataRetentionPeriodLabel,dataRetentionUnit:p.days,features:f.vendorFeatures,legitimateInterest:f.vendorLegitimateInterestPurposes,legitimateInterestClaim:f.legitimateInterestLabel,privacyPolicy:p.privacyPolicyLinkText,purposes:f.vendorPurpose,specialFeatures:f.vendorSpecialFeatures,specialPurposes:f.vendorSpecialPurposes,toggleAll:f.labelsActivateAllVendors},d.acmVendor={privacyPolicy:f.atpAdditionalInfo},d}return R(t,e),t}(Nn),jn=function(){function e(){this.aggregatedServices=[],this.allLegacyServicesHaveName=!1,this.isVariantLoaded=!1,this.isAggregatorLoaded=!1,this.language="",this.translations=null,this.botInstance=nn.getInstance(),this.locationInstance=$t.getInstance(),this.rulesetInstance=Pn.getInstance(),this.apiInstance=Xt.getInstance(),this._core=null,this._coreJson=null,this._data=null,this._dpsJson=null,this._labels=null,this._legacySettings=null,this._ui=null,this.controllerIdInstance=Ht.getInstance(),this.acceptAllImplicitlyOnInit=null,this.denyAllExplicitlyOnInit=null}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){e.instance.allLegacyServicesHaveName=!1,e.instance.core=null,e.instance.data=null,e.instance.labels=null,e.instance.legacySettings=null,e.instance.ui=null,e.instance.dpsJson=null,e.instance.coreJson=null},Object.defineProperty(e.prototype,"core",{get:function(){return this._core},set:function(e){this._core=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"coreJson",{get:function(){return this._coreJson},set:function(e){this._coreJson=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"data",{get:function(){return this._data},set:function(e){this._data=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dpsJson",{get:function(){return this._dpsJson},set:function(e){this._dpsJson=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"labels",{get:function(){return this._labels},set:function(e){this._labels=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"legacySettings",{get:function(){return this._legacySettings},set:function(e){this._legacySettings=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"ui",{get:function(){return this._ui},set:function(e){this._ui=e},enumerable:!1,configurable:!0}),e.prototype.init=function(e,t,n){return J(this,void 0,void 0,(function(){return q(this,(function(r){return this.language=n,this.core=new Rn(e,n),this.coreJson=e,this.dpsJson=t,this.isAggregatorLoaded=!1,this.allLegacyServicesHaveName=this.checkIfServiceNameExists(t.consentTemplates),this.isAggregatorLoaded=!1,[2]}))}))},e.prototype.initData=function(e,t,n,r,i,s,o){return void 0===t&&(t=[]),void 0===n&&(n=!0),void 0===r&&(r=!1),void 0===i&&(i=[]),void 0===s&&(s=!1),void 0===o&&(o=!1),J(this,void 0,void 0,(function(){var a,c,u,l,d,p,f=this;return q(this,(function(h){switch(h.label){case 0:if(c=(a=this).coreJson,u=a.dpsJson,l=a.legacySettings,!c||!u)return[2];switch(d=this.controllerIdInstance.value,p=(null==t?void 0:t.length)?t:this.aggregatedServices,e){case 0:return[3,1];case 2:return[3,3];case 1:return[3,5]}return[3,7];case 1:return[4,dynamicImportPolyfill("./CcpaData-d47c98d5-91f7df5a.js",import.meta.url).then((function(e){var t=new e.default(c,u,d,s);f.data=t}))];case 2:return h.sent(),[3,8];case 3:return l?[4,dynamicImportPolyfill("./TcfData-fc4f7add-2c7a09e0.js",import.meta.url).then((function(e){var t=e.default;return J(f,void 0,void 0,(function(){var e,s;return q(this,(function(a){switch(a.label){case 0:return(e=this.getDataTransferSettings())&&c.tcf2?(s=new t(l,c.tcf2,u,e,d,this.language,i,o),this.data=s,n?[4,s.init(r)]:[3,2]):[3,2];case 1:a.sent(),a.label=2;case 2:return[2]}}))}))}))]:[2];case 4:return h.sent(),[3,8];case 5:return[4,dynamicImportPolyfill("./DefaultData-b403198d-fac3d7c5.js",import.meta.url).then((function(e){var t=new e.default(u,d,p);f.data=t}))];case 6:return h.sent(),[3,8];case 7:console.error("Usercentrics: Unknown variant"),h.label=8;case 8:return this.isVariantLoaded=!0,[2]}}))}))},e.prototype.checkIfServiceNameExists=function(e){return e.every((function(e){var t;return null!=(null===(t=e._meta)||void 0===t?void 0:t.name)}))},e.prototype.initLabels=function(e,t,n){var r=this,i=r.coreJson,s=r.dpsJson,o=r.legacySettings,a=(null==n?void 0:n.length)?n:this.aggregatedServices;if(s&&this.core)if(o)switch(e){case 0:var c=new wn(o,s,t,a);this.labels=c;break;case 1:var u=new Ln(o,s,t,a);this.labels=u;break;case 2:var l=new Hn(o,s,this.getThirdPartyCount(),t,a);this.labels=l;break;default:console.error("Usercentrics: Unknown variant")}else{var d=new Dn(s,a,t),p=i?{label:"Powered by",partnerUrlLabel:Ne(i.labels.partnerPoweredByLinkText,null),urlLabel:"Usercentrics Consent Management"}:void 0;this.labels=K(K({},d),{poweredBy:p})}else console.error("Usercentrics: You have to call the init method before!")},e.prototype.initUI=function(e,t){return J(this,void 0,void 0,(function(){var n,r,i,s=this;return q(this,(function(o){switch(o.label){case 0:return r=(n=this).coreJson,i=n.legacySettings,r?2!==e?[3,2]:[4,dynamicImportPolyfill("./PrivacyButtonUI-13f104d4-a800139a.js",import.meta.url).then((function(e){var t,n=e.default,o=Ue.CAT,a=!1;i&&(o=i.secondLayer.defaultView,a=i.secondLayer.hideDataProcessingServices);var c=new n(r,{defaultView:o,hideDataProcessingServices:a}),u=r.enablePoweredBy?{partnerUrl:r.partnerPoweredByUrl||null,url:"https://www.usercentrics.com/consent-management-platform-powered-by-usercentrics/"}:null;s.ui=K(K({},c),{customCss:(null===(t=null==i?void 0:i.features)||void 0===t?void 0:t.customCss)&&i.useUnsafeCustomCss&&null!=i.stylesCss?i.stylesCss:null,poweredBy:u})}))]:[2];case 1:return o.sent(),[2];case 2:if(!i)return[2];switch(t){case 0:return[3,3];case 1:return[3,5];case 2:return[3,7]}return[3,9];case 3:return[4,dynamicImportPolyfill("./CcpaUI-12afaf40-9a125ea9.js",import.meta.url).then((function(e){var t=new e.default(r,i);s.ui=t}))];case 4:return o.sent(),[3,10];case 5:return[4,dynamicImportPolyfill("./DefaultUI-297a9aab-66552c46.js",import.meta.url).then((function(e){var t=new e.default(r,i);s.ui=t}))];case 6:return o.sent(),[3,10];case 7:return[4,dynamicImportPolyfill("./TcfUI-7444f2b7-216bf1ba.js",import.meta.url).then((function(e){var t=new e.default(r,i);s.ui=t}))];case 8:return o.sent(),[3,10];case 9:console.error("Usercentrics: Unknown variant"),o.label=10;case 10:return[2]}}))}))},e.prototype.getCcpaData=function(){return at(this.data)?this.data:null},e.prototype.getDefaultData=function(){return function(e){return null!=e&&!at(e)&&!ct(e)}(this.data)?this.data:null},e.prototype.getTcfData=function(){return ct(this.data)?this.data:null},e.prototype.getCcpaLabels=function(){return ut(this.labels)?this.labels:null},e.prototype.getDefaultLabels=function(){return dt(this.labels)?this.labels:null},e.prototype.getTcfLabels=function(){return lt(this.labels)?this.labels:null},e.prototype.getCcpaUI=function(){return ht(this.ui)?this.ui:null},e.prototype.getDefaultUI=function(){return vt(this.ui)?this.ui:null},e.prototype.getTcfUI=function(){return gt(this.ui)?this.ui:null},e.prototype.getDataExchangeSettings=function(){return this.core?this.core.dataExchangeSettings:[]},e.prototype.getCategoriesData=function(){return this.data?this.data.categories:[]},e.prototype.getCategoriesBaseData=function(){var e=this;return this.data?this.data.categories.map((function(t){return{isEssential:t.isEssential,isHidden:t.isHidden,services:e.getServicesBaseInfo().filter((function(e){return e.categorySlug===t.slug})),slug:t.slug}})):[]},e.prototype.getCategoriesLabels=function(){return this.labels&&ft(this.labels)?this.labels.categories:[]},e.prototype.getCategoriesDataAndLabels=function(){return function(e,t,n){return e.reduce((function(e,r){var i=t.find((function(e){return e.slug===r.slug})),s=st(r.services,n);return i&&e.push(K(K(K({},i),r),{services:s})),e}),[])}(this.getCategoriesData(),this.getCategoriesLabels(),this.getServicesLabels())},e.prototype.getCategoriesBasic=function(){var e=this.getCategoriesData(),t=this.getServicesLabels();return e.reduce((function(e,n){return Q(Q([],X(e),!1),[K(K({},n),{services:it(n.services,t)})],!1)}),[])},e.prototype.getCategoriesBaseInfo=function(){return this.getCategoriesDataAndLabels()},e.prototype.getCategoriesFullInfo=function(e,t){return J(this,void 0,void 0,(function(){return q(this,(function(n){switch(n.label){case 0:return this.isAggregatorLoaded?[3,2]:[4,this.extendServices(e,t)];case 1:n.sent(),n.label=2;case 2:return[2,this.getCategoriesDataAndLabels()]}}))}))},e.prototype.getDataTransferSettings=function(e){return this.core?{controllerId:this.controllerIdInstance.value,id:this.core.id,selectedLanguage:this.core.language.selected,version:e||this.core.version}:null},e.prototype.getEssentialCategories=function(){return this.getCategoriesLabels().length?this.getCategoriesDataAndLabels().reduce((function(e,t){return t.isEssential?Q(Q([],X(e),!1),[t],!1):e}),[]):this.getCategoriesBasic().filter((function(e){return e.isEssential}))},e.prototype.getEssentialCategoriesData=function(){return this.getCategoriesData().reduce((function(e,t){return t.isEssential?Q(Q([],X(e),!1),[t],!1):e}),[])},e.prototype.getNonEssentialCategories=function(){return this.getCategoriesLabels().length?this.getCategoriesDataAndLabels().reduce((function(e,t){return t.isEssential?e:Q(Q([],X(e),!1),[t],!1)}),[]):this.getCategoriesBasic().filter((function(e){return!e.isEssential}))},e.prototype.getNonEssentialCategoriesData=function(){return this.getCategoriesData().reduce((function(e,t){return t.isEssential?e:Q(Q([],X(e),!1),[t],!1)}),[])},e.prototype.getGoogleConsentMode=function(){return!!this.core&&this.core.googleConsentMode},e.prototype.getServicesLabels=function(){return this.labels?this.labels.services:[]},e.prototype.getServicesData=function(){return this.getCategoriesData().reduce((function(e,t){return Q(Q([],X(e),!1),X(t.services),!1)}),[])},e.prototype.getServicesDataAndLabels=function(){var e=this.getServicesData();return st(e,this.getServicesLabels())},e.prototype.getServicesBaseInfo=function(){var e,t;return e=this.mapBaseServices(this.getServicesData()),t=this.getServicesLabels(),e.reduce((function(e,n){var r=t.find((function(e){return e.id===n.id}));return Q(Q([],X(e),!1),[K(K(K({},n),r||rt),{id:n.id})],!1)}),[])},e.prototype.getServicesFullInfo=function(e,t){return J(this,void 0,void 0,(function(){return q(this,(function(n){switch(n.label){case 0:return this.isAggregatorLoaded?[3,2]:[4,this.extendServices(e,t)];case 1:n.sent(),n.label=2;case 2:return[2,this.getServicesDataAndLabels()]}}))}))},e.prototype.getServicesFromCategories=function(e){return e.reduce((function(e,t){return e.concat(t.services)}),[])},e.prototype.getServicesWithConsent=function(){return this.getServicesDataAndLabels().reduce((function(e,t){return t.consent.status?Q(Q([],X(e),!1),[t],!1):e}),[])},e.prototype.areAllServicesAccepted=function(){return this.getServicesData().every((function(e){return e.consent.status}))},e.prototype.areAllVendorsAndPurposesAccepted=function(){var e=this.getTcfData();return!!e&&e.areAllPurposesAccepted()&&e.areAllVendorsAccepted()},e.prototype.extendServices=function(e,t){return J(this,void 0,void 0,(function(){var n,r;return q(this,(function(i){switch(i.label){case 0:return(n=this.dpsJson)?(this.isAggregatorLoaded=!0,[4,this.fetchServices(n)]):[2];case 1:return(r=i.sent())&&0!==r.length?(this.aggregatedServices=r,this.initLabels(e,t,r),this.data&&(this.data.categories=this.data.categories.map((function(e){var t=n.categories.find((function(t){return t.categorySlug===e.slug}));return K(K({},e),{services:e.services.map((function(e){if(n.consentTemplates.find((function(t){return t.templateId===e.id}))&&t){var i=null==r?void 0:r.find((function(t){return e.id===t.templateId&&e.version===t.version})),s=new Vn(i);return K(K({},e),s)}return e}))})}))),[2]):[2]}}))}))},e.prototype.isCcpaEnabled=function(){var e;return(null===(e=this.core)||void 0===e?void 0:e.isCcpaEnabled)||!1},e.prototype.isCcpaAvailable=function(){var e;if(this.isCcpaEnabled()&&null!=(null===(e=this.coreJson)||void 0===e?void 0:e.ccpa))switch(this.coreJson.ccpa.region){case Me.US:return this.locationInstance.getIsUserInUS();case Me.US_CA_ONLY:return this.locationInstance.getIsUserInCalifornia();default:return Promise.resolve(!0)}return Promise.resolve(!1)},e.prototype.isCrossDomainEnabled=function(){var e;return(null===(e=this.core)||void 0===e?void 0:e.consentSharingIFrameIsActive)||!1},e.prototype.isTcfEnabled=function(){var e;return(null===(e=this.core)||void 0===e?void 0:e.isTcfEnabled)||!1},e.prototype.isTcfAvailable=function(){var e;return this.isTcfEnabled()&&null!=(null===(e=this.coreJson)||void 0===e?void 0:e.tcf2)},e.prototype.isTagLoggerActive=function(){var e;return(null===(e=this.core)||void 0===e?void 0:e.isTagLoggerActive)||!1},e.prototype.mergeServicesIntoExistingCategories=function(e){return this.getCategoriesDataAndLabels().map((function(t){return K(K({},t),{services:t.services.map((function(t){return e.find((function(e){return e.id===t.id}))||t}))})}))},e.prototype.mergeServicesDataIntoExistingCategories=function(e){return this.getCategoriesData().map((function(t){return K(K({},t),{services:t.services.map((function(t){return e.find((function(e){return e.id===t.id}))||t}))})}))},e.prototype.setCategories=function(e){this.data&&(this.data.categories=e)},e.prototype.setControllerId=function(e){this.data&&(this.data.controllerId=e),ct(this.data)&&this.data.updateControllerId(e)},e.prototype.updateServicesLanguage=function(e){this.data&&this.data.categories.map((function(t){return K(K({},t),{services:t.services.map((function(t){return K(K({},t),{language:K(K({},t.language),{selected:e})})}))})}))},e.prototype.shouldDenyAllExplicitlyOnInit=function(){var e;return J(this,void 0,void 0,(function(){var t;return q(this,(function(n){switch(n.label){case 0:return null!==this.denyAllExplicitlyOnInit?[2,this.denyAllExplicitlyOnInit]:(t=this,[4,this.isCcpaAvailable()]);case 1:return t.denyAllExplicitlyOnInit=n.sent()&&!(null===(e=this.getCcpaData())||void 0===e?void 0:e.isExplicitConsented)&&!0===navigator.globalPrivacyControl||!1,[2,this.denyAllExplicitlyOnInit]}}))}))},e.prototype.shouldAcceptAllImplicitlyOnInit=function(){var e,t;return J(this,void 0,void 0,(function(){var n,r,i,s,o,a;return q(this,(function(c){switch(c.label){case 0:return null!==this.acceptAllImplicitlyOnInit?[2,this.acceptAllImplicitlyOnInit]:(n=this,(s=this.botInstance.isRobot())?[3,2]:[4,this.isCcpaAvailable()]);case 1:s=c.sent()&&!navigator.globalPrivacyControl,c.label=2;case 2:return(i=s)?[3,5]:(o=null===(e=this.core)||void 0===e?void 0:e.acceptAllImplicitlyOutsideEU)?[4,this.locationInstance.getIsUserInEU()]:[3,4];case 3:o=!c.sent(),c.label=4;case 4:i=o,c.label=5;case 5:return(r=i)?[3,8]:(a=this.isTcfAvailable())?[4,null===(t=this.getTcfData())||void 0===t?void 0:t.getGdprApplies()]:[3,7];case 6:a=!c.sent(),c.label=7;case 7:r=a,c.label=8;case 8:return n.acceptAllImplicitlyOnInit=r||""!==this.apiInstance.getRulesetId()&&this.rulesetInstance.getIsUsingNoShow()&&this.rulesetInstance.getNoShow(),[2,this.acceptAllImplicitlyOnInit]}}))}))},e.prototype.shouldAcceptAllImplicitlyOnVendorAdded=function(){var e,t,n;return J(this,void 0,void 0,(function(){var r,i,s,o,a;return q(this,(function(c){switch(c.label){case 0:return(s=this.botInstance.isRobot())?[3,2]:[4,this.isCcpaAvailable()];case 1:s=c.sent()&&!(null===(e=this.getCcpaData())||void 0===e?void 0:e.isOptedOut),c.label=2;case 2:return(i=s)?[3,5]:(o=null===(t=this.core)||void 0===t?void 0:t.acceptAllImplicitlyOutsideEU)?[4,this.locationInstance.getIsUserInEU()]:[3,4];case 3:o=!c.sent(),c.label=4;case 4:i=o,c.label=5;case 5:return(r=i)?[3,8]:(a=this.isTcfAvailable())?[4,null===(n=this.getTcfData())||void 0===n?void 0:n.getGdprApplies()]:[3,7];case 6:a=!c.sent(),c.label=7;case 7:r=a,c.label=8;case 8:return[2,r||""!==this.apiInstance.getRulesetId()&&this.rulesetInstance.getIsUsingNoShow()&&this.rulesetInstance.getNoShow()]}}))}))},e.prototype.shouldShowFirstLayerOnVersionChange=function(){var e=ye.fetchSettingsVersion();if(this.core&&e&&Ae(this.core.showFirstLayerOnVersionChange)){var t=this.core.version.split("."),n=e.split(".");return Ce(this.core.showFirstLayerOnVersionChange,k.MAJOR)&&t[0]!==n[0]||Ce(this.core.showFirstLayerOnVersionChange,k.MINOR)&&t[1]!==n[1]||Ce(this.core.showFirstLayerOnVersionChange,k.PATCH)&&t[2]!==n[2]}return!1},e.prototype.getUpdatedServicesWithConsent=function(e){return this.getServicesDataAndLabels().map((function(t){if(!t.isEssential){var n=t;return n.consent.status=e===V.TRUE,n}return t}))},e.prototype.getUpdatedServicesDataWithConsent=function(e){return this.getServicesData().map((function(t){if(!t.isEssential){var n=t;return n.consent.status=e===V.TRUE,n}return t}))},e.prototype.getUpdatedServicesWithDecisions=function(e){return this.getServicesDataAndLabels().map((function(t){var n=e.find((function(e){return e.serviceId===t.id})),r=t;return r.consent.status=t.isEssential||(n?n.status:t.consent.status),r}))},e.prototype.getUpdatedServicesDataWithDecisions=function(e){return this.getServicesData().map((function(t){var n=e.find((function(e){return e.serviceId===t.id})),r=t;return r.consent.status=t.isEssential||(n?n.status:t.consent.status),r}))},e.prototype.updateDataTransferSettings=function(e){var t=e.controllerId,n=e.id,r=e.selectedLanguage,i=e.version;this.core&&this.data&&(this.data.controllerId=t,this.core.id=n,this.core.language.selected=r,this.core.version=i)},e.prototype.isTcfHistoryV2Disabled=function(){return!!ct(this.data)&&!0===this.data.tcfv2HistoryDisabled},e.prototype.getTCFPurposeOneTreatment=function(){return ct(this.data)&&this.data.purposeOneTreatment||!1},e.prototype.getTCFStackIds=function(){return ct(this.data)?this.data.stackIds:[]},e.prototype.getTCFVendorIds=function(){return ct(this.data)?this.data.vendorIds:[]},e.prototype.getTCFDisclosedVendorsSegmentString=function(){var e;return null===(e=this.getTcfData())||void 0===e?void 0:e.getTCFDisclosedVendorsSegmentString()},e.prototype.injectTCString=function(e){return J(this,void 0,void 0,(function(){var t;return q(this,(function(n){return(t=this.getTcfData())?[2,t.injectTCString(e)]:[2,!1]}))}))},e.prototype.fetchServices=function(e){return J(this,void 0,void 0,(function(){var t,n,r,i;return q(this,(function(s){switch(s.label){case 0:return(t=qt(e.consentTemplates)).length?(n=Xt.getInstance(),r=nn.getInstance(),i=[],r.isRobot()?[3,2]:[4,n.fetchAggregatedServices(t)]):[2,null];case 1:i=s.sent(),s.label=2;case 2:return[2,i]}}))}))},e.prototype.mapBaseServices=function(e){return e.map((function(e){return{categorySlug:e.categorySlug,consent:e.consent,fetchSubServices:e.fetchSubServices,id:e.id,isEssential:e.isEssential,isHidden:e.isHidden,processorId:e.processorId,subServices:e.subServices,subServicesLength:e.subServicesLength,usesThirdCountry:e.usesThirdCountry,version:e.version}}))},e.prototype.getThirdPartyCount=function(){var e=ct(this.data)?this.data.getVendorsCount():0;return this.getServicesData().length+e},e}(),$n=function(){function e(){this.restoreAction=null,this.apiInstance=Xt.getInstance(),this.controllerIdInstance=Ht.getInstance(),this.eventDispatcherInstance=un.getInstance(),this.settingsV2=jn.getInstance(),this.botInstance=nn.getInstance()}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){delete e.instance.userSessionData},e.prototype.execute=function(e,t,n,r,i,s){var o,a,c,u=this.settingsV2.getDataTransferSettings();if(u){var l=e.map((function(e){return we(u,e,n,r,e.categorySlug)})),d=Yn(t,l);if(!this.botInstance.isRobot()&&(s&&this.apiInstance.saveConsentsV1_DEPRECATED({consentString:i,dataTransferObjects:l}),null===(o=this.settingsV2.core)||void 0===o?void 0:o.consentAPIv2)){var p=Re({consentAction:n,consentString:i,dataTransferSettings:u,isAnalyticsEnabled:null===(a=this.settingsV2.core)||void 0===a?void 0:a.consentAnalytics,isCcpa:this.settingsV2.isCcpaEnabled(),isConsentXDeviceEnabled:null===(c=this.settingsV2.core)||void 0===c?void 0:c.consentXDevice,isTcf:!1,services:e});this.apiInstance.saveConsents(p)}this.settingsV2.setCategories(this.settingsV2.mergeServicesDataIntoExistingCategories(d));var f,h=this.settingsV2.getServicesDataAndLabels(),g=ye.mapSettings(u,h);ye.saveSettings(g,h),Cn.enableScriptsForServicesWithConsent(this.settingsV2.getServicesWithConsent());var v=this.settingsV2.getTcfData();v&&v.advertiserConsentModeEnabled&&(f=v.getTCFData().vendors.find((function(e){return 755===e.id}))),this.eventDispatcherInstance.dispatch(l,f,this.settingsV2.getGoogleConsentMode())}},e.prototype.getMergedServicesAndSettingsFromStorage=function(e){var t=e,n=ye.fetchSettings();if(n&&t){var r=this.settingsV2.getServicesFromCategories(this.settingsV2.getEssentialCategories()),i=this.settingsV2.getServicesFromCategories(this.settingsV2.getEssentialCategoriesData()),s=this.settingsV2.getServicesFromCategories(this.settingsV2.getNonEssentialCategories()),o=this.settingsV2.getServicesFromCategories(this.settingsV2.getNonEssentialCategoriesData()),a=this.getMergedAndUpdatedEssentialServices(r,n),c=this.getMergedAndUpdatedEssentialServices(i,n),u=this.getMergedNonEssentialServices(s,n),l=this.getMergedNonEssentialServices(o,n);return n.controllerId!==this.controllerIdInstance.value&&""===this.controllerIdInstance.value&&(this.controllerIdInstance.value=n.controllerId,t.controllerId=n.controllerId,ct(t)&&t.updateControllerId(n.controllerId)),{dataTransferSettings:this.settingsV2.getDataTransferSettings(),mergedServices:a.mergedEssentialServices.concat(u),mergedServicesData:c.mergedEssentialServices.concat(l),mergedSettingsData:t,updatedEssentialServices:a.updatedEssentialServices}}return{dataTransferSettings:null,mergedServices:[],mergedServicesData:[],mergedSettingsData:t,updatedEssentialServices:[]}},e.prototype.getLatestConsentType=function(e){return e.length>0?e[e.length-1].type:"implicit"},e.prototype.getLatestConsentAction=function(e){return e.length>0?e[e.length-1].action:"onInitialPageLoad"},e.prototype.setUserSessionData=function(e){this.userSessionData=e},e.prototype.mergeServicesAndSettings=function(e,t,n,r,i,s){var o,a,c,u=this;if(!n)return[];if(Ae(r)){var l=r.map((function(e){return we(n,e,"onEssentialChange",u.getLatestConsentType(e.consent.history),null==e?void 0:e.categorySlug)})),d=Yn(t,l),p=Wn(t,d,i);if(s&&this.apiInstance.saveConsentsV1_DEPRECATED({dataTransferObjects:l}),null===(o=this.settingsV2.core)||void 0===o?void 0:o.consentAPIv2){var f=Re({consentAction:"onEssentialChange",dataTransferSettings:n,isAnalyticsEnabled:null===(a=this.settingsV2.core)||void 0===a?void 0:a.consentAnalytics,isCcpa:this.settingsV2.isCcpaEnabled(),isConsentXDeviceEnabled:null===(c=this.settingsV2.core)||void 0===c?void 0:c.consentXDevice,isTcf:this.settingsV2.isTcfAvailable(),services:r});this.apiInstance.saveConsents(f)}this.settingsV2.setCategories(this.settingsV2.mergeServicesDataIntoExistingCategories(d)),ye.saveSettings(ye.mapSettings(n,p),p)}else this.settingsV2.setCategories(this.settingsV2.mergeServicesDataIntoExistingCategories(t)),ye.saveSettings(ye.mapSettings(n,e),e);return e.map((function(e){return we(n,e,u.getLatestConsentAction(e.consent.history),u.getLatestConsentType(e.consent.history),null==e?void 0:e.categorySlug)}))},e.prototype.restoreUserSession=function(e){var t,n,r;return J(this,void 0,void 0,(function(){var i;return q(this,(function(s){switch(s.label){case 0:return this.controllerIdInstance.value&&this.controllerIdInstance.needsSessionRestore&&(!(null===(t=this.settingsV2.core)||void 0===t?void 0:t.consentAPIv2)||(null===(n=this.settingsV2.core)||void 0===n?void 0:n.consentAPIv2)&&(null===(r=this.settingsV2.core)||void 0===r?void 0:r.consentXDevice))?[4,this.getCrossDeviceSessionData(this.controllerIdInstance.value)]:[3,2];case 1:i=s.sent(),s.label=2;case 2:if(!i&&this.userSessionData&&(i=K({},this.userSessionData)),!i&&window[ne]&&"function"==typeof window[ne].getUserSessionData)try{(null==(i=JSON.parse(window[ne].getUserSessionData(),(function(e,t){if("timestamp"===e){var n=t.toString();return-1!==n.indexOf(".")?1e3*Number(n):Number(n)}return t})))?void 0:i.consents)&&(null==i?void 0:i.consents.length)&&!i.consents.every((function(e){return e.action}))&&(i.consents=i.consents.map((function(e){return K(K({},e),{action:"onMobileSessionRestore"})})),this.restoreAction="onMobileSessionRestore")}catch(e){}return i||!Se.isCrossDomainAvailable()?[3,4]:[4,this.getCrossDomainSessionData()];case 3:i=s.sent(),s.label=4;case 4:return i&&i.controllerId?[2,this.restoreData(i,e)]:(this.controllerIdInstance.needsSessionRestore=!1,[2,!1])}}))}))},e.prototype.getCrossDomainSessionData=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){switch(e.label){case 0:return[4,Se.getCrossDomainSessionData().catch((function(){return console.warn(W.CROSS_DOMAIN_DATA_NOT_AVAILABLE),{}}))];case 1:return[2,e.sent()]}}))}))},e.prototype.getCrossDeviceSessionData=function(e){var t,n;return J(this,void 0,void 0,(function(){var r,i,s,o,a,c,u,l,d,p,f;return q(this,(function(h){switch(h.label){case 0:return r=["dWLDa0s-m","VkvM9IcSA","Zdgjo9gQh","r2tAWzO7","GVl-ixMH"],i=this.apiInstance.getSettingsId(),s=this.settingsV2.isTcfAvailable(),[4,this.settingsV2.isCcpaAvailable()];case 1:return o=h.sent(),a=null===(t=this.settingsV2.core)||void 0===t?void 0:t.consentAPIv2,c=[],u=null,l=null,a?[4,this.apiInstance.fetchUserConsentsV2(s,o).catch((function(){return console.warn(Y.CROSS_DEVICE_DATA_NOT_AVAILABLE),null}))]:[3,3];case 2:return d=h.sent(),c=null!==(n=null==d?void 0:d.consents)&&void 0!==n?n:[],s&&(-1===r.indexOf(i)&&this.settingsV2.isTcfHistoryV2Disabled()||(u=null==d?void 0:d.tcf)),o&&(l=null==d?void 0:d.ccpa),[3,8];case 3:return[4,this.apiInstance.fetchUserConsents().catch((function(){return console.warn(Y.CROSS_DEVICE_DATA_NOT_AVAILABLE),[]}))];case 4:return p=h.sent(),c=p,s?-1===r.indexOf(i)?[3,6]:[4,this.apiInstance.fetchUserTcfData().catch((function(){return console.warn(Y.CROSS_DEVICE_TCF_DATA_NOT_AVAILABLE),null}))]:[3,8];case 5:return u=h.sent(),[3,8];case 6:return this.settingsV2.isTcfHistoryV2Disabled()?[3,8]:[4,this.apiInstance.fetchUserTcfDataV2().catch((function(){return console.warn(Y.CROSS_DEVICE_TCF_DATA_NOT_AVAILABLE),null}))];case 7:u=h.sent(),h.label=8;case 8:return s&&!u&&Se.isCrossDomainAvailable()?[4,this.getCrossDomainSessionData()]:[3,10];case 9:(f=h.sent())&&f.tcf&&f.controllerId===e&&(u=f.tcf),h.label=10;case 10:return[2,K(K({consents:c,controllerId:e,language:this.apiInstance.getJsonFileLanguage()},null!==u&&{tcf:u}),null!==l&&{ccpa:l})]}}))}))},e.prototype.restoreData=function(e,t){return J(this,void 0,void 0,(function(){var n,r,i,s,o,a,c,u,l,d,p,f,h,g,v,S,E,_,m=this;return q(this,(function(I){switch(I.label){case 0:return n=e.controllerId,r=e.consents,i=e.tcf,s=e.ccpa,o=this.settingsV2.core,a=this.getDataFacadeServices(t),c=ye.fetchControllerId(),this.controllerIdInstance.value=n,u=ye.fetchTCFData(),l=!1,!n||!Ae(r)&&a.length?[3,5]:(d=Kn(r),p=ye.fetchServices(),"onSessionRestored"===e.consents[0].action&&n===c&&p.length===d.length&&p[0].history.length>0&&p[0].history[0].action&&"onSessionRestored"===p[0].history[0].action?[3,5]:[3,1]);case 1:return!Ae(d)&&a.length?[3,5]:(f=[],h=[],d.forEach((function(e){var t=a.findIndex((function(t){return t.id===e.templateId}));if(t>-1){var n=a[t],r=n;r.consent.status=e.status;var i=h.findIndex((function(e){return e.id===n.id}));-1===i?h.push(r):h[i]=r,a[t]=r;var s=m.settingsV2.getDataTransferSettings(e.settingsVersion);s&&f.push(we(s,r,e.action,e.updatedBy,n.categorySlug,{timestamp:"string"==typeof e.timestamp?m.resolveTimestamp(e.timestamp):e.timestamp}))}})),this.settingsV2.data&&this.settingsV2.setControllerId(n),g=void 0,v=void 0,o&&d.length&&(v=De(d.map((function(e){return e.settingsVersion}))).sort(ze)).length&&(g=v[v.length-1]),S=Yn(a,f),(E=this.settingsV2.getDataTransferSettings(g))?c&&n&&n!==c?[4,ye.clearAll()]:[3,3]:[3,4]);case 2:I.sent(),I.label=3;case 3:ye.saveSettings(ye.mapSettings(E,S),S),I.label=4;case 4:Ae(d)&&ye.setUserActionPerformed(!0),l=!0,I.label=5;case 5:return i&&i.tcString&&(i.tcString!==u.tcString||i.acString!==u.acString)&&(ye.saveTCFData(i),a.length||ye.setUserActionPerformed(!0)),_=this.settingsV2.getCcpaData(),s&&s.ccpaString&&_&&(_.setIsOptedOut(s.ccpaString),ye.setCcpaString(s.ccpaString),s.timestamp?ye.setCcpaTimeStamp(s):ye.clearCcpaData(),ye.setUserActionPerformed(!0)),[2,l]}}))}))},e.prototype.getDataFacadeServices=function(e){var t=this.settingsV2.checkIfServiceNameExists,n=e.categories,r=e.consentTemplates;if(r.length>0&&t(r))return r.map((function(e){var t,r=n.find((function(t){return t.categorySlug===e.categorySlug}));return{categorySlug:e.categorySlug,consent:bn(e,r),id:e.templateId,name:(null===(t=e._meta)||void 0===t?void 0:t.name)||"",processorId:"".concat(Ut(Bt())),version:e.version}}));var i=this.settingsV2.getServicesDataAndLabels();return i.length>0?i.map((function(e){return{categorySlug:e.categorySlug,consent:e.consent,id:e.id,name:e.name,processorId:e.processorId,version:e.version}})):[]},e.prototype.getMergedAndUpdatedEssentialServices=function(e,t){var n=this,r=[],i=e.map((function(e){var i,s,o=null===(i=t.services)||void 0===i?void 0:i.find((function(t){return t.id===e.id}));if(o){var a=e;return a.consent.history=o.history,a.consent.status=!0,a.processorId=o.processorId,a.categorySlug=(null===(s=n.settingsV2.getCategoriesBaseData().find((function(e){return e.services.some((function(e){return e.id===a.id}))})))||void 0===s?void 0:s.slug)||"",o.status||r.push(a),a}return e}));return{mergedEssentialServices:i,updatedEssentialServices:r}},e.prototype.getMergedNonEssentialServices=function(e,t){return e.map((function(e){var n,r,i=null===(n=t.services)||void 0===n?void 0:n.find((function(t){return t.id===e.id}));if(i)return(s=e).consent.history=i.history,s.consent.status=i.status,s.processorId=i.processorId,s;if(0===e.consent.history.length){var s=e,o=null===(r=t.services)||void 0===r?void 0:r.find((function(e){return e.history.length>0}));return s.consent.history=[{action:"onInitialPageLoad",language:e.language?e.language.selected:pn,status:e.consent.status,timestamp:(new Date).getTime(),type:"implicit",versions:o&&o.history.length>0?o.history[0].versions:{application:"",service:e.version,settings:""}}],s}return e}))},e.prototype.resolveTimestamp=function(e){return 10===e.length?1e3*parseInt(e,10):parseInt(e,10)},e}(),Yn=function(e,t){return e.map((function(e){var n=t.filter((function(t){return t.service.id===e.id}));if(Ae(n)){var r=e.consent.history,i=r.length+n.length,s=i<=3?r:r.slice(i-3),o=e;return o.consent.history=Q(Q([],X(s),!1),X(n.map((function(e){return zn(e)}))),!1),o}return e}))},Wn=function(e,t,n){return e.reduce((function(e,r){var i=t.find((function(e){return e.id===r.id})),s=n.find((function(e){return e.id===r.id}));return i&&s?Q(Q([],X(e),!1),[{categorySlug:r.categorySlug,consent:i.consent,id:r.id,language:r.language,name:s.name,processorId:r.processorId,version:r.version}],!1):Q([],X(e),!1)}),[])},zn=function(e){return{action:e.consent.action,language:e.settings.language,status:e.consent.status,timestamp:e.timestamp,type:e.consent.type,versions:{application:e.applicationVersion,service:e.service.version,settings:e.settings.version}}},Kn=function(e){return e.filter((function(e){return!yt.includes(e.action)}))},Jn=p((function e(t,n,r,i){l(this,e),h(this,"eventName",void 0),h(this,"listenerId",void 0),h(this,"data",void 0),h(this,"pingData",void 0),this.eventName=t,this.listenerId=n,this.data=r,this.pingData=i})),qn=p((function e(t){l(this,e),h(this,"gppVersion",void 0),h(this,"cmpStatus",void 0),h(this,"cmpDisplayStatus",void 0),h(this,"signalStatus",void 0),h(this,"supportedAPIs",void 0),h(this,"cmpId",void 0),h(this,"sectionList",void 0),h(this,"applicableSections",void 0),h(this,"gppString",void 0),h(this,"parsedSections",void 0),this.gppVersion=t.gppVersion,this.cmpStatus=t.cmpStatus,this.cmpDisplayStatus=t.cmpDisplayStatus,this.signalStatus=t.signalStatus,this.supportedAPIs=t.supportedAPIs,this.cmpId=t.cmpId,this.sectionList=t.gppModel.getSectionIds(),this.applicableSections=t.applicableSections,this.gppString=t.gppModel.encode(),this.parsedSections=t.gppModel.toObject()})),Xn=p((function e(t,n,r){l(this,e),h(this,"callback",void 0),h(this,"parameter",void 0),h(this,"success",!0),h(this,"cmpApiContext",void 0),this.cmpApiContext=t,Object.assign(this,{callback:n,parameter:r})}),[{key:"execute",value:function(){try{return this.respond()}catch(e){return this.invokeCallback(null),null}}},{key:"invokeCallback",value:function(e){var t=null!==e;this.callback&&this.callback(e,t)}}]),Qn=function(){function e(){return l(this,e),u(this,e,arguments)}return S(e,Xn),p(e,[{key:"respond",value:function(){var e=this.cmpApiContext.eventQueue.add({callback:this.callback,parameter:this.parameter}),t=new Jn("listenerRegistered",e,!0,new qn(this.cmpApiContext));this.invokeCallback(t)}}])}(),Zn=function(){function e(){return l(this,e),u(this,e,arguments)}return S(e,Xn),p(e,[{key:"respond",value:function(){var e=new qn(this.cmpApiContext);this.invokeCallback(e)}}])}(),er=function(){function e(){return l(this,e),u(this,e,arguments)}return S(e,Xn),p(e,[{key:"respond",value:function(){if(!this.parameter||0===this.parameter.length)throw new Error("<section>.<field> parameter required");var e=this.parameter.split(".");if(2!=e.length)throw new Error("Field name must be in the format <section>.<fieldName>");var t=e[0],n=e[1],r=this.cmpApiContext.gppModel.getFieldValue(t,n);this.invokeCallback(r)}}])}(),tr=function(){function e(){return l(this,e),u(this,e,arguments)}return S(e,Xn),p(e,[{key:"respond",value:function(){if(!this.parameter||0===this.parameter.length)throw new Error("<section> parameter required");var e=null;this.cmpApiContext.gppModel.hasSection(this.parameter)&&(e=this.cmpApiContext.gppModel.getSection(this.parameter)),this.invokeCallback(e)}}])}(),nr=function(){function e(){return l(this,e),u(this,e,arguments)}return S(e,Xn),p(e,[{key:"respond",value:function(){if(!this.parameter||0===this.parameter.length)throw new Error("<section>[.version] parameter required");var e=this.cmpApiContext.gppModel.hasSection(this.parameter);this.invokeCallback(e)}}])}();!function(e){e.ADD_EVENT_LISTENER="addEventListener",e.GET_FIELD="getField",e.GET_SECTION="getSection",e.HAS_SECTION="hasSection",e.PING="ping",e.REMOVE_EVENT_LISTENER="removeEventListener"}(In||(In={}));var rr,ir,sr,or,ar,cr,ur=function(){function e(){return l(this,e),u(this,e,arguments)}return S(e,Xn),p(e,[{key:"respond",value:function(){var e=this.parameter,t=this.cmpApiContext.eventQueue.remove(e),n=new Jn("listenerRemoved",e,t,new qn(this.cmpApiContext));this.invokeCallback(n)}}])}();rr=In.ADD_EVENT_LISTENER,ir=In.GET_FIELD,sr=In.GET_SECTION,or=In.HAS_SECTION,ar=In.PING,cr=In.REMOVE_EVENT_LISTENER;var lr,dr,pr,fr,hr=p((function e(){l(this,e)}));h(hr,rr,Qn),h(hr,ir,er),h(hr,sr,tr),h(hr,or,nr),h(hr,ar,Zn),h(hr,cr,ur),function(e){e.STUB="stub",e.LOADING="loading",e.LOADED="loaded",e.ERROR="error"}(lr||(lr={})),function(e){e.VISIBLE="visible",e.HIDDEN="hidden",e.DISABLED="disabled"}(dr||(dr={})),function(e){e.GPP_LOADED="gpploaded",e.CMP_UI_SHOWN="cmpuishown",e.USER_ACTION_COMPLETE="useractioncomplete"}(pr||(pr={})),function(e){e.NOT_READY="not ready",e.READY="ready"}(fr||(fr={}));var gr=p((function e(t,n){if(l(this,e),h(this,"callQueue",void 0),h(this,"customCommands",void 0),h(this,"cmpApiContext",void 0),this.cmpApiContext=t,n){var r=In.ADD_EVENT_LISTENER;if(null!=n&&n[r])throw new Error("Built-In Custom Commmand for ".concat(r," not allowed"));if(r=In.REMOVE_EVENT_LISTENER,null!=n&&n[r])throw new Error("Built-In Custom Commmand for ".concat(r," not allowed"));this.customCommands=n}try{this.callQueue=window.__gpp()||[]}catch(e){this.callQueue=[]}finally{window.__gpp=this.apiCall.bind(this),this.purgeQueuedCalls()}}),[{key:"apiCall",value:function(e,t,n,r){if("string"!=typeof e)t(null,!1);else{if(t&&"function"!=typeof t)throw new Error("invalid callback function");this.isCustomCommand(e)?this.customCommands[e](t,n):this.isBuiltInCommand(e)?new hr[e](this.cmpApiContext,t,n).execute():t&&t(null,!1)}}},{key:"purgeQueuedCalls",value:function(){var e=this.callQueue;this.callQueue=[],e.forEach((function(e){var t;(t=window).__gpp.apply(t,T(e))}))}},{key:"isCustomCommand",value:function(e){return this.customCommands&&"function"==typeof this.customCommands[e]}},{key:"isBuiltInCommand",value:function(e){return void 0!==hr[e]}}]),vr=p((function e(t){l(this,e),h(this,"eventQueue",new Map),h(this,"queueNumber",1e3),h(this,"cmpApiContext",void 0),this.cmpApiContext=t;try{for(var n=window.__gpp("events")||[],r=0;r<n.length;r++){var i=n[r];this.eventQueue.set(i.id,{callback:i.callback,parameter:i.parameter})}}catch(e){console.log(e)}}),[{key:"add",value:function(e){return this.eventQueue.set(this.queueNumber,e),this.queueNumber++}},{key:"get",value:function(e){return this.eventQueue.get(e)}},{key:"remove",value:function(e){return this.eventQueue.delete(e)}},{key:"exec",value:function(e,t){var n=this;this.eventQueue.forEach((function(r,i){var s=new Jn(e,i,t,new qn(n.cmpApiContext));r.callback(s,!0)}))}},{key:"clear",value:function(){this.queueNumber=1e3,this.eventQueue.clear()}},{key:"size",get:function(){return this.eventQueue.size}}]),Sr=function(){function e(t){var n;return l(this,e),(n=u(this,e,[t])).name="DecodingError",n}return S(e,N(Error)),p(e)}(),Er=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){var n=[];if(e>=1)for(n.push(1);e>=2*n[0];)n.unshift(2*n[0]);for(var r="",i=0;i<n.length;i++){var s=n[i];e>=s?(r+="1",e-=s):r+="0"}for(;r.length<t;)r="0"+r;return r}},{key:"decode",value:function(e){if(!/^[0-1]*$/.test(e))throw new Sr("Undecodable FixedInteger '"+e+"'");for(var t=0,n=[],r=0;r<e.length;r++)n[e.length-(r+1)]=0===r?1:2*n[e.length-r];for(var i=0;i<e.length;i++)"1"===e.charAt(i)&&(t+=n[i]);return t}}]),_r=function(){function e(t){var n;return l(this,e),(n=u(this,e,[t])).name="EncodingError",n}return S(e,N(Error)),p(e)}(),mr=function(){function e(){l(this,e)}return p(e,[{key:"encode",value:function(t){if(!/^[0-1]*$/.test(t))throw new _r("Unencodable Base64Url '"+t+"'");t=this.pad(t);for(var n="",r=0;r<=t.length-6;){var i=t.substring(r,r+6);try{var s=Er.decode(i);n+=e.DICT.charAt(s),r+=6}catch(e){throw new _r("Unencodable Base64Url '"+t+"'")}}return n}},{key:"decode",value:function(t){if(!/^[A-Za-z0-9\-_]*$/.test(t))throw new Sr("Undecodable Base64URL string");for(var n="",r=0;r<t.length;r++){var i=t.charAt(r),s=e.REVERSE_DICT.get(i);n+=Er.encode(s,6)}return n}}])}();h(mr,"DICT","ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"),h(mr,"REVERSE_DICT",new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]]));var Ir,yr=function(){function e(){return l(this,e),u(this,e,arguments)}return S(e,mr),p(e,[{key:"pad",value:function(e){for(;e.length%8>0;)e+="0";for(;e.length%6>0;)e+="0";return e}}])}(),Cr=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){var t=[];if(e>=1&&(t.push(1),e>=2)){t.push(2);for(var n=2;e>=t[n-1]+t[n-2];)t.push(t[n-1]+t[n-2]),n++}for(var r="1",i=t.length-1;i>=0;i--){var s=t[i];e>=s?(r="1"+r,e-=s):r="0"+r}return r}},{key:"decode",value:function(e){if(!/^[0-1]*$/.test(e)||e.length<2||e.indexOf("11")!==e.length-2)throw new Sr("Undecodable FibonacciInteger '"+e+"'");for(var t=0,n=[],r=0;r<e.length-1;r++)0===r?n.push(1):1===r?n.push(2):n.push(n[r-1]+n[r-2]);for(var i=0;i<e.length-1;i++)"1"===e.charAt(i)&&(t+=n[i]);return t}}]),Tr=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){if(!0===e)return"1";if(!1===e)return"0";throw new _r("Unencodable Boolean '"+e+"'")}},{key:"decode",value:function(e){if("1"===e)return!0;if("0"===e)return!1;throw new Sr("Undecodable Boolean '"+e+"'")}}]),br=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){e=e.sort((function(e,t){return e-t}));for(var t=[],n=0,r=0;r<e.length;){for(var i=r;i<e.length-1&&e[i]+1===e[i+1];)i++;t.push(e.slice(r,i+1)),r=i+1}for(var s=Er.encode(t.length,12),o=0;o<t.length;o++)if(1==t[o].length){var a=t[o][0]-n;n=t[o][0],s+="0"+Cr.encode(a)}else{var c=t[o][0]-n;n=t[o][0];var u=t[o][t[o].length-1]-n;n=t[o][t[o].length-1],s+="1"+Cr.encode(c)+Cr.encode(u)}return s}},{key:"decode",value:function(e){if(!/^[0-1]*$/.test(e)||e.length<12)throw new Sr("Undecodable FibonacciIntegerRange '"+e+"'");for(var t=[],n=Er.decode(e.substring(0,12)),r=0,i=12,s=0;s<n;s++){var o=Tr.decode(e.substring(i,i+1));if(i++,!0===o){var a=e.indexOf("11",i),c=Cr.decode(e.substring(i,a+2))+r;r=c,i=a+2,a=e.indexOf("11",i);var u=Cr.decode(e.substring(i,a+2))+r;r=u,i=a+2;for(var l=c;l<=u;l++)t.push(l)}else{var d=e.indexOf("11",i),p=Cr.decode(e.substring(i,d+2))+r;r=p,t.push(p),i=d+2}}return t}}]),Ar=p((function e(){l(this,e),h(this,"value",void 0)}),[{key:"hasValue",value:function(){return void 0!==this.value&&null!==this.value}},{key:"getValue",value:function(){return this.value}},{key:"setValue",value:function(e){this.value=e}}]),Or=function(){function e(t){var n;return l(this,e),(n=u(this,e)).setValue(t),n}return S(e,Ar),p(e,[{key:"encode",value:function(){return br.encode(this.value)}},{key:"decode",value:function(e){this.value=br.decode(e)}},{key:"substring",value:function(e,t){for(var n=Er.decode(e.substring(t,t+12)),r=t+12,i=0;i<n;i++)r="1"===e.charAt(r)?e.indexOf("11",e.indexOf("11",r+1)+2)+2:e.indexOf("11",r+1)+2;return e.substring(t,r)}},{key:"getValue",value:function(){return T(C(e,"getValue",this,3)([]))}},{key:"setValue",value:function(t){C(e,"setValue",this,3)([Array.from(new Set(t)).sort((function(e,t){return e-t}))])}}])}(),Nr=function(){function e(t,n){var r;return l(this,e),h(r=u(this,e),"bitStringLength",void 0),r.bitStringLength=t,r.setValue(n),r}return S(e,Ar),p(e,[{key:"encode",value:function(){return Er.encode(this.value,this.bitStringLength)}},{key:"decode",value:function(e){this.value=Er.decode(e)}},{key:"substring",value:function(e,t){return e.substring(t,t+this.bitStringLength)}}])}();!function(e){e.ID="Id",e.VERSION="Version",e.SECTION_IDS="SectionIds"}(Ir||(Ir={}));var wr=p((function e(t,n){l(this,e),h(this,"fields",void 0),h(this,"fieldOrder",void 0),this.fields=t,this.fieldOrder=n}),[{key:"hasField",value:function(e){return this.fields.has(e)}},{key:"getFieldValue",value:function(e){return this.fields.has(e)?this.fields.get(e).getValue():null}},{key:"setFieldValue",value:function(e,t){if(!this.fields.has(e))throw new Error(e+" not found");this.fields.get(e).setValue(t)}},{key:"getFieldOrder",value:function(){return this.fieldOrder}},{key:"encodeToBitString",value:function(){for(var e="",t=0;t<this.fieldOrder.length;t++){var n=this.fieldOrder[t];if(!this.fields.has(n))throw new Error("Field not found: '"+n+"'");e+=this.fields.get(n).encode()}return e}},{key:"decodeFromBitString",value:function(e){for(var t=0,n=0;n<this.fieldOrder.length;n++){var r=this.fieldOrder[n];if(!this.fields.has(r))throw new Error("Field not found: '"+r+"'");var i=this.fields.get(r),s=i.substring(e,t);i.decode(s),t+=s.length}}},{key:"toObj",value:function(){for(var e={},t=0;t<this.fieldOrder.length;t++){var n=this.fieldOrder[t];if(this.fields.has(n)){var r=this.fields.get(n).getValue();e[n]=r}}return e}}]),Rr=function(){function e(t){var n;l(this,e);var r=new Map;return r.set(Ir.ID.toString(),new Nr(6,e.ID)),r.set(Ir.VERSION.toString(),new Nr(6,e.VERSION)),r.set(Ir.SECTION_IDS.toString(),new Or([])),h(n=u(this,e,[r,[Ir.ID.toString(),Ir.VERSION.toString(),Ir.SECTION_IDS.toString()]]),"base64UrlEncoder",new yr),t&&t.length>0&&n.decode(t),n}return S(e,wr),p(e,[{key:"encode",value:function(){var e=this.encodeToBitString();return this.base64UrlEncoder.encode(e)}},{key:"decode",value:function(e){var t=this.base64UrlEncoder.decode(e);this.decodeFromBitString(t)}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(Rr,"ID",3),h(Rr,"VERSION",1),h(Rr,"NAME","header");var Lr,Dr=function(){function e(t){var n;return l(this,e),(n=u(this,e,[t])).name="InvalidFieldError",n}return S(e,N(Error)),p(e)}(),Pr=function(){function e(t){var n;return l(this,e),(n=u(this,e,[t])).name="LazyDecodingError",n}return S(e,Sr),p(e)}(),Vr=function(){function e(t){var n;return l(this,e),(n=u(this,e)).setValue(t),n}return S(e,Ar),p(e,[{key:"encode",value:function(){return Tr.encode(this.value)}},{key:"decode",value:function(e){this.value=Tr.decode(e)}},{key:"substring",value:function(e,t){return e.substring(t,t+1)}}])}(),Ur=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){return e?Er.encode(Math.round(e.getTime()/100),36):Er.encode(0,36)}},{key:"decode",value:function(e){if(!/^[0-1]*$/.test(e)||36!==e.length)throw new Sr("Undecodable Datetime '"+e+"'");return new Date(100*Er.decode(e))}}]),kr=function(){function e(t){var n;return l(this,e),(n=u(this,e)).setValue(t),n}return S(e,Ar),p(e,[{key:"encode",value:function(){return Ur.encode(this.value)}},{key:"decode",value:function(e){this.value=Ur.decode(e)}},{key:"substring",value:function(e,t){return e.substring(t,t+36)}}])}(),Mr=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){for(var n="",r=0;r<e.length;r++)n+=Tr.encode(e[r]);for(;n.length<t;)n+="0";return n}},{key:"decode",value:function(e){if(!/^[0-1]*$/.test(e))throw new Sr("Undecodable FixedBitfield '"+e+"'");for(var t=[],n=0;n<e.length;n++)t.push(Tr.decode(e.substring(n,n+1)));return t}}]),Fr=function(){function e(t,n){var r;return l(this,e),h(r=u(this,e),"getLength",void 0),r.getLength=t,r.setValue(n),r}return S(e,Ar),p(e,[{key:"encode",value:function(){return Mr.encode(this.value,this.getLength())}},{key:"decode",value:function(e){this.value=Mr.decode(e)}},{key:"substring",value:function(e,t){return e.substring(t,t+this.getLength())}},{key:"getValue",value:function(){return T(C(e,"getValue",this,3)([]))}},{key:"setValue",value:function(t){for(var n=this.getLength(),r=T(t),i=r.length;i<n;i++)r.push(!1);r.length>n&&(r=r.slice(0,n)),C(e,"setValue",this,3)([T(r)])}}])}(),xr=function(){function e(t){var n;return l(this,e),h(n=u(this,e),"numElements",void 0),n.numElements=t.length,n.setValue(t),n}return S(e,Ar),p(e,[{key:"encode",value:function(){return Mr.encode(this.value,this.numElements)}},{key:"decode",value:function(e){this.value=Mr.decode(e)}},{key:"substring",value:function(e,t){return e.substring(t,t+this.numElements)}},{key:"getValue",value:function(){return T(C(e,"getValue",this,3)([]))}},{key:"setValue",value:function(t){for(var n=T(t),r=n.length;r<this.numElements;r++)n.push(!1);n.length>this.numElements&&(n=n.slice(0,this.numElements)),C(e,"setValue",this,3)([n])}}])}(),Gr=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){for(;e.length<t;)e+=" ";for(var n="",r=0;r<e.length;r++){var i=e.charCodeAt(r);if(32===i)n+=Er.encode(63,6);else{if(!(i>=65))throw new _r("Unencodable FixedString '"+e+"'");n+=Er.encode(e.charCodeAt(r)-65,6)}}return n}},{key:"decode",value:function(e){if(!/^[0-1]*$/.test(e)||e.length%6!=0)throw new Sr("Undecodable FixedString '"+e+"'");for(var t="",n=0;n<e.length;n+=6){var r=Er.decode(e.substring(n,n+6));t+=63===r?" ":String.fromCharCode(r+65)}return t.trim()}}]),Br=function(){function e(t,n){var r;return l(this,e),h(r=u(this,e),"stringLength",void 0),r.stringLength=t,r.setValue(n),r}return S(e,Ar),p(e,[{key:"encode",value:function(){return Gr.encode(this.value,this.stringLength)}},{key:"decode",value:function(e){this.value=Gr.decode(e)}},{key:"substring",value:function(e,t){return e.substring(t,t+6*this.stringLength)}}])}(),Hr=p((function e(t,n){l(this,e),h(this,"fields",void 0),h(this,"segments",void 0),this.fields=t,this.segments=n}),[{key:"hasField",value:function(e){return this.fields.has(e)}},{key:"getFieldValue",value:function(e){return this.fields.has(e)?this.fields.get(e).getValue():null}},{key:"setFieldValue",value:function(e,t){if(!this.fields.has(e))throw new Error(e+" not found");this.fields.get(e).setValue(t)}},{key:"getSegments",value:function(){return this.segments}},{key:"encodeSegmentsToBitStrings",value:function(){for(var e=[],t=0;t<this.segments.length;t++){for(var n="",r=0;r<this.segments[t].length;r++){var i=this.segments[t][r];if(!this.fields.has(i))throw new Error("Field not found: '"+i+"'");try{n+=this.fields.get(i).encode()}catch(e){throw new Error("Unable to encode "+i)}}e.push(n)}return e}},{key:"decodeSegmentsFromBitStrings",value:function(e){for(var t=0;t<this.segments.length&&t<e.length;t++){var n=e[t];if(n&&n.length>0)for(var r=0,i=0;i<this.segments[t].length;i++){var s=this.segments[t][i];if(!this.fields.has(s))throw new Error("Field not found: '"+s+"'");try{var o=this.fields.get(s),a=o.substring(n,r);o.decode(a),r+=a.length}catch(e){throw new Error("Unable to decode "+s)}}}}},{key:"toObj",value:function(){for(var e={},t=0;t<this.segments.length;t++)for(var n=0;n<this.segments[t].length;n++){var r=this.segments[t][n];if(this.fields.has(r)){var i=this.fields.get(r).getValue();e[r]=i}}return e}}]),jr=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){e.sort((function(e,t){return e-t}));for(var t=[],n=0;n<e.length;){for(var r=n;r<e.length-1&&e[r]+1===e[r+1];)r++;t.push(e.slice(n,r+1)),n=r+1}for(var i=Er.encode(t.length,12),s=0;s<t.length;s++)1===t[s].length?i+="0"+Er.encode(t[s][0],16):i+="1"+Er.encode(t[s][0],16)+Er.encode(t[s][t[s].length-1],16);return i}},{key:"decode",value:function(e){if(!/^[0-1]*$/.test(e)||e.length<12)throw new Sr("Undecodable FixedIntegerRange '"+e+"'");for(var t=[],n=Er.decode(e.substring(0,12)),r=12,i=0;i<n;i++){var s=Tr.decode(e.substring(r,r+1));if(r++,!0===s){var o=Er.decode(e.substring(r,r+16));r+=16;var a=Er.decode(e.substring(r,r+16));r+=16;for(var c=o;c<=a;c++)t.push(c)}else{var u=Er.decode(e.substring(r,r+16));t.push(u),r+=16}}return t}}]),$r=function(){function e(t){var n;return l(this,e),(n=u(this,e)).setValue(t),n}return S(e,Ar),p(e,[{key:"encode",value:function(){return jr.encode(this.value)}},{key:"decode",value:function(e){this.value=jr.decode(e)}},{key:"substring",value:function(e,t){for(var n=Er.decode(e.substring(t,t+12)),r=t+12,i=0;i<n;i++)"1"===e.charAt(r)?r+=33:r+=17;return e.substring(t,r)}},{key:"getValue",value:function(){return T(C(e,"getValue",this,3)([]))}},{key:"setValue",value:function(t){C(e,"setValue",this,3)([Array.from(new Set(t)).sort((function(e,t){return e-t}))])}}])}(),Yr=function(){function e(t){var n;return l(this,e),(n=u(this,e)).setValue(t),n}return S(e,Ar),p(e,[{key:"encode",value:function(){var e=this.value.length>0?this.value[this.value.length-1]:0,t=jr.encode(this.value),n=e;if(t.length<=n)return Er.encode(e,16)+"1"+t;for(var r=[],i=0,s=0;s<e;s++)s===this.value[i]-1?(r[s]=!0,i++):r[s]=!1;return Er.encode(e,16)+"0"+Mr.encode(r,n)}},{key:"decode",value:function(e){if("1"===e.charAt(16))this.value=jr.decode(e.substring(17));else{for(var t=[],n=Mr.decode(e.substring(17)),r=0;r<n.length;r++)!0===n[r]&&t.push(r+1);this.value=t}}},{key:"substring",value:function(e,t){var n=Er.decode(e.substring(t,t+16));return"1"===e.charAt(t+16)?e.substring(t,t+17)+new $r([]).substring(e,t+17):e.substring(t,t+17+n)}},{key:"getValue",value:function(){return T(C(e,"getValue",this,3)([]))}},{key:"setValue",value:function(t){C(e,"setValue",this,3)([Array.from(new Set(t)).sort((function(e,t){return e-t}))])}}])}();!function(e){e.VERSION="Version",e.CREATED="Created",e.LAST_UPDATED="LastUpdated",e.CMP_ID="CmpId",e.CMP_VERSION="CmpVersion",e.CONSENT_SCREEN="ConsentScreen",e.CONSENT_LANGUAGE="ConsentLanguage",e.VENDOR_LIST_VERSION="VendorListVersion",e.POLICY_VERSION="PolicyVersion",e.IS_SERVICE_SPECIFIC="IsServiceSpecific",e.USE_NON_STANDARD_STACKS="UseNonStandardStacks",e.SPECIAL_FEATURE_OPTINS="SpecialFeatureOptins",e.PURPOSE_CONSENTS="PurposeConsents",e.PURPOSE_LEGITIMATE_INTERESTS="PurposeLegitimateInterests",e.PURPOSE_ONE_TREATMENT="PurposeOneTreatment",e.PUBLISHER_COUNTRY_CODE="PublisherCountryCode",e.VENDOR_CONSENTS="VendorConsents",e.VENDOR_LEGITIMATE_INTERESTS="VendorLegitimateInterests",e.PUBLISHER_RESTRICTIONS="PublisherRestrictions",e.PUBLISHER_PURPOSES_SEGMENT_TYPE="PublisherPurposesSegmentType",e.PUBLISHER_CONSENTS="PublisherConsents",e.PUBLISHER_LEGITIMATE_INTERESTS="PublisherLegitimateInterests",e.NUM_CUSTOM_PURPOSES="NumCustomPurposes",e.PUBLISHER_CUSTOM_CONSENTS="PublisherCustomConsents",e.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS="PublisherCustomLegitimateInterests",e.VENDORS_ALLOWED_SEGMENT_TYPE="VendorsAllowedSegmentType",e.VENDORS_ALLOWED="VendorsAllowed",e.VENDORS_DISCLOSED_SEGMENT_TYPE="VendorsDisclosedSegmentType",e.VENDORS_DISCLOSED="VendorsDisclosed"}(Lr||(Lr={}));var Wr,zr=function(){function e(){return l(this,e),u(this,e,arguments)}return S(e,mr),p(e,[{key:"pad",value:function(e){for(;e.length%24>0;)e+="0";return e}}])}(),Kr=function(){function e(t){var n;l(this,e);var r=new Map,i=new Date;r.set(Lr.VERSION.toString(),new Nr(6,e.VERSION)),r.set(Lr.CREATED.toString(),new kr(i)),r.set(Lr.LAST_UPDATED.toString(),new kr(i)),r.set(Lr.CMP_ID.toString(),new Nr(12,0)),r.set(Lr.CMP_VERSION.toString(),new Nr(12,0)),r.set(Lr.CONSENT_SCREEN.toString(),new Nr(6,0)),r.set(Lr.CONSENT_LANGUAGE.toString(),new Br(2,"EN")),r.set(Lr.VENDOR_LIST_VERSION.toString(),new Nr(12,0)),r.set(Lr.POLICY_VERSION.toString(),new Nr(6,2)),r.set(Lr.IS_SERVICE_SPECIFIC.toString(),new Vr(!1)),r.set(Lr.USE_NON_STANDARD_STACKS.toString(),new Vr(!1)),r.set(Lr.SPECIAL_FEATURE_OPTINS.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),r.set(Lr.PURPOSE_CONSENTS.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),r.set(Lr.PURPOSE_LEGITIMATE_INTERESTS.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),r.set(Lr.PURPOSE_ONE_TREATMENT.toString(),new Vr(!1)),r.set(Lr.PUBLISHER_COUNTRY_CODE.toString(),new Br(2,"AA")),r.set(Lr.VENDOR_CONSENTS.toString(),new Yr([])),r.set(Lr.VENDOR_LEGITIMATE_INTERESTS.toString(),new Yr([])),r.set(Lr.PUBLISHER_RESTRICTIONS.toString(),new $r([])),r.set(Lr.PUBLISHER_PURPOSES_SEGMENT_TYPE.toString(),new Nr(3,3)),r.set(Lr.PUBLISHER_CONSENTS.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),r.set(Lr.PUBLISHER_LEGITIMATE_INTERESTS.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1]));var s=new Nr(6,0);return r.set(Lr.NUM_CUSTOM_PURPOSES.toString(),s),r.set(Lr.PUBLISHER_CUSTOM_CONSENTS.toString(),new Fr((function(){return s.getValue()}),[])),r.set(Lr.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS.toString(),new Fr((function(){return s.getValue()}),[])),r.set(Lr.VENDORS_ALLOWED_SEGMENT_TYPE.toString(),new Nr(3,2)),r.set(Lr.VENDORS_ALLOWED.toString(),new Yr([])),r.set(Lr.VENDORS_DISCLOSED_SEGMENT_TYPE.toString(),new Nr(3,1)),r.set(Lr.VENDORS_DISCLOSED.toString(),new Yr([])),h(n=u(this,e,[r,[[Lr.VERSION.toString(),Lr.CREATED.toString(),Lr.LAST_UPDATED.toString(),Lr.CMP_ID.toString(),Lr.CMP_VERSION.toString(),Lr.CONSENT_SCREEN.toString(),Lr.CONSENT_LANGUAGE.toString(),Lr.VENDOR_LIST_VERSION.toString(),Lr.POLICY_VERSION.toString(),Lr.IS_SERVICE_SPECIFIC.toString(),Lr.USE_NON_STANDARD_STACKS.toString(),Lr.SPECIAL_FEATURE_OPTINS.toString(),Lr.PURPOSE_CONSENTS.toString(),Lr.PURPOSE_LEGITIMATE_INTERESTS.toString(),Lr.PURPOSE_ONE_TREATMENT.toString(),Lr.PUBLISHER_COUNTRY_CODE.toString(),Lr.VENDOR_CONSENTS.toString(),Lr.VENDOR_LEGITIMATE_INTERESTS.toString(),Lr.PUBLISHER_RESTRICTIONS.toString()],[Lr.PUBLISHER_PURPOSES_SEGMENT_TYPE.toString(),Lr.PUBLISHER_CONSENTS.toString(),Lr.PUBLISHER_LEGITIMATE_INTERESTS.toString(),Lr.NUM_CUSTOM_PURPOSES.toString(),Lr.PUBLISHER_CUSTOM_CONSENTS.toString(),Lr.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS.toString()],[Lr.VENDORS_ALLOWED_SEGMENT_TYPE.toString(),Lr.VENDORS_ALLOWED.toString()],[Lr.VENDORS_DISCLOSED_SEGMENT_TYPE.toString(),Lr.VENDORS_DISCLOSED.toString()]]]),"base64UrlEncoder",new zr),t&&t.length>0&&n.decode(t),n}return S(e,Hr),p(e,[{key:"encode",value:function(){var e=this.encodeSegmentsToBitStrings(),t=[];return t.push(this.base64UrlEncoder.encode(e[0])),this.getFieldValue(Lr.IS_SERVICE_SPECIFIC.toString())?e[1]&&e[1].length>0&&t.push(this.base64UrlEncoder.encode(e[1])):(e[2]&&e[2].length>0&&t.push(this.base64UrlEncoder.encode(e[2])),e[3]&&e[3].length>0&&t.push(this.base64UrlEncoder.encode(e[3]))),t.join(".")}},{key:"decode",value:function(e){for(var t=e.split("."),n=[],r=0;r<t.length;r++){var i=this.base64UrlEncoder.decode(t[r]);switch(i.substring(0,3)){case"000":n[0]=i;break;case"001":n[3]=i;break;case"010":n[2]=i;break;case"011":n[1]=i;break;default:throw new Sr("Unable to decode segment '"+t[r]+"'")}}this.decodeSegmentsFromBitStrings(n)}},{key:"setFieldValue",value:function(t,n){if(C(e,"setFieldValue",this,3)([t,n]),t!==Lr.CREATED.toString()&&t!==Lr.LAST_UPDATED.toString()){var r=new Date,i=new Date(Date.UTC(r.getUTCFullYear(),r.getUTCMonth(),r.getUTCDate()));this.setFieldValue(Lr.CREATED.toString(),i),this.setFieldValue(Lr.LAST_UPDATED.toString(),i)}}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(Kr,"ID",2),h(Kr,"VERSION",2),h(Kr,"NAME","tcfeuv2"),function(e){e.VERSION="Version",e.CREATED="Created",e.LAST_UPDATED="LastUpdated",e.CMP_ID="CmpId",e.CMP_VERSION="CmpVersion",e.CONSENT_SCREEN="ConsentScreen",e.CONSENT_LANGUAGE="ConsentLanguage",e.VENDOR_LIST_VERSION="VendorListVersion",e.TCF_POLICY_VERSION="TcfPolicyVersion",e.USE_NON_STANDARD_STACKS="UseNonStandardStacks",e.SPECIAL_FEATURE_EXPRESS_CONSENT="SpecialFeatureExpressConsent",e.PURPOSES_EXPRESS_CONSENT="PurposesExpressConsent",e.PURPOSES_IMPLIED_CONSENT="PurposesImpliedConsent",e.VENDOR_EXPRESS_CONSENT="VendorExpressConsent",e.VENDOR_IMPLIED_CONSENT="VendorImpliedConsent",e.SEGMENT_TYPE="SegmentType",e.PUB_PURPOSES_EXPRESS_CONSENT="PubPurposesExpressConsent",e.PUB_PURPOSES_IMPLIED_CONSENT="PubPurposesImpliedConsent",e.NUM_CUSTOM_PURPOSES="NumCustomPurposes",e.CUSTOM_PURPOSES_EXPRESS_CONSENT="CustomPurposesExpressConsent",e.CUSTOM_PURPOSES_IMPLIED_CONSENT="CustomPurposesImpliedConsent"}(Wr||(Wr={}));var Jr,qr=function(){function e(t){var n;l(this,e);var r=new Map,i=new Date;r.set(Wr.VERSION.toString(),new Nr(6,e.VERSION)),r.set(Wr.CREATED.toString(),new kr(i)),r.set(Wr.LAST_UPDATED.toString(),new kr(i)),r.set(Wr.CMP_ID.toString(),new Nr(12,0)),r.set(Wr.CMP_VERSION.toString(),new Nr(12,0)),r.set(Wr.CONSENT_SCREEN.toString(),new Nr(6,0)),r.set(Wr.CONSENT_LANGUAGE.toString(),new Br(2,"EN")),r.set(Wr.VENDOR_LIST_VERSION.toString(),new Nr(12,0)),r.set(Wr.TCF_POLICY_VERSION.toString(),new Nr(6,2)),r.set(Wr.USE_NON_STANDARD_STACKS.toString(),new Vr(!1)),r.set(Wr.SPECIAL_FEATURE_EXPRESS_CONSENT.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),r.set(Wr.PURPOSES_EXPRESS_CONSENT.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),r.set(Wr.PURPOSES_IMPLIED_CONSENT.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),r.set(Wr.VENDOR_EXPRESS_CONSENT.toString(),new Yr([])),r.set(Wr.VENDOR_IMPLIED_CONSENT.toString(),new Yr([])),r.set(Wr.SEGMENT_TYPE.toString(),new Nr(3,3)),r.set(Wr.PUB_PURPOSES_EXPRESS_CONSENT.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),r.set(Wr.PUB_PURPOSES_IMPLIED_CONSENT.toString(),new xr([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1]));var s=new Nr(6,0);return r.set(Wr.NUM_CUSTOM_PURPOSES.toString(),s),r.set(Wr.CUSTOM_PURPOSES_EXPRESS_CONSENT.toString(),new Fr((function(){return s.getValue()}),[])),r.set(Wr.CUSTOM_PURPOSES_IMPLIED_CONSENT.toString(),new Fr((function(){return s.getValue()}),[])),h(n=u(this,e,[r,[[Wr.VERSION.toString(),Wr.CREATED.toString(),Wr.LAST_UPDATED.toString(),Wr.CMP_ID.toString(),Wr.CMP_VERSION.toString(),Wr.CONSENT_SCREEN.toString(),Wr.CONSENT_LANGUAGE.toString(),Wr.VENDOR_LIST_VERSION.toString(),Wr.TCF_POLICY_VERSION.toString(),Wr.USE_NON_STANDARD_STACKS.toString(),Wr.SPECIAL_FEATURE_EXPRESS_CONSENT.toString(),Wr.PURPOSES_EXPRESS_CONSENT.toString(),Wr.PURPOSES_IMPLIED_CONSENT.toString(),Wr.VENDOR_EXPRESS_CONSENT.toString(),Wr.VENDOR_IMPLIED_CONSENT.toString()],[Wr.SEGMENT_TYPE.toString(),Wr.PUB_PURPOSES_EXPRESS_CONSENT.toString(),Wr.PUB_PURPOSES_IMPLIED_CONSENT.toString(),Wr.NUM_CUSTOM_PURPOSES.toString(),Wr.CUSTOM_PURPOSES_EXPRESS_CONSENT.toString(),Wr.CUSTOM_PURPOSES_IMPLIED_CONSENT.toString()]]]),"base64UrlEncoder",new yr),t&&t.length>0&&n.decode(t),n}return S(e,Hr),p(e,[{key:"encode",value:function(){var e=this.encodeSegmentsToBitStrings(),t=[];return t.push(this.base64UrlEncoder.encode(e[0])),e[1]&&e[1].length>0&&t.push(this.base64UrlEncoder.encode(e[1])),t.join(".")}},{key:"decode",value:function(e){for(var t=e.split("."),n=[],r=0;r<t.length;r++){var i=this.base64UrlEncoder.decode(t[r]);switch(i.substring(0,3)){case"000":n[0]=i;break;case"011":n[1]=i;break;default:throw new Sr("Unable to decode segment '"+t[r]+"'")}}this.decodeSegmentsFromBitStrings(n)}},{key:"setFieldValue",value:function(t,n){if(C(e,"setFieldValue",this,3)([t,n]),t!==Wr.CREATED.toString()&&t!==Wr.LAST_UPDATED.toString()){var r=new Date,i=new Date(Date.UTC(r.getUTCFullYear(),r.getUTCMonth(),r.getUTCDate()));this.setFieldValue(Wr.CREATED.toString(),i),this.setFieldValue(Wr.LAST_UPDATED.toString(),i)}}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(qr,"ID",5),h(qr,"VERSION",1),h(qr,"NAME","tcfcav1"),function(e){e.VERSION="Version",e.NOTICE="Notice",e.OPT_OUT_SALE="OptOutSale",e.LSPA_COVERED="LspaCovered"}(Jr||(Jr={}));var Xr=function(){function e(t){l(this,e),h(this,"fields",void 0),this.fields=new Map,this.fields.set(Jr.VERSION.toString(),e.VERSION),this.fields.set(Jr.NOTICE.toString(),"-"),this.fields.set(Jr.OPT_OUT_SALE.toString(),"-"),this.fields.set(Jr.LSPA_COVERED.toString(),"-"),t&&t.length>0&&this.decode(t)}return p(e,[{key:"hasField",value:function(e){return this.fields.has(e)}},{key:"getFieldValue",value:function(e){return this.fields.has(e)?this.fields.get(e):null}},{key:"setFieldValue",value:function(e,t){if(!this.fields.has(e))throw new Dr(e+" not found");this.fields.set(e,t)}},{key:"toObj",value:function(){var e,t={},n=f(this.fields.keys());try{for(n.s();!(e=n.n()).done;){var r=e.value,i=this.fields.get(r);t[r.toString()]=i}}catch(e){n.e(e)}finally{n.f()}return t}},{key:"encode",value:function(){var e="";return e+=this.getFieldValue(Jr.VERSION.toString()),e+=this.getFieldValue(Jr.NOTICE.toString()),(e+=this.getFieldValue(Jr.OPT_OUT_SALE.toString()))+this.getFieldValue(Jr.LSPA_COVERED.toString())}},{key:"decode",value:function(e){this.setFieldValue(Jr.VERSION.toString(),parseInt(e.charAt(0))),this.setFieldValue(Jr.NOTICE.toString(),e.charAt(1)),this.setFieldValue(Jr.OPT_OUT_SALE.toString(),e.charAt(2)),this.setFieldValue(Jr.LSPA_COVERED.toString(),e.charAt(3))}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(Xr,"ID",6),h(Xr,"VERSION",1),h(Xr,"NAME","uspv1");var Qr,Zr=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t,n){for(var r="",i=0;i<e.length;i++)r+=Er.encode(e[i],t);for(;r.length<t*n;)r+="0";return r}},{key:"decode",value:function(e,t,n){if(!/^[0-1]*$/.test(e))throw new Sr("Undecodable FixedInteger '"+e+"'");if(e.length>t*n)throw new Sr("Undecodable FixedIntegerList '"+e+"'");if(e.length%t!=0)throw new Sr("Undecodable FixedIntegerList '"+e+"'");for(;e.length<t*n;)e+="0";e.length>t*n&&(e=e.substring(0,t*n));for(var r=[],i=0;i<e.length;i+=t)r.push(Er.decode(e.substring(i,i+t)));for(;r.length<n;)r.push(0);return r}}]),ei=function(){function e(t,n){var r;return l(this,e),h(r=u(this,e),"elementBitStringLength",void 0),h(r,"numElements",void 0),r.elementBitStringLength=t,r.numElements=n.length,r.setValue(n),r}return S(e,Ar),p(e,[{key:"encode",value:function(){return Zr.encode(this.value,this.elementBitStringLength,this.numElements)}},{key:"decode",value:function(e){this.value=Zr.decode(e,this.elementBitStringLength,this.numElements)}},{key:"substring",value:function(e,t){return e.substring(t,t+this.elementBitStringLength*this.numElements)}},{key:"getValue",value:function(){return T(C(e,"getValue",this,3)([]))}},{key:"setValue",value:function(t){for(var n=T(t),r=n.length;r<this.numElements;r++)n.push(0);n.length>this.numElements&&(n=n.slice(0,this.numElements)),C(e,"setValue",this,3)([n])}}])}();!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.SHARING_OPT_OUT_NOTICE="SharingOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE="SensitiveDataProcessingOptOutNotice",e.SENSITIVE_DATA_LIMIT_USE_NOTICE="SensitiveDataLimitUseNotice",e.SALE_OPT_OUT="SaleOptOut",e.SHARING_OPT_OUT="SharingOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.PERSONAL_DATA_CONSENTS="PersonalDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Qr||(Qr={}));var ti,ni=function(){function e(t){var n;l(this,e);var r=new Map;return r.set(Qr.VERSION.toString(),new Nr(6,e.VERSION)),r.set(Qr.SHARING_NOTICE.toString(),new Nr(2,0)),r.set(Qr.SALE_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(Qr.SHARING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(Qr.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(Qr.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(Qr.SENSITIVE_DATA_LIMIT_USE_NOTICE.toString(),new Nr(2,0)),r.set(Qr.SALE_OPT_OUT.toString(),new Nr(2,0)),r.set(Qr.SHARING_OPT_OUT.toString(),new Nr(2,0)),r.set(Qr.TARGETED_ADVERTISING_OPT_OUT.toString(),new Nr(2,0)),r.set(Qr.SENSITIVE_DATA_PROCESSING.toString(),new ei(2,[0,0,0,0,0,0,0,0,0,0,0,0])),r.set(Qr.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ei(2,[0,0])),r.set(Qr.PERSONAL_DATA_CONSENTS.toString(),new Nr(2,0)),r.set(Qr.MSPA_COVERED_TRANSACTION.toString(),new Nr(2,0)),r.set(Qr.MSPA_OPT_OUT_OPTION_MODE.toString(),new Nr(2,0)),r.set(Qr.MSPA_SERVICE_PROVIDER_MODE.toString(),new Nr(2,0)),r.set(Qr.GPC_SEGMENT_TYPE.toString(),new Nr(2,1)),r.set(Qr.GPC_SEGMENT_INCLUDED.toString(),new Vr(!0)),r.set(Qr.GPC.toString(),new Vr(!1)),h(n=u(this,e,[r,[[Qr.VERSION.toString(),Qr.SHARING_NOTICE.toString(),Qr.SALE_OPT_OUT_NOTICE.toString(),Qr.SHARING_OPT_OUT_NOTICE.toString(),Qr.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),Qr.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE.toString(),Qr.SENSITIVE_DATA_LIMIT_USE_NOTICE.toString(),Qr.SALE_OPT_OUT.toString(),Qr.SHARING_OPT_OUT.toString(),Qr.TARGETED_ADVERTISING_OPT_OUT.toString(),Qr.SENSITIVE_DATA_PROCESSING.toString(),Qr.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),Qr.PERSONAL_DATA_CONSENTS.toString(),Qr.MSPA_COVERED_TRANSACTION.toString(),Qr.MSPA_OPT_OUT_OPTION_MODE.toString(),Qr.MSPA_SERVICE_PROVIDER_MODE.toString()],[Qr.GPC_SEGMENT_TYPE.toString(),Qr.GPC.toString()]]]),"base64UrlEncoder",new yr),t&&t.length>0&&n.decode(t),n}return S(e,Hr),p(e,[{key:"encode",value:function(){var e=this.encodeSegmentsToBitStrings(),t=[];return t.push(this.base64UrlEncoder.encode(e[0])),e[1]&&e[1].length>0&&!0===this.fields.get(Qr.GPC_SEGMENT_INCLUDED).getValue()&&t.push(this.base64UrlEncoder.encode(e[1])),t.join(".")}},{key:"decode",value:function(e){for(var t=e.split("."),n=[],r=!1,i=0;i<t.length;i++){var s=this.base64UrlEncoder.decode(t[i]);switch(s.substring(0,2)){case"00":n[0]=s;break;case"01":r=!0,n[1]=s;break;default:throw new Sr("Unable to decode segment '"+t[i]+"'")}}this.decodeSegmentsFromBitStrings(n),this.fields.get(Qr.GPC_SEGMENT_INCLUDED).setValue(r)}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(ni,"ID",7),h(ni,"VERSION",1),h(ni,"NAME","usnatv1"),function(e){e.VERSION="Version",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.SHARING_OPT_OUT_NOTICE="SharingOptOutNotice",e.SENSITIVE_DATA_LIMIT_USE_NOTICE="SensitiveDataLimitUseNotice",e.SALE_OPT_OUT="SaleOptOut",e.SHARING_OPT_OUT="SharingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.PERSONAL_DATA_CONSENTS="PersonalDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(ti||(ti={}));var ri,ii=function(){function e(t){var n;l(this,e);var r=new Map;return r.set(ti.VERSION.toString(),new Nr(6,e.VERSION)),r.set(ti.SALE_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ti.SHARING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ti.SENSITIVE_DATA_LIMIT_USE_NOTICE.toString(),new Nr(2,0)),r.set(ti.SALE_OPT_OUT.toString(),new Nr(2,0)),r.set(ti.SHARING_OPT_OUT.toString(),new Nr(2,0)),r.set(ti.SENSITIVE_DATA_PROCESSING.toString(),new ei(2,[0,0,0,0,0,0,0,0,0])),r.set(ti.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ei(2,[0,0])),r.set(ti.PERSONAL_DATA_CONSENTS.toString(),new Nr(2,0)),r.set(ti.MSPA_COVERED_TRANSACTION.toString(),new Nr(2,0)),r.set(ti.MSPA_OPT_OUT_OPTION_MODE.toString(),new Nr(2,0)),r.set(ti.MSPA_SERVICE_PROVIDER_MODE.toString(),new Nr(2,0)),r.set(ti.GPC_SEGMENT_TYPE.toString(),new Nr(2,1)),r.set(ti.GPC_SEGMENT_INCLUDED.toString(),new Vr(!0)),r.set(ti.GPC.toString(),new Vr(!1)),h(n=u(this,e,[r,[[ti.VERSION.toString(),ti.SALE_OPT_OUT_NOTICE.toString(),ti.SHARING_OPT_OUT_NOTICE.toString(),ti.SENSITIVE_DATA_LIMIT_USE_NOTICE.toString(),ti.SALE_OPT_OUT.toString(),ti.SHARING_OPT_OUT.toString(),ti.SENSITIVE_DATA_PROCESSING.toString(),ti.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),ti.PERSONAL_DATA_CONSENTS.toString(),ti.MSPA_COVERED_TRANSACTION.toString(),ti.MSPA_OPT_OUT_OPTION_MODE.toString(),ti.MSPA_SERVICE_PROVIDER_MODE.toString()],[ti.GPC_SEGMENT_TYPE.toString(),ti.GPC.toString()]]]),"base64UrlEncoder",new yr),t&&t.length>0&&n.decode(t),n}return S(e,Hr),p(e,[{key:"encode",value:function(){var e=this.encodeSegmentsToBitStrings(),t=[];return t.push(this.base64UrlEncoder.encode(e[0])),e[1]&&e[1].length>0&&!0===this.fields.get(ti.GPC_SEGMENT_INCLUDED).getValue()&&t.push(this.base64UrlEncoder.encode(e[1])),t.join(".")}},{key:"decode",value:function(e){for(var t=e.split("."),n=[],r=!1,i=0;i<t.length;i++){var s=this.base64UrlEncoder.decode(t[i]);switch(s.substring(0,2)){case"00":n[0]=s;break;case"01":r=!0,n[1]=s;break;default:throw new Sr("Unable to decode segment '"+t[i]+"'")}}this.decodeSegmentsFromBitStrings(n),this.fields.get(ti.GPC_SEGMENT_INCLUDED).setValue(r)}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(ii,"ID",8),h(ii,"VERSION",1),h(ii,"NAME","uscav1"),function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode"}(ri||(ri={}));var si,oi=function(){function e(t){var n;l(this,e);var r=new Map;return r.set(ri.VERSION.toString(),new Nr(6,e.VERSION)),r.set(ri.SHARING_NOTICE.toString(),new Nr(2,0)),r.set(ri.SALE_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ri.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ri.SALE_OPT_OUT.toString(),new Nr(2,0)),r.set(ri.TARGETED_ADVERTISING_OPT_OUT.toString(),new Nr(2,0)),r.set(ri.SENSITIVE_DATA_PROCESSING.toString(),new ei(2,[0,0,0,0,0,0,0,0])),r.set(ri.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Nr(2,0)),r.set(ri.MSPA_COVERED_TRANSACTION.toString(),new Nr(2,0)),r.set(ri.MSPA_OPT_OUT_OPTION_MODE.toString(),new Nr(2,0)),r.set(ri.MSPA_SERVICE_PROVIDER_MODE.toString(),new Nr(2,0)),h(n=u(this,e,[r,[ri.VERSION.toString(),ri.SHARING_NOTICE.toString(),ri.SALE_OPT_OUT_NOTICE.toString(),ri.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),ri.SALE_OPT_OUT.toString(),ri.TARGETED_ADVERTISING_OPT_OUT.toString(),ri.SENSITIVE_DATA_PROCESSING.toString(),ri.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),ri.MSPA_COVERED_TRANSACTION.toString(),ri.MSPA_OPT_OUT_OPTION_MODE.toString(),ri.MSPA_SERVICE_PROVIDER_MODE.toString()]]),"base64UrlEncoder",new yr),t&&t.length>0&&n.decode(t),n}return S(e,wr),p(e,[{key:"encode",value:function(){return this.base64UrlEncoder.encode(this.encodeToBitString())}},{key:"decode",value:function(e){this.decodeFromBitString(this.base64UrlEncoder.decode(e))}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(oi,"ID",9),h(oi,"VERSION",1),h(oi,"NAME","usvav1"),function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(si||(si={}));var ai,ci=function(){function e(t){var n;l(this,e);var r=new Map;return r.set(si.VERSION.toString(),new Nr(6,e.VERSION)),r.set(si.SHARING_NOTICE.toString(),new Nr(2,0)),r.set(si.SALE_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(si.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(si.SALE_OPT_OUT.toString(),new Nr(2,0)),r.set(si.TARGETED_ADVERTISING_OPT_OUT.toString(),new Nr(2,0)),r.set(si.SENSITIVE_DATA_PROCESSING.toString(),new ei(2,[0,0,0,0,0,0,0])),r.set(si.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Nr(2,0)),r.set(si.MSPA_COVERED_TRANSACTION.toString(),new Nr(2,0)),r.set(si.MSPA_OPT_OUT_OPTION_MODE.toString(),new Nr(2,0)),r.set(si.MSPA_SERVICE_PROVIDER_MODE.toString(),new Nr(2,0)),r.set(si.GPC_SEGMENT_TYPE.toString(),new Nr(2,1)),r.set(si.GPC_SEGMENT_INCLUDED.toString(),new Vr(!0)),r.set(si.GPC.toString(),new Vr(!1)),h(n=u(this,e,[r,[[si.VERSION.toString(),si.SHARING_NOTICE.toString(),si.SALE_OPT_OUT_NOTICE.toString(),si.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),si.SALE_OPT_OUT.toString(),si.TARGETED_ADVERTISING_OPT_OUT.toString(),si.SENSITIVE_DATA_PROCESSING.toString(),si.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),si.MSPA_COVERED_TRANSACTION.toString(),si.MSPA_OPT_OUT_OPTION_MODE.toString(),si.MSPA_SERVICE_PROVIDER_MODE.toString()],[si.GPC_SEGMENT_TYPE.toString(),si.GPC.toString()]]]),"base64UrlEncoder",new yr),t&&t.length>0&&n.decode(t),n}return S(e,Hr),p(e,[{key:"encode",value:function(){var e=this.encodeSegmentsToBitStrings(),t=[];return t.push(this.base64UrlEncoder.encode(e[0])),e[1]&&e[1].length>0&&!0===this.fields.get(si.GPC_SEGMENT_INCLUDED).getValue()&&t.push(this.base64UrlEncoder.encode(e[1])),t.join(".")}},{key:"decode",value:function(e){for(var t=e.split("."),n=[],r=!1,i=0;i<t.length;i++){var s=this.base64UrlEncoder.decode(t[i]);switch(s.substring(0,2)){case"00":n[0]=s;break;case"01":r=!0,n[1]=s;break;default:throw new Sr("Unable to decode segment '"+t[i]+"'")}}this.decodeSegmentsFromBitStrings(n),this.fields.get(si.GPC_SEGMENT_INCLUDED).setValue(r)}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(ci,"ID",10),h(ci,"VERSION",1),h(ci,"NAME","uscov1"),function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE="SensitiveDataProcessingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode"}(ai||(ai={}));var ui,li=function(){function e(t){var n;l(this,e);var r=new Map;return r.set(ai.VERSION.toString(),new Nr(6,e.VERSION)),r.set(ai.SHARING_NOTICE.toString(),new Nr(2,0)),r.set(ai.SALE_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ai.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ai.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ai.SALE_OPT_OUT.toString(),new Nr(2,0)),r.set(ai.TARGETED_ADVERTISING_OPT_OUT.toString(),new Nr(2,0)),r.set(ai.SENSITIVE_DATA_PROCESSING.toString(),new ei(2,[0,0,0,0,0,0,0,0])),r.set(ai.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Nr(2,0)),r.set(ai.MSPA_COVERED_TRANSACTION.toString(),new Nr(2,0)),r.set(ai.MSPA_OPT_OUT_OPTION_MODE.toString(),new Nr(2,0)),r.set(ai.MSPA_SERVICE_PROVIDER_MODE.toString(),new Nr(2,0)),h(n=u(this,e,[r,[ai.VERSION.toString(),ai.SHARING_NOTICE.toString(),ai.SALE_OPT_OUT_NOTICE.toString(),ai.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),ai.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE.toString(),ai.SALE_OPT_OUT.toString(),ai.TARGETED_ADVERTISING_OPT_OUT.toString(),ai.SENSITIVE_DATA_PROCESSING.toString(),ai.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),ai.MSPA_COVERED_TRANSACTION.toString(),ai.MSPA_OPT_OUT_OPTION_MODE.toString(),ai.MSPA_SERVICE_PROVIDER_MODE.toString()]]),"base64UrlEncoder",new yr),t&&t.length>0&&n.decode(t),n}return S(e,wr),p(e,[{key:"encode",value:function(){return this.base64UrlEncoder.encode(this.encodeToBitString())}},{key:"decode",value:function(e){this.decodeFromBitString(this.base64UrlEncoder.decode(e))}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(li,"ID",11),h(li,"VERSION",1),h(li,"NAME","usutv1"),function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(ui||(ui={}));var di=function(){function e(t){var n;l(this,e);var r=new Map;return r.set(ui.VERSION.toString(),new Nr(6,e.VERSION)),r.set(ui.SHARING_NOTICE.toString(),new Nr(2,0)),r.set(ui.SALE_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ui.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new Nr(2,0)),r.set(ui.SALE_OPT_OUT.toString(),new Nr(2,0)),r.set(ui.TARGETED_ADVERTISING_OPT_OUT.toString(),new Nr(2,0)),r.set(ui.SENSITIVE_DATA_PROCESSING.toString(),new ei(2,[0,0,0,0,0,0,0,0])),r.set(ui.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ei(2,[0,0,0])),r.set(ui.MSPA_COVERED_TRANSACTION.toString(),new Nr(2,0)),r.set(ui.MSPA_OPT_OUT_OPTION_MODE.toString(),new Nr(2,0)),r.set(ui.MSPA_SERVICE_PROVIDER_MODE.toString(),new Nr(2,0)),r.set(ui.GPC_SEGMENT_TYPE.toString(),new Nr(2,1)),r.set(ui.GPC_SEGMENT_INCLUDED.toString(),new Vr(!0)),r.set(ui.GPC.toString(),new Vr(!1)),h(n=u(this,e,[r,[[ui.VERSION.toString(),ui.SHARING_NOTICE.toString(),ui.SALE_OPT_OUT_NOTICE.toString(),ui.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),ui.SALE_OPT_OUT.toString(),ui.TARGETED_ADVERTISING_OPT_OUT.toString(),ui.SENSITIVE_DATA_PROCESSING.toString(),ui.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),ui.MSPA_COVERED_TRANSACTION.toString(),ui.MSPA_OPT_OUT_OPTION_MODE.toString(),ui.MSPA_SERVICE_PROVIDER_MODE.toString()],[ui.GPC_SEGMENT_TYPE.toString(),ui.GPC.toString()]]]),"base64UrlEncoder",new yr),t&&t.length>0&&n.decode(t),n}return S(e,Hr),p(e,[{key:"encode",value:function(){var e=this.encodeSegmentsToBitStrings(),t=[];return t.push(this.base64UrlEncoder.encode(e[0])),e[1]&&e[1].length>0&&!0===this.fields.get(ui.GPC_SEGMENT_INCLUDED).getValue()&&t.push(this.base64UrlEncoder.encode(e[1])),t.join(".")}},{key:"decode",value:function(e){for(var t=e.split("."),n=[],r=!1,i=0;i<t.length;i++){var s=this.base64UrlEncoder.decode(t[i]);switch(s.substring(0,2)){case"00":n[0]=s;break;case"01":r=!0,n[1]=s;break;default:throw new Sr("Unable to decode segment '"+t[i]+"'")}}this.decodeSegmentsFromBitStrings(n),this.fields.get(ui.GPC_SEGMENT_INCLUDED).setValue(r)}},{key:"getId",value:function(){return e.ID}},{key:"getName",value:function(){return e.NAME}}])}();h(di,"ID",12),h(di,"VERSION",1),h(di,"NAME","usctv1");var pi=p((function e(){l(this,e)}));h(pi,"SECTION_ID_NAME_MAP",new Map([[Kr.ID,Kr.NAME],[qr.ID,qr.NAME],[Xr.ID,Xr.NAME],[ni.ID,ni.NAME],[ii.ID,ii.NAME],[oi.ID,oi.NAME],[ci.ID,ci.NAME],[li.ID,li.NAME],[di.ID,di.NAME]])),h(pi,"SECTION_ORDER",[Kr.NAME,qr.NAME,Xr.NAME,ni.NAME,ii.NAME,oi.NAME,ci.NAME,li.NAME,di.NAME]);var fi=p((function e(t){l(this,e),h(this,"sections",new Map),h(this,"encodedString",void 0),h(this,"decoded",void 0),h(this,"dirty",void 0),t?(this.encodedString=t,this.decoded=!1,this.dirty=!1):(this.encodedString="DBAA",this.decoded=!1,this.dirty=!1)}),[{key:"setFieldValue",value:function(e,t,n){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}var r=null;if(this.sections.has(e)?r=this.sections.get(e):e===qr.NAME?(r=new qr,this.sections.set(qr.NAME,r)):e===Kr.NAME?(r=new Kr,this.sections.set(Kr.NAME,r)):e===Xr.NAME?(r=new Xr,this.sections.set(Xr.NAME,r)):e===ni.NAME?(r=new ni,this.sections.set(ni.NAME,r)):e===ii.NAME?(r=new ii,this.sections.set(ii.NAME,r)):e===oi.NAME?(r=new oi,this.sections.set(oi.NAME,r)):e===ci.NAME?(r=new ci,this.sections.set(ci.NAME,r)):e===li.NAME?(r=new li,this.sections.set(li.NAME,r)):e===di.NAME&&(r=new di,this.sections.set(di.NAME,r)),!r)throw new Dr(e+"."+t+" not found");r.setFieldValue(t,n),this.dirty=!0}},{key:"setFieldValueBySectionId",value:function(e,t,n){this.setFieldValue(pi.SECTION_ID_NAME_MAP.get(e),t,n)}},{key:"getFieldValue",value:function(e,t){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}return this.sections.has(e)?this.sections.get(e).getFieldValue(t):null}},{key:"getFieldValueBySectionId",value:function(e,t){return this.getFieldValue(pi.SECTION_ID_NAME_MAP.get(e),t)}},{key:"hasField",value:function(e,t){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}return!!this.sections.has(e)&&this.sections.get(e).hasField(t)}},{key:"hasFieldBySectionId",value:function(e,t){return this.hasField(pi.SECTION_ID_NAME_MAP.get(e),t)}},{key:"hasSection",value:function(e){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}return this.sections.has(e)}},{key:"hasSectionId",value:function(e){return this.hasSection(pi.SECTION_ID_NAME_MAP.get(e))}},{key:"deleteSection",value:function(e){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}this.sections.delete(e),this.dirty=!0}},{key:"deleteSectionById",value:function(e){this.deleteSection(pi.SECTION_ID_NAME_MAP.get(e))}},{key:"clear",value:function(){this.sections.clear(),this.encodedString="DBAA",this.decoded=!1,this.dirty=!1}},{key:"getHeader",value:function(){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}var e=new Rr;return e.setFieldValue("SectionIds",this.getSectionIds()),e.toObj()}},{key:"getSection",value:function(e){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}return this.sections.has(e)?this.sections.get(e).toObj():null}},{key:"getSectionIds",value:function(){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}for(var e=[],t=0;t<pi.SECTION_ORDER.length;t++){var n=pi.SECTION_ORDER[t];if(this.sections.has(n)){var r=this.sections.get(n);e.push(r.getId())}}return e}},{key:"encode",value:function(){if(!this.dirty)return this.encodedString;if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}for(var e=[],t=[],n=0;n<pi.SECTION_ORDER.length;n++){var r=pi.SECTION_ORDER[n];if(this.sections.has(r)){var i=this.sections.get(r);e.push(i.encode()),t.push(i.getId())}}var s=new Rr;return s.setFieldValue("SectionIds",this.getSectionIds()),e.unshift(s.encode()),this.encodedString=e.join("~"),this.dirty=!1,this.encodedString}},{key:"decode",value:function(e){this.encodedString=e,this.decoded=!1,this.dirty=!0,this.sections.clear();var t=e.split("~"),n=new Rr(t[0]);this.sections.set(Rr.NAME,n);for(var r=n.getFieldValue("SectionIds"),i=0;i<r.length;i++)if(r[i]===qr.ID){var s=new qr(t[i+1]);this.sections.set(qr.NAME,s)}else if(r[i]===Kr.ID){var o=new Kr(t[i+1]);this.sections.set(Kr.NAME,o)}else if(r[i]===Xr.ID){var a=new Xr(t[i+1]);this.sections.set(Xr.NAME,a)}else if(r[i]===ni.ID){var c=new ni(t[i+1]);this.sections.set(ni.NAME,c)}else if(r[i]===ii.ID){var u=new ii(t[i+1]);this.sections.set(ii.NAME,u)}else if(r[i]===oi.ID){var l=new oi(t[i+1]);this.sections.set(oi.NAME,l)}else if(r[i]===ci.ID){var d=new ci(t[i+1]);this.sections.set(ci.NAME,d)}else if(r[i]===li.ID){var p=new li(t[i+1]);this.sections.set(li.NAME,p)}else if(r[i]===di.ID){var f=new di(t[i+1]);this.sections.set(di.NAME,f)}this.decoded=!0,this.dirty=!1}},{key:"encodeSection",value:function(e){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}return this.sections.has(e)?this.sections.get(e).encode():null}},{key:"encodeSectionById",value:function(e){return this.encodeSection(pi.SECTION_ID_NAME_MAP.get(e))}},{key:"decodeSection",value:function(e,t){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}var n=null;this.sections.has(e)?n=this.sections.get(e):e===qr.NAME?(n=new qr,this.sections.set(qr.NAME,n)):e===Kr.NAME?(n=new Kr,this.sections.set(Kr.NAME,n)):e===Xr.NAME?(n=new Xr,this.sections.set(Xr.NAME,n)):e===ni.NAME?(n=new ni,this.sections.set(ni.NAME,n)):e===ii.NAME?(n=new ii,this.sections.set(ii.NAME,n)):e===oi.NAME?(n=new oi,this.sections.set(oi.NAME,n)):e===ci.NAME?(n=new ci,this.sections.set(ci.NAME,n)):e===li.NAME?(n=new li,this.sections.set(li.NAME,n)):e===di.NAME&&(n=new di,this.sections.set(di.NAME,n)),n&&(n.decode(t),this.dirty=!0)}},{key:"decodeSectionById",value:function(e,t){this.decodeSection(pi.SECTION_ID_NAME_MAP.get(e),t)}},{key:"toObject",value:function(){if(!this.decoded&&null!=this.encodedString&&this.encodedString.length>0)try{this.decode(this.encodedString)}catch(e){throw new Pr(e.message)}for(var e={},t=0;t<pi.SECTION_ORDER.length;t++){var n=pi.SECTION_ORDER[t];this.sections.has(n)&&(e[n]=this.sections.get(n).toObj())}return e}}]),hi=p((function e(){l(this,e),h(this,"gppVersion","1.1"),h(this,"supportedAPIs",[]),h(this,"eventQueue",new vr(this)),h(this,"cmpStatus",lr.LOADING),h(this,"cmpDisplayStatus",dr.HIDDEN),h(this,"signalStatus",fr.NOT_READY),h(this,"applicableSections",[]),h(this,"gppModel",new fi),h(this,"cmpId",void 0),h(this,"cmpVersion",void 0),h(this,"eventStatus",void 0)}),[{key:"reset",value:function(){this.eventQueue.clear(),this.cmpStatus=lr.LOADING,this.cmpDisplayStatus=dr.HIDDEN,this.signalStatus=fr.NOT_READY,this.applicableSections=[],this.supportedAPIs=[],this.gppModel=new fi,delete this.cmpId,delete this.cmpVersion,delete this.eventStatus}}]),gi=p((function e(){l(this,e)}),null,[{key:"absCall",value:function(e,t,n,r){return new Promise((function(i,s){var o=new XMLHttpRequest;o.withCredentials=n,o.addEventListener("load",(function(){if(o.readyState==XMLHttpRequest.DONE)if(o.status>=200&&o.status<300){var e=o.response;if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}i(e)}else s(new Error("HTTP Status: ".concat(o.status," response type: ").concat(o.responseType)))})),o.addEventListener("error",(function(){s(new Error("error"))})),o.addEventListener("abort",(function(){s(new Error("aborted"))})),null===t?o.open("GET",e,!0):o.open("POST",e,!0),o.responseType="json",o.timeout=r,o.ontimeout=function(){s(new Error("Timeout "+r+"ms "+e))},o.send(t)}))}},{key:"post",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return this.absCall(e,JSON.stringify(t),n,r)}},{key:"fetch",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return this.absCall(e,null,t,n)}}]),vi=function(){function e(t){var n;return l(this,e),(n=u(this,e,[t])).name="GVLError",n}return S(e,N(Error)),p(e)}(),Si=function(){function e(){l(this,e)}return p(e,[{key:"has",value:function(t){return e.langSet.has(t)}},{key:"forEach",value:function(t){e.langSet.forEach(t)}},{key:"size",get:function(){return e.langSet.size}}])}();h(Si,"langSet",new Set(["BG","CA","CS","DA","DE","EL","EN","ES","ET","FI","FR","HR","HU","IT","JA","LT","LV","MT","NL","NO","PL","PT","RO","RU","SK","SL","SV","TR","ZH"]));var Ei=function(){function e(){l(this,e),h(this,"vendors",void 0),h(this,"consentLanguages",new Si),h(this,"gvlSpecificationVersion",void 0),h(this,"vendorListVersion",void 0),h(this,"tcfPolicyVersion",void 0),h(this,"lastUpdated",void 0),h(this,"purposes",void 0),h(this,"specialPurposes",void 0),h(this,"features",void 0),h(this,"specialFeatures",void 0),h(this,"stacks",void 0),h(this,"dataCategories",void 0),h(this,"language",e.DEFAULT_LANGUAGE),h(this,"vendorIds",void 0),h(this,"ready",!1),h(this,"fullVendorList",void 0),h(this,"byPurposeVendorMap",void 0),h(this,"bySpecialPurposeVendorMap",void 0),h(this,"byFeatureVendorMap",void 0),h(this,"bySpecialFeatureVendorMap",void 0),h(this,"baseUrl",void 0),h(this,"languageFilename","purposes-[LANG].json")}return p(e,[{key:"changeLanguage",value:(n=c(I().mark((function e(t){var n,r;return I().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.toUpperCase(),!this.consentLanguages.has(n)){e.next=18;break}if(n===this.language){e.next=16;break}return this.language=n,r=this.baseUrl+this.languageFilename.replace("[LANG]",t),e.prev=5,e.t0=this,e.next=9,gi.fetch(r);case 9:e.t1=e.sent,e.t0.populate.call(e.t0,e.t1),e.next=16;break;case 13:throw e.prev=13,e.t2=e.catch(5),new vi("unable to load language: "+e.t2.message);case 16:e.next=19;break;case 18:throw new vi("unsupported language ".concat(t));case 19:case"end":return e.stop()}}),e,this,[[5,13]])}))),function(e){return n.apply(this,arguments)})},{key:"getJson",value:function(){return JSON.parse(JSON.stringify({gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories,vendors:this.fullVendorList}))}},{key:"isVendorList",value:function(e){return void 0!==e&&void 0!==e.vendors}},{key:"populate",value:function(e){this.purposes=e.purposes,this.specialPurposes=e.specialPurposes,this.features=e.features,this.specialFeatures=e.specialFeatures,this.stacks=e.stacks,this.dataCategories=e.dataCategories,this.isVendorList(e)&&(this.gvlSpecificationVersion=e.gvlSpecificationVersion,this.tcfPolicyVersion=e.tcfPolicyVersion,this.vendorListVersion=e.vendorListVersion,this.lastUpdated=e.lastUpdated,"string"==typeof this.lastUpdated&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors=e.vendors,this.fullVendorList=e.vendors,this.mapVendors(),this.ready=!0)}},{key:"mapVendors",value:function(e){var t=this;this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach((function(e){t.byPurposeVendorMap[e]={legInt:new Set,impCons:new Set,consent:new Set,flexible:new Set}})),Object.keys(this.specialPurposes).forEach((function(e){t.bySpecialPurposeVendorMap[e]=new Set})),Object.keys(this.features).forEach((function(e){t.byFeatureVendorMap[e]=new Set})),Object.keys(this.specialFeatures).forEach((function(e){t.bySpecialFeatureVendorMap[e]=new Set})),Array.isArray(e)||(e=Object.keys(this.fullVendorList).map((function(e){return+e}))),this.vendorIds=new Set(e),this.vendors=e.reduce((function(e,n){var r=t.vendors[String(n)];return r&&void 0===r.deletedDate&&(r.purposes.forEach((function(e){t.byPurposeVendorMap[String(e)].consent.add(n)})),r.specialPurposes.forEach((function(e){t.bySpecialPurposeVendorMap[String(e)].add(n)})),r.legIntPurposes&&r.legIntPurposes.forEach((function(e){t.byPurposeVendorMap[String(e)].legInt.add(n)})),r.impConsPurposes&&r.impConsPurposes.forEach((function(e){t.byPurposeVendorMap[String(e)].impCons.add(n)})),r.flexiblePurposes&&r.flexiblePurposes.forEach((function(e){t.byPurposeVendorMap[String(e)].flexible.add(n)})),r.features.forEach((function(e){t.byFeatureVendorMap[String(e)].add(n)})),r.specialFeatures.forEach((function(e){t.bySpecialFeatureVendorMap[String(e)].add(n)})),e[n]=r),e}),{})}},{key:"getFilteredVendors",value:function(e,t,n,r){var i=this,s=e.charAt(0).toUpperCase()+e.slice(1),o={};return("purpose"===e&&n?this["by"+s+"VendorMap"][String(t)][n]:this["by"+(r?"Special":"")+s+"VendorMap"][String(t)]).forEach((function(e){o[String(e)]=i.vendors[String(e)]})),o}},{key:"getVendorsWithConsentPurpose",value:function(e){return this.getFilteredVendors("purpose",e,"consent")}},{key:"getVendorsWithLegIntPurpose",value:function(e){return this.getFilteredVendors("purpose",e,"legInt")}},{key:"getVendorsWithFlexiblePurpose",value:function(e){return this.getFilteredVendors("purpose",e,"flexible")}},{key:"getVendorsWithSpecialPurpose",value:function(e){return this.getFilteredVendors("purpose",e,void 0,!0)}},{key:"getVendorsWithFeature",value:function(e){return this.getFilteredVendors("feature",e)}},{key:"getVendorsWithSpecialFeature",value:function(e){return this.getFilteredVendors("feature",e,void 0,!0)}},{key:"narrowVendorsTo",value:function(e){this.mapVendors(e)}},{key:"isReady",get:function(){return this.ready}}],[{key:"fromVendorList",value:function(t){var n=new e;return n.populate(t),n}},{key:"fromUrl",value:(t=c(I().mark((function t(n){var r,i,s,o,a,c;return I().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if((r=n.baseUrl)&&0!==r.length){t.next=3;break}throw new vi("Invalid baseUrl: '"+r+"'");case 3:if(!/^https?:\/\/vendorlist\.consensu\.org\//.test(r)){t.next=5;break}throw new vi("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");case 5:if(r.length>0&&"/"!==r[r.length-1]&&(r+="/"),(i=new e).baseUrl=r,n.languageFilename?i.languageFilename=n.languageFilename:i.languageFilename="purposes-[LANG].json",!(n.version>0)){t.next=20;break}return(s=n.versionedFilename)||(s="archives/vendor-list-v[VERSION].json"),o=r+s.replace("[VERSION]",String(n.version)),t.t0=i,t.next=16,gi.fetch(o);case 16:t.t1=t.sent,t.t0.populate.call(t.t0,t.t1),t.next=28;break;case 20:return(a=n.latestFilename)||(a="vendor-list.json"),c=r+a,t.t2=i,t.next=26,gi.fetch(c);case 26:t.t3=t.sent,t.t2.populate.call(t.t2,t.t3);case 28:return t.abrupt("return",i);case 29:case"end":return t.stop()}}),t)}))),function(e){return t.apply(this,arguments)})},{key:"isInstanceOf",value:function(e){return"object"===A(e)&&"function"==typeof e.narrowVendorsTo}}]);var t,n}();h(Ei,"DEFAULT_LANGUAGE","EN");var _i,mi,Ii=function(){return p((function e(t,n,r){l(this,e),h(this,"callResponder",void 0),h(this,"cmpApiContext",void 0),this.cmpApiContext=new hi,this.cmpApiContext.cmpId=t,this.cmpApiContext.cmpVersion=n,this.callResponder=new gr(this.cmpApiContext,r)}),[{key:"fireEvent",value:function(e,t){this.cmpApiContext.eventQueue.exec(e,t)}},{key:"fireErrorEvent",value:function(e){this.cmpApiContext.eventQueue.exec("error",e)}},{key:"fireSectionChange",value:function(e){this.cmpApiContext.eventQueue.exec("sectionChange",e)}},{key:"getEventStatus",value:function(){return this.cmpApiContext.eventStatus}},{key:"setEventStatus",value:function(e){this.cmpApiContext.eventStatus=e}},{key:"getCmpStatus",value:function(){return this.cmpApiContext.cmpStatus}},{key:"setCmpStatus",value:function(e){this.cmpApiContext.cmpStatus=e,this.cmpApiContext.eventQueue.exec("cmpStatus",e)}},{key:"getCmpDisplayStatus",value:function(){return this.cmpApiContext.cmpDisplayStatus}},{key:"setCmpDisplayStatus",value:function(e){this.cmpApiContext.cmpDisplayStatus=e,this.cmpApiContext.eventQueue.exec("cmpDisplayStatus",e)}},{key:"getSignalStatus",value:function(){return this.cmpApiContext.signalStatus}},{key:"setSignalStatus",value:function(e){this.cmpApiContext.signalStatus=e,this.cmpApiContext.eventQueue.exec("signalStatus",e)}},{key:"getApplicableSections",value:function(){return this.cmpApiContext.applicableSections}},{key:"setApplicableSections",value:function(e){this.cmpApiContext.applicableSections=e}},{key:"getSupportedAPIs",value:function(){return this.cmpApiContext.supportedAPIs}},{key:"setSupportedAPIs",value:function(e){this.cmpApiContext.supportedAPIs=e}},{key:"setGppString",value:function(e){this.cmpApiContext.gppModel.decode(e)}},{key:"getGppString",value:function(){return this.cmpApiContext.gppModel.encode()}},{key:"setSectionString",value:function(e,t){this.cmpApiContext.gppModel.decodeSection(e,t)}},{key:"setSectionStringById",value:function(e,t){this.setSectionString(pi.SECTION_ID_NAME_MAP.get(e),t)}},{key:"getSectionString",value:function(e){return this.cmpApiContext.gppModel.encodeSection(e)}},{key:"getSectionStringById",value:function(e){return this.getSectionString(pi.SECTION_ID_NAME_MAP.get(e))}},{key:"setFieldValue",value:function(e,t,n){this.cmpApiContext.gppModel.setFieldValue(e,t,n)}},{key:"setFieldValueBySectionId",value:function(e,t,n){this.setFieldValue(pi.SECTION_ID_NAME_MAP.get(e),t,n)}},{key:"getFieldValue",value:function(e,t){return this.cmpApiContext.gppModel.getFieldValue(e,t)}},{key:"getFieldValueBySectionId",value:function(e,t){return this.getFieldValue(pi.SECTION_ID_NAME_MAP.get(e),t)}},{key:"getSection",value:function(e){return this.cmpApiContext.gppModel.getSection(e)}},{key:"getSectionById",value:function(e){return this.getSection(pi.SECTION_ID_NAME_MAP.get(e))}},{key:"hasSection",value:function(e){return this.cmpApiContext.gppModel.hasSection(e)}},{key:"hasSectionId",value:function(e){return this.hasSection(pi.SECTION_ID_NAME_MAP.get(e))}},{key:"deleteSection",value:function(e){this.cmpApiContext.gppModel.deleteSection(e)}},{key:"deleteSectionById",value:function(e){this.deleteSection(pi.SECTION_ID_NAME_MAP.get(e))}},{key:"clear",value:function(){this.cmpApiContext.gppModel.clear()}},{key:"getObject",value:function(){return this.cmpApiContext.gppModel.toObject()}},{key:"getGvlFromVendorList",value:function(e){return Ei.fromVendorList(e)}},{key:"getGvlFromUrl",value:(e=c(I().mark((function e(t){return I().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",Ei.fromUrl(t));case 1:case"end":return e.stop()}}),e)}))),function(t){return e.apply(this,arguments)})}]);var e}(),yi={tcfeuv2:2,tcfcav1:5,uspv1:6,usnat:7,usca:8,usva:9,usco:10,usut:11,usct:12},Ci=function(){function e(e,t){var n=this;this.setApplicableSections=function(e){if(n.gppEnabled)try{n.gppApi.setApplicableSections([yi[e]])}catch(t){throw new Error('GppData - setApplicableSections - unrecognized legalframework "'.concat(e,'"'))}},this.setCmpSignalReady=function(){return J(n,void 0,void 0,(function(){return q(this,(function(e){return this.gppEnabled&&this.gppApi.setSignalStatus(fr.READY),[2]}))}))},this.setCmpStatusLoaded=function(){return J(n,void 0,void 0,(function(){return q(this,(function(e){return this.gppEnabled&&this.gppApi.setCmpStatus(lr.LOADED),[2]}))}))},this.setCmpDisplayHidden=function(){return J(n,void 0,void 0,(function(){return q(this,(function(e){return this.gppEnabled&&this.gppApi.setCmpDisplayStatus(dr.HIDDEN),[2]}))}))},this.setCmpDisplayVisible=function(){return J(n,void 0,void 0,(function(){return q(this,(function(e){return this.gppEnabled&&this.gppApi.setCmpDisplayStatus(dr.VISIBLE),[2]}))}))},this.setSectionString=function(e,t){return J(n,void 0,void 0,(function(){return q(this,(function(n){return""!==e&&this.gppEnabled&&(this.gppApi.setSectionString(t,e),this.gppApi.fireSectionChange(t)),[2]}))}))},this.gppEnabled=t||!1,this.cmpId=(null==e?void 0:e.tcf2.cmpId)||5,this.cmpVersion=(null==e?void 0:e.tcf2.cmpVersion)||parseInt("3",10),this.gppApi=new Ii(this.cmpId,this.cmpVersion),this.gppApi.setCmpStatus(lr.LOADING),this.gppApi.setSignalStatus(fr.NOT_READY)}return e.getInstance=function(t,n){var r,i=(null===(r=e.instance)||void 0===r?void 0:r.gppEnabled)||!1;return n||i?(e.instance||(e.instance=new e(t,n)),e.instance):null},e.prototype.resetGpp=function(){this.gppApi={}},e}(),Ti=function(){function e(){this.primaryLanguage="",this.apiInstance=Xt.getInstance()}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.prototype.setPrimaryLanguage=function(e){this.primaryLanguage=e},e.prototype.getPrimaryLanguage=function(){return this.primaryLanguage},e.prototype.resolveLanguage=function(e){return void 0===e&&(e=!1),J(this,void 0,void 0,(function(){var t,n,r,i,s;return q(this,(function(o){switch(o.label){case 0:return t=ye.fetchUserCountryResponse(),n=[],t?($t.getInstance().setUserCountryData({code:t.countryCode,name:t.countryName,regionCode:t.regionCode}),[3,3]):[3,1];case 1:return[4,this.apiInstance.fetchAvailableLanguages()];case 2:n=o.sent(),o.label=3;case 3:if(!e){if(this.primaryLanguage)return this.apiInstance.setJsonFileLanguage(this.primaryLanguage),[2];if(r=ye.fetchLanguage())return this.apiInstance.setJsonFileLanguage(r),[2]}return n.length?[3,5]:[4,this.apiInstance.fetchAvailableLanguages()];case 4:n=o.sent(),o.label=5;case 5:return(i=Ai(n))?(this.apiInstance.setJsonFileLanguage(i),[2]):(s=bi(n))?(this.apiInstance.setJsonFileLanguage(s),[2]):n.length>0?(this.apiInstance.setJsonFileLanguage(n[0]),[2]):(this.apiInstance.setJsonFileLanguage(pn),[2])}}))}))},e}(),bi=function(e){var t=window.navigator;if(Ae(t.languages))for(var n=0;n<t.languages.length;n+=1){var r=Oi(e,t.languages[n]);if(r)return r}var i=null!=t.language?t.language:t.userLanguage;return Oi(e,i)},Ai=function(e){var t=document.documentElement.lang;return t?Oi(e,t):null},Oi=function(e,t){if(t){var n=t.toLowerCase().replace("-","_");if(Ce(e,n))return n;var r=t.slice(0,2);if(Ce(e,r))return r}return null};!function(e){e.API_INVALID_COMMAND="Usercentrics: (__uspapi) Invalid command: ",e.API_INVALID_CALLBACK="Usercentrics: (__uspapi) callback parameter not a function",e.INVALID_STRING="Usercentrics: ccpa string is invalid",e.RESHOW_AFTER_DAYS_INVALID="Usercentrics: reshow after days must be greater than 364.",e.SETTINGS_UNDEFINED="Usercentrics: ccpa is missing in setting"}(_i||(_i={})),function(e){e.ACCEPT="Y",e.DENY="N",e.UNKNOWN="-"}(mi||(mi={}));var Ni,wi,Ri,Li,Di,Pi=function(){function e(){this.apiInstance=Xt.getInstance()}return e.prototype.getTargets=function(e){var t=this;return e.reduce((function(e,n){if("mark"!==n.entryType&&"measure"!==n.entryType&&"paint"!==n.entryType&&!n.name.startsWith("data:")&&!["visible"].includes(n.name)&&!/^https:\/\/([a-z0-9.]+).usercentrics\.eu/.test(n.name)){var r=n.name,i=t.extractSubdomain(r);if(-1===e.indexOf(i))return Q(Q([],X(e),!1),[i],!1)}return e}),[])},e.prototype.initTagLogger=function(){var e=this;window.addEventListener("beforeunload",(function(){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e.updateLog()}))}))},e.prototype.extractSubdomain=function(e){var t,n=e.split("/");return e.indexOf("://")>-1?(t="".concat(n[2]),n[3]&&(t+="/".concat(n[3]))):(t="".concat(n[0]),n[1]&&(t+="/".concat(n[1]))),"".concat(t.split("?")[0])},e.prototype.updateLog=function(){if(Object.prototype.hasOwnProperty.call(window,"performance")&&"function"==typeof performance.getEntries){var e=window.performance.getEntries();this.apiInstance.updateTagLoggerData(this.getTargets(e))}else console.log("UC Warning: tag logger functionality is not supported by your browser.")},e}(),Vi=function(){function e(){this.initOptions=null,this.isFirstTimePageVisit=!0,this.selectedLayer=null,this.shouldAcceptAllImplicitly=null,this.shouldShowFirstLayerOnVersionChange=!1,this.variant=null,this.ampInstance=Et.getInstance(),this.settingsV2=jn.getInstance(),this.rulesetInstance=Pn.getInstance(),this.apiInstance=Xt.getInstance()}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.resetInstance=function(){e.instance.selectedLayer=null,e.instance.variant=null,e.instance.shouldAcceptAllImplicitly=null,e.instance.initOptions=null},e.prototype.init=function(e){return J(this,void 0,void 0,(function(){var t;return q(this,(function(n){switch(n.label){case 0:return this.initOptions=e,this.isFirstTimePageVisit=this.isFirstTimeVisit(),null!==this.shouldAcceptAllImplicitly?[3,2]:(t=this,[4,this.settingsV2.shouldAcceptAllImplicitlyOnInit()]);case 1:t.shouldAcceptAllImplicitly=n.sent(),n.label=2;case 2:return this.shouldShowFirstLayerOnVersionChange=this.settingsV2.shouldShowFirstLayerOnVersionChange(),[2]}}))}))},e.prototype.isFirstTimeVisit=function(){return!ye.settingsExist()},e.prototype.shouldShowNone=function(){return J(this,void 0,void 0,(function(){var e,t;return q(this,(function(n){switch(n.label){case 0:return e=0===this.variant,null!==this.shouldAcceptAllImplicitly?[3,2]:(t=this,[4,this.settingsV2.shouldAcceptAllImplicitlyOnInit()]);case 1:t.shouldAcceptAllImplicitly=n.sent(),n.label=2;case 2:return this.apiInstance.getRulesetId()&&this.rulesetInstance.getIsUsingNoShow()?[2,this.rulesetInstance.getNoShow()]:[2,this.shouldAcceptAllImplicitly&&!e]}}))}))},e.prototype.shouldManuallyResurface=function(){var e=this.settingsV2.core,t=ye.fetchServices(),n=0===this.variant,r=null;if(t&&t.length>0){if(n)r=ye.getCcpaTimeStamp();else{var i=je(t.reduce((function(e,t){return Q(Q([],X(e),!1),X(t.history),!1)}),[]));e&&i.length>0&&(r=i[0].timestamp)}if(e&&r){var s=e.renewConsentsTimestamp;if(s&&Date.now()/1e3>s&&r/1e3<s)return!0}}return!1},e.prototype.shouldShowFirstLayer=function(e){var t,n,r,i=this.ampInstance.isAmpEnabled(),s=0===this.variant,o=(null===(t=e.ccpa)||void 0===t?void 0:t.showOnPageLoad)||!1,a=s?o:!this.shouldAcceptAllImplicitly;if(this.shouldManuallyResurface())return!0;if(this.shouldAcceptAllImplicitly&&!s)return!1;if(a&&this.isFirstTimePageVisit||this.shouldShowFirstLayerOnVersionChange||!ye.fetchUserActionPerformed()&&(!s||o)||i)return!0;switch(this.variant){case 0:if(this.shouldShowFirstLayerForCcpa(null===(n=e.ccpa)||void 0===n?void 0:n.reshowCMP,null===(r=e.ccpa)||void 0===r?void 0:r.reshowAfterDays))return!0;break;case 2:var c=this.settingsV2.getTcfData();if(null==c?void 0:c.shouldResurfaceUI())return!0;break;default:if(this.shouldForceReshowGDPRBanner())return!0}return!1},e.prototype.shouldForceReshowGDPRBanner=function(){var e=this.settingsV2.core;if(!e)return!1;var t,n=e.reshowBanner,r=this.settingsV2.getServicesData(),i=je(r.reduce((function(e,t){return Q(Q([],X(e),!1),X(t.consent.history),!1)}),[]));if(n>0&&i.length>0){var s=new Date(i[0].timestamp);return s.setMonth(s.getMonth()+n),t=s,(new Date).getTime()-t.getTime()>=0}return!1},e.prototype.shouldShowPrivacyButton=function(e){var t=window.location.href,n=!e.privacyButtonUrls||0===e.privacyButtonUrls.contains.length;return e.privacyButtonUrls&&e.privacyButtonUrls.contains.length>0&&e.privacyButtonUrls.contains.some((function(e){return t.includes(e)}))&&(n=!0),e.privacyButtonIsVisible&&n},e.prototype.resolveUiVariant=function(e){return J(this,void 0,void 0,(function(){return q(this,(function(t){switch(t.label){case 0:return null!==this.variant?[2,this.variant]:[4,this.settingsV2.isCcpaAvailable()];case 1:return t.sent()?this.variant=0:this.variant=e?2:1,[2,this.variant]}}))}))},e.prototype.resolveUiInitialLayer=function(e){return J(this,void 0,void 0,(function(){return q(this,(function(t){switch(t.label){case 0:return[4,this.shouldShowNone()];case 1:return t.sent()?[2,1]:this.shouldShowFirstLayer(e)?[2,0]:this.shouldShowPrivacyButton(e)?[2,2]:[2,1]}}))}))},e.prototype.resolveUIOptions=function(e){var t;return J(this,void 0,void 0,(function(){var n,r,i,s;return q(this,(function(o){switch(o.label){case 0:return n=this.ampInstance.isAmpEnabled(),null!==this.variant?[3,2]:(r=this,[4,this.resolveUiVariant(e.tcf2Enabled)]);case 1:r.variant=o.sent(),o.label=2;case 2:return 0!==this.variant&&ye.clearCcpa(),2!==this.variant&&ye.clearTcf(),i=this,(null===(t=this.initOptions)||void 0===t?void 0:t.suppressCmpDisplay)?(s=1,[3,5]):[3,3];case 3:return[4,this.resolveUiInitialLayer(e)];case 4:s=o.sent(),o.label=5;case 5:return i.selectedLayer=s,[2,{ampEnabled:n,initialLayer:this.selectedLayer,variant:this.variant}]}}))}))},e.prototype.shouldShowFirstLayerForCcpa=function(e,t){var n;void 0===t&&(t=365);var r,i=this.settingsV2.legacySettings,s=(r=ye.getCcpaData())?((new Date).getTime()-r.timestamp)/864e5:0;return i||void 0!==e?null!==(n=null!=e?e:null==i?void 0:i.ccpa.reshowCMP)&&void 0!==n&&n&&s>t:s>t},e}(),Ui=Object.freeze({initialize:function({modulePath:e=".",importFunctionName:t="__import__"}={}){try{self[t]=new Function("u","return import(u)")}catch(n){const r=new URL(e,location),i=e=>{URL.revokeObjectURL(e.src),e.remove()};self[t]=e=>new Promise(((n,s)=>{const o=new URL(e,r);if(self[t].moduleMap[o])return n(self[t].moduleMap[o]);const a=new Blob([`import * as m from '${o}';`,`${t}.moduleMap['${o}']=m;`],{type:"text/javascript"}),c=Object.assign(document.createElement("script"),{type:"module",src:URL.createObjectURL(a),onerror(){s(new Error(`Failed to import: ${e}`)),i(c)},onload(){n(self[t].moduleMap[o]),i(c)}});document.head.appendChild(c)})),self[t].moduleMap={}}}});function ki(e,t){return Object.keys(t).forEach((function(n){"default"===n||"__esModule"===n||Object.prototype.hasOwnProperty.call(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[n]}})})),e}function Mi(e,t,n,r){Object.defineProperty(e,t,{get:n,set:r,enumerable:!0,configurable:!0})}var Fi={},xi={};Mi(xi,"Base64Url",(function(){return Ji}));var Gi={},Bi={};Mi(Bi,"DecodingError",(function(){return Hi}));var Hi=function(){function e(t){var n;return l(this,e),(n=u(this,e,[t])).name="DecodingError",n}return S(e,N(Error)),p(e)}(),ji={};Mi(ji,"EncodingError",(function(){return $i}));var $i=function(){function e(t){var n;return l(this,e),(n=u(this,e,[t])).name="EncodingError",n}return S(e,N(Error)),p(e)}(),Yi={};Mi(Yi,"GVLError",(function(){return Wi}));var Wi=function(){function e(t){var n;return l(this,e),(n=u(this,e,[t])).name="GVLError",n}return S(e,N(Error)),p(e)}(),zi={};Mi(zi,"TCModelError",(function(){return Ki}));var Ki=function(){function e(t,n){var r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return l(this,e),(r=u(this,e,["invalid value ".concat(n," passed for ").concat(t," ").concat(i)])).name="TCModelError",r}return S(e,N(Error)),p(e)}();ki(Gi,Bi),ki(Gi,ji),ki(Gi,Yi),ki(Gi,zi);var Ji=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){if(!/^[0-1]+$/.test(e))throw new $i("Invalid bitField");var t=e.length%this.LCM;e+=t?"0".repeat(this.LCM-t):"";for(var n="",r=0;r<e.length;r+=this.BASIS)n+=this.DICT[parseInt(e.substr(r,this.BASIS),2)];return n}},{key:"decode",value:function(e){if(!/^[A-Za-z0-9\-_]+$/.test(e))throw new Hi("Invalidly encoded Base64URL string");for(var t="",n=0;n<e.length;n++){var r=this.REVERSE_DICT.get(e[n]).toString(2);t+="0".repeat(this.BASIS-r.length)+r}return t}}]);(Ni=Ji).DICT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",Ni.REVERSE_DICT=new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]]),Ni.BASIS=6,Ni.LCM=24;var qi={};Mi(qi,"BitLength",(function(){return ks}));var Xi={},Qi={};Mi(Qi,"ConsentLanguages",(function(){return Zi}));var Zi=function(){function e(){l(this,e)}return p(e,[{key:"has",value:function(t){return e.langSet.has(t)}},{key:"parseLanguage",value:function(t){var n=(t=t.toUpperCase()).split("-")[0];if(t.length>=2&&2==n.length){if(e.langSet.has(t))return t;if(e.langSet.has(n))return n;var r=n+"-"+n;if(e.langSet.has(r))return r;var i,s=f(e.langSet);try{for(s.s();!(i=s.n()).done;){var o=i.value;if(-1!==o.indexOf(t)||-1!==o.indexOf(n))return o}}catch(e){s.e(e)}finally{s.f()}}throw new Error("unsupported language ".concat(t))}},{key:"forEach",value:function(t){e.langSet.forEach(t)}},{key:"size",get:function(){return e.langSet.size}}])}();Zi.langSet=new Set(["AR","BG","BS","CA","CS","CY","DA","DE","EL","EN","ES","ET","EU","FI","FR","GL","HE","HI","HR","HU","ID","IT","JA","KA","KO","LT","LV","MK","MS","MT","NL","NO","PL","PT-BR","PT-PT","RO","RU","SK","SL","SQ","SR-LATN","SR-CYRL","SV","SW","TH","TL","TR","UK","VI","ZH","ZH-HANT"]);var es={};Mi(es,"Fields",(function(){return ts}));var ts=p((function e(){l(this,e)}));(wi=ts).cmpId="cmpId",wi.cmpVersion="cmpVersion",wi.consentLanguage="consentLanguage",wi.consentScreen="consentScreen",wi.created="created",wi.supportOOB="supportOOB",wi.isServiceSpecific="isServiceSpecific",wi.lastUpdated="lastUpdated",wi.numCustomPurposes="numCustomPurposes",wi.policyVersion="policyVersion",wi.publisherCountryCode="publisherCountryCode",wi.publisherCustomConsents="publisherCustomConsents",wi.publisherCustomLegitimateInterests="publisherCustomLegitimateInterests",wi.publisherLegitimateInterests="publisherLegitimateInterests",wi.publisherConsents="publisherConsents",wi.publisherRestrictions="publisherRestrictions",wi.purposeConsents="purposeConsents",wi.purposeLegitimateInterests="purposeLegitimateInterests",wi.purposeOneTreatment="purposeOneTreatment",wi.specialFeatureOptins="specialFeatureOptins",wi.useNonStandardTexts="useNonStandardTexts",wi.vendorConsents="vendorConsents",wi.vendorLegitimateInterests="vendorLegitimateInterests",wi.vendorListVersion="vendorListVersion",wi.vendorsAllowed="vendorsAllowed",wi.vendorsDisclosed="vendorsDisclosed",wi.version="version";var ns={};Mi(ns,"PurposeRestriction",(function(){return os})),Mi({},"Cloneable",(function(){return rs}));var rs=p((function e(){l(this,e)}),[{key:"clone",value:function(){var e=this,t=new this.constructor;return Object.keys(this).forEach((function(n){var r=e.deepClone(e[n]);void 0!==r&&(t[n]=r)})),t}},{key:"deepClone",value:function(e){var t=A(e);if("number"===t||"string"===t||"boolean"===t)return e;if(null!==e&&"object"===t){if("function"==typeof e.clone)return e.clone();if(e instanceof Date)return new Date(e.getTime());if(void 0!==e[Symbol.iterator]){var n,r=[],i=f(e);try{for(i.s();!(n=i.n()).done;){var s=n.value;r.push(this.deepClone(s))}}catch(e){i.e(e)}finally{i.f()}return e instanceof Array?r:new e.constructor(r)}var o={};for(var a in e)e.hasOwnProperty(a)&&(o[a]=this.deepClone(e[a]));return o}}}]),is={};Mi(is,"RestrictionType",(function(){return ss}));var ss=function(e){return e[e.NOT_ALLOWED=0]="NOT_ALLOWED",e[e.REQUIRE_CONSENT=1]="REQUIRE_CONSENT",e[e.REQUIRE_LI=2]="REQUIRE_LI",e}({}),os=function(){function e(t,n){var r;return l(this,e),r=u(this,e),void 0!==t&&(r.purposeId=t),void 0!==n&&(r.restrictionType=n),r}return S(e,rs),p(e,[{key:"hash",get:function(){if(!this.isValid())throw new Error("cannot hash invalid PurposeRestriction");return"".concat(this.purposeId).concat(e.hashSeparator).concat(this.restrictionType)}},{key:"purposeId",get:function(){return this.purposeId_},set:function(e){this.purposeId_=e}},{key:"isValid",value:function(){return Number.isInteger(this.purposeId)&&this.purposeId>0&&(this.restrictionType===ss.NOT_ALLOWED||this.restrictionType===ss.REQUIRE_CONSENT||this.restrictionType===ss.REQUIRE_LI)}},{key:"isSameAs",value:function(e){return this.purposeId===e.purposeId&&this.restrictionType===e.restrictionType}}],[{key:"unHash",value:function(t){var n=t.split(this.hashSeparator),r=new e;if(2!==n.length)throw new Ki("hash",t);return r.purposeId=parseInt(n[0],10),r.restrictionType=parseInt(n[1],10),r}}])}();os.hashSeparator="-";var as={};Mi(as,"PurposeRestrictionVector",(function(){return cs}));var cs=function(){function e(){var t;l(this,e);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(t=u(this,e,[].concat(r))).bitLength=0,t.map=new Map,t}return S(e,rs),p(e,[{key:"has",value:function(e){return this.map.has(e)}},{key:"isOkToHave",value:function(e,t,n){var r,i=!0;if(null!==(r=this.gvl)&&void 0!==r&&r.vendors){var s=this.gvl.vendors[n];if(s)if(e===ss.NOT_ALLOWED)i=s.legIntPurposes.includes(t)||s.purposes.includes(t);else if(s.flexiblePurposes.length)switch(e){case ss.REQUIRE_CONSENT:i=s.flexiblePurposes.includes(t)&&s.legIntPurposes.includes(t);break;case ss.REQUIRE_LI:i=s.flexiblePurposes.includes(t)&&s.purposes.includes(t)}else i=!1;else i=!1}return i}},{key:"add",value:function(e,t){if(this.isOkToHave(t.restrictionType,t.purposeId,e)){var n=t.hash;this.has(n)||(this.map.set(n,new Set),this.bitLength=0),this.map.get(n).add(e)}}},{key:"restrictPurposeToLegalBasis",value:function(e){var t=Array.from(this.gvl.vendorIds),n=e.hash,r=t[t.length-1],i=T(Array(r).keys()).map((function(e){return e+1}));if(this.has(n))for(var s=1;s<=r;s++)this.map.get(n).add(s);else this.map.set(n,new Set(i)),this.bitLength=0}},{key:"getVendors",value:function(e){var t=[];if(e){var n=e.hash;this.has(n)&&(t=Array.from(this.map.get(n)))}else{var r=new Set;this.map.forEach((function(e){e.forEach((function(e){r.add(e)}))})),t=Array.from(r)}return t.sort((function(e,t){return e-t}))}},{key:"getRestrictionType",value:function(e,t){var n;return this.getRestrictions(e).forEach((function(e){e.purposeId===t&&(void 0===n||n>e.restrictionType)&&(n=e.restrictionType)})),n}},{key:"vendorHasRestriction",value:function(e,t){for(var n=!1,r=this.getRestrictions(e),i=0;i<r.length&&!n;i++)n=t.isSameAs(r[i]);return n}},{key:"getMaxVendorId",value:function(){var e=0;return this.map.forEach((function(t){e=Math.max(Array.from(t)[t.size-1],e)})),e}},{key:"getRestrictions",value:function(e){var t=[];return this.map.forEach((function(n,r){e?n.has(e)&&t.push(os.unHash(r)):t.push(os.unHash(r))})),t}},{key:"getPurposes",value:function(){var e=new Set;return this.map.forEach((function(t,n){e.add(os.unHash(n).purposeId)})),Array.from(e)}},{key:"remove",value:function(e,t){var n=t.hash,r=this.map.get(n);r&&(r.delete(e),0==r.size&&(this.map.delete(n),this.bitLength=0))}},{key:"gvl",get:function(){return this.gvl_},set:function(e){var t=this;this.gvl_||(this.gvl_=e,this.map.forEach((function(e,n){var r=os.unHash(n);Array.from(e).forEach((function(n){t.isOkToHave(r.restrictionType,r.purposeId,n)||e.delete(n)}))})))}},{key:"isEmpty",value:function(){return 0===this.map.size}},{key:"numRestrictions",get:function(){return this.map.size}}])}(),us={};Mi(us,"DeviceDisclosureStorageAccessType",(function(){return ls}));var ls=function(e){return e.COOKIE="cookie",e.WEB="web",e.APP="app",e}({}),ds={};Mi(ds,"Segment",(function(){return ps}));var ps=function(e){return e.CORE="core",e.VENDORS_DISCLOSED="vendorsDisclosed",e.VENDORS_ALLOWED="vendorsAllowed",e.PUBLISHER_TC="publisherTC",e}({}),fs={};Mi(fs,"SegmentIDs",(function(){return hs}));var hs=p((function e(){l(this,e)}));(Ri=hs).ID_TO_KEY=[ps.CORE,ps.VENDORS_DISCLOSED,ps.VENDORS_ALLOWED,ps.PUBLISHER_TC],Ri.KEY_TO_ID=h(h(h(h({},ps.CORE,0),ps.VENDORS_DISCLOSED,1),ps.VENDORS_ALLOWED,2),ps.PUBLISHER_TC,3);var gs={};Mi(gs,"Vector",(function(){return Vs}));var vs,Ss,Es,_s,ms,Is,ys,Cs,Ts,bs,As,Os,Ns,ws,Rs,Ls,Ds,Ps,Vs=function(){function e(){var t;l(this,e);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(t=u(this,e,[].concat(r))).bitLength=0,t.maxId_=0,t.set_=new Set,t}return S(e,rs),p(e,[{key:Symbol.iterator,value:I().mark((function e(){var t;return I().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=1;case 1:if(!(t<=this.maxId)){e.next=7;break}return e.next=4,[t,this.has(t)];case 4:t++,e.next=1;break;case 7:case"end":return e.stop()}}),e,this)}))},{key:"values",value:function(){return this.set_.values()}},{key:"maxId",get:function(){return this.maxId_}},{key:"has",value:function(e){return this.set_.has(e)}},{key:"unset",value:function(e){var t=this;Array.isArray(e)?e.forEach((function(e){return t.unset(e)})):"object"===A(e)?this.unset(Object.keys(e).map((function(e){return Number(e)}))):(this.set_.delete(Number(e)),this.bitLength=0,e===this.maxId&&(this.maxId_=0,this.set_.forEach((function(e){t.maxId_=Math.max(t.maxId,e)}))))}},{key:"isIntMap",value:function(e){var t=this,n="object"===A(e);return n&&Object.keys(e).every((function(n){var r=Number.isInteger(parseInt(n,10));return(r=r&&t.isValidNumber(e[n].id))&&void 0!==e[n].name}))}},{key:"isValidNumber",value:function(e){return parseInt(e,10)>0}},{key:"isSet",value:function(e){var t=!1;return e instanceof Set&&(t=Array.from(e).every(this.isValidNumber)),t}},{key:"set",value:function(e){var t=this;if(Array.isArray(e))e.forEach((function(e){return t.set(e)}));else if(this.isSet(e))this.set(Array.from(e));else if(this.isIntMap(e))this.set(Object.keys(e).map((function(e){return Number(e)})));else{if(!this.isValidNumber(e))throw new Ki("set()",e,"must be positive integer array, positive integer, Set<number>, or IntMap");this.set_.add(e),this.maxId_=Math.max(this.maxId,e),this.bitLength=0}}},{key:"empty",value:function(){this.set_=new Set}},{key:"forEach",value:function(e){for(var t=1;t<=this.maxId;t++)e(this.has(t),t)}},{key:"size",get:function(){return this.set_.size}},{key:"setAll",value:function(e){this.set(e)}}])}(),Us={};ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Us,{}),ki(Xi,Qi),ki(Xi,es),ki(Xi,{}),ki(Xi,{}),ki(Xi,ns),ki(Xi,as),ki(Xi,us),ki(Xi,{}),ki(Xi,is),ki(Xi,ds),ki(Xi,fs),ki(Xi,gs),ki(Xi,Us);var ks=p((function e(){l(this,e)}));Li=ks,vs=ts.cmpId,Ss=ts.cmpVersion,Es=ts.consentLanguage,_s=ts.consentScreen,ms=ts.created,Is=ts.isServiceSpecific,ys=ts.lastUpdated,Cs=ts.policyVersion,Ts=ts.publisherCountryCode,bs=ts.publisherLegitimateInterests,As=ts.publisherConsents,Os=ts.purposeConsents,Ns=ts.purposeLegitimateInterests,ws=ts.purposeOneTreatment,Rs=ts.specialFeatureOptins,Ls=ts.useNonStandardTexts,Ds=ts.vendorListVersion,Ps=ts.version,Li[vs]=12,Li[Ss]=12,Li[Es]=12,Li[_s]=6,Li[ms]=36,Li[Is]=1,Li[ys]=36,Li[Cs]=6,Li[Ts]=12,Li[bs]=24,Li[As]=24,Li[Os]=24,Li[Ns]=24,Li[ws]=1,Li[Rs]=12,Li[Ls]=1,Li[Ds]=12,Li[Ps]=6,Li.anyBoolean=1,Li.encodingType=1,Li.maxId=16,Li.numCustomPurposes=6,Li.numEntries=12,Li.numRestrictions=12,Li.purposeId=6,Li.restrictionType=2,Li.segmentType=3,Li.singleOrRange=1,Li.vendorId=16;var Ms={};Mi(Ms,"SegmentEncoder",(function(){return co}));var Fs={},xs={};Mi(xs,"BooleanEncoder",(function(){return Gs}));var Gs=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){return String(Number(e))}},{key:"decode",value:function(e){return"1"===e}}]),Bs={};Mi(Bs,"DateEncoder",(function(){return $s}));var Hs={};Mi(Hs,"IntEncoder",(function(){return js}));var js=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){var n;if("string"==typeof e&&(e=parseInt(e,10)),(n=e.toString(2)).length>t||e<0)throw new $i("".concat(e," too large to encode into ").concat(t));return n.length<t&&(n="0".repeat(t-n.length)+n),n}},{key:"decode",value:function(e,t){if(t!==e.length)throw new Hi("invalid bit length");return parseInt(e,2)}}]),$s=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){return js.encode(Math.round(e.getTime()/100),t)}},{key:"decode",value:function(e,t){if(t!==e.length)throw new Hi("invalid bit length");var n=new Date;return n.setTime(100*js.decode(e,t)),n}}]),Ys={};Mi(Ys,"FieldEncoderMap",(function(){return no}));var Ws={};Mi(Ws,"FixedVectorEncoder",(function(){return zs}));var zs=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){for(var n="",r=1;r<=t;r++)n+=Gs.encode(e.has(r));return n}},{key:"decode",value:function(e,t){if(e.length!==t)throw new Hi("bitfield encoding length mismatch");for(var n=new Vs,r=1;r<=t;r++)Gs.decode(e[r-1])&&n.set(r);return n.bitLength=e.length,n}}]),Ks={};Mi(Ks,"LangEncoder",(function(){return Js}));var Js=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){var n=(e=e.toUpperCase()).charCodeAt(0)-65,r=e.charCodeAt(1)-65;if(n<0||n>25||r<0||r>25)throw new $i("invalid language code: ".concat(e));if(t%2==1)throw new $i("numBits must be even, ".concat(t," is not valid"));return t/=2,js.encode(n,t)+js.encode(r,t)}},{key:"decode",value:function(e,t){if(t!==e.length||e.length%2)throw new Hi("invalid bit length for language");var n=e.length/2,r=js.decode(e.slice(0,n),n)+65,i=js.decode(e.slice(n),n)+65;return String.fromCharCode(r)+String.fromCharCode(i)}}]),qs={};Mi(qs,"PurposeRestrictionVectorEncoder",(function(){return Xs}));var Xs=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){var t=js.encode(e.numRestrictions,ks.numRestrictions);if(!e.isEmpty()){var n=function(t,n){for(var r=t+1;r<=n;r++)if(e.gvl.vendorIds.has(r))return r;return t};e.getRestrictions().forEach((function(r){t+=js.encode(r.purposeId,ks.purposeId),t+=js.encode(r.restrictionType,ks.restrictionType);for(var i=e.getVendors(r),s=i.length,o=0,a=0,c="",u=0;u<s;u++){var l=i[u];if(0===a&&(o++,a=l),u===s-1||i[u+1]>n(l,i[s-1])){var d=!(l===a);c+=Gs.encode(d),c+=js.encode(a,ks.vendorId),d&&(c+=js.encode(l,ks.vendorId)),a=0}}t+=js.encode(o,ks.numEntries),t+=c}))}return t}},{key:"decode",value:function(e){var t=0,n=new cs,r=js.decode(e.substr(t,ks.numRestrictions),ks.numRestrictions);t+=ks.numRestrictions;for(var i=0;i<r;i++){var s=js.decode(e.substr(t,ks.purposeId),ks.purposeId);t+=ks.purposeId;var o=js.decode(e.substr(t,ks.restrictionType),ks.restrictionType);t+=ks.restrictionType;var a=new os(s,o),c=js.decode(e.substr(t,ks.numEntries),ks.numEntries);t+=ks.numEntries;for(var u=0;u<c;u++){var l=Gs.decode(e.substr(t,ks.anyBoolean));t+=ks.anyBoolean;var d=js.decode(e.substr(t,ks.vendorId),ks.vendorId);if(t+=ks.vendorId,l){var p=js.decode(e.substr(t,ks.vendorId),ks.vendorId);if(t+=ks.vendorId,p<d)throw new Hi("Invalid RangeEntry: endVendorId ".concat(p," is less than ").concat(d));for(var f=d;f<=p;f++)n.add(f,a)}else n.add(d,a)}}return n.bitLength=t,n}}]),Qs={};Mi(Qs,"VendorVectorEncoder",(function(){return to}));var Zs={};Mi(Zs,"VectorEncodingType",(function(){return eo}));var eo=function(e){return e[e.FIELD=0]="FIELD",e[e.RANGE=1]="RANGE",e}({}),to=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e){var t,n=[],r=[],i=js.encode(e.maxId,ks.maxId),s="",o=ks.maxId+ks.encodingType,a=o+e.maxId,c=2*ks.vendorId+ks.singleOrRange+ks.numEntries,u=o+ks.numEntries;return e.forEach((function(i,o){s+=Gs.encode(i),(t=e.maxId>c&&u<a)&&i&&(e.has(o+1)?0===r.length&&(r.push(o),u+=ks.singleOrRange,u+=ks.vendorId):(r.push(o),u+=ks.vendorId,n.push(r),r=[]))})),t?(i+=String(eo.RANGE),i+=this.buildRangeEncoding(n)):(i+=String(eo.FIELD),i+=s),i}},{key:"decode",value:function(e,t){var n,r=0,i=js.decode(e.substr(r,ks.maxId),ks.maxId);r+=ks.maxId;var s=js.decode(e.charAt(r),ks.encodingType);if(r+=ks.encodingType,s===eo.RANGE){if(n=new Vs,1===t){if("1"===e.substr(r,1))throw new Hi("Unable to decode default consent=1");r++}var o=js.decode(e.substr(r,ks.numEntries),ks.numEntries);r+=ks.numEntries;for(var a=0;a<o;a++){var c=Gs.decode(e.charAt(r));r+=ks.singleOrRange;var u=js.decode(e.substr(r,ks.vendorId),ks.vendorId);if(r+=ks.vendorId,c){var l=js.decode(e.substr(r,ks.vendorId),ks.vendorId);r+=ks.vendorId;for(var d=u;d<=l;d++)n.set(d)}else n.set(u)}}else{var p=e.substr(r,i);r+=i,n=zs.decode(p,i)}return n.bitLength=r,n}},{key:"buildRangeEncoding",value:function(e){var t=e.length,n=js.encode(t,ks.numEntries);return e.forEach((function(e){var t=1===e.length;n+=Gs.encode(!t),n+=js.encode(e[0],ks.vendorId),t||(n+=js.encode(e[1],ks.vendorId))})),n}}]);function no(){var e;return h(h(h(h(h(h(h(h(h(h(e={},ts.version,js),ts.created,$s),ts.lastUpdated,$s),ts.cmpId,js),ts.cmpVersion,js),ts.consentScreen,js),ts.consentLanguage,Js),ts.vendorListVersion,js),ts.policyVersion,js),ts.isServiceSpecific,Gs),h(h(h(h(h(h(h(h(h(h(e,ts.useNonStandardTexts,Gs),ts.specialFeatureOptins,zs),ts.purposeConsents,zs),ts.purposeLegitimateInterests,zs),ts.purposeOneTreatment,Gs),ts.publisherCountryCode,Js),ts.vendorConsents,to),ts.vendorLegitimateInterests,to),ts.publisherRestrictions,Xs),"segmentType",js),h(h(h(h(h(h(h(e,ts.vendorsDisclosed,to),ts.vendorsAllowed,to),ts.publisherConsents,zs),ts.publisherLegitimateInterests,zs),ts.numCustomPurposes,js),ts.publisherCustomConsents,zs),ts.publisherCustomLegitimateInterests,zs)}ki(Fs,xs),ki(Fs,Bs),ki(Fs,Ys),ki(Fs,Ws),ki(Fs,Hs),ki(Fs,Ks),ki(Fs,qs),ki(Fs,Zs),ki(Fs,Qs);var ro={},io={};Mi(io,"FieldSequence",(function(){return so}));var so=p((function e(){l(this,e),this[1]=h({},ps.CORE,[ts.version,ts.created,ts.lastUpdated,ts.cmpId,ts.cmpVersion,ts.consentScreen,ts.consentLanguage,ts.vendorListVersion,ts.purposeConsents,ts.vendorConsents]),this[2]=h(h(h(h({},ps.CORE,[ts.version,ts.created,ts.lastUpdated,ts.cmpId,ts.cmpVersion,ts.consentScreen,ts.consentLanguage,ts.vendorListVersion,ts.policyVersion,ts.isServiceSpecific,ts.useNonStandardTexts,ts.specialFeatureOptins,ts.purposeConsents,ts.purposeLegitimateInterests,ts.purposeOneTreatment,ts.publisherCountryCode,ts.vendorConsents,ts.vendorLegitimateInterests,ts.publisherRestrictions]),ps.PUBLISHER_TC,[ts.publisherConsents,ts.publisherLegitimateInterests,ts.numCustomPurposes,ts.publisherCustomConsents,ts.publisherCustomLegitimateInterests]),ps.VENDORS_ALLOWED,[ts.vendorsAllowed]),ps.VENDORS_DISCLOSED,[ts.vendorsDisclosed])})),oo={};Mi(oo,"SegmentSequence",(function(){return ao}));var ao=p((function e(t,n){if(l(this,e),this[1]=[ps.CORE],this[2]=[ps.CORE],2===t.version)if(t.isServiceSpecific)this[2].push(ps.PUBLISHER_TC);else{var r=!(!n||!n.isForVendors);r&&!0!==t[ts.supportOOB]||this[2].push(ps.VENDORS_DISCLOSED),r&&(t[ts.supportOOB]&&t[ts.vendorsAllowed].size>0&&this[2].push(ps.VENDORS_ALLOWED),this[2].push(ps.PUBLISHER_TC))}}));ki(ro,io),ki(ro,oo),ki(ro,{});var co=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){var n,r=this;try{n=this.fieldSequence[String(e.version)][t]}catch(n){throw new $i("Unable to encode version: ".concat(e.version,", segment: ").concat(t))}var i="";t!==ps.CORE&&(i=js.encode(hs.KEY_TO_ID[t],ks.segmentType));var s=no();return n.forEach((function(n){var o=e[n],a=s[n],c=ks[n];void 0===c&&r.isPublisherCustom(n)&&(c=Number(e[ts.numCustomPurposes]));try{i+=a.encode(o,c)}catch(e){throw new $i("Error encoding ".concat(t,"->").concat(n,": ").concat(e.message))}})),Ji.encode(i)}},{key:"decode",value:function(e,t,n){var r=this,i=Ji.decode(e),s=0;n===ps.CORE&&(t.version=js.decode(i.substr(s,ks[ts.version]),ks[ts.version])),n!==ps.CORE&&(s+=ks.segmentType);var o=this.fieldSequence[String(t.version)][n],a=no();return o.forEach((function(e){var n=a[e],o=ks[e];if(void 0===o&&r.isPublisherCustom(e)&&(o=Number(t[ts.numCustomPurposes])),0!==o){var c=i.substr(s,o);if(t[e]=n===to?n.decode(c,t.version):n.decode(c,o),Number.isInteger(o))s+=o;else{if(!Number.isInteger(t[e].bitLength))throw new Hi(e);s+=t[e].bitLength}}})),t}},{key:"isPublisherCustom",value:function(e){return 0===e.indexOf("publisherCustom")}}]);co.fieldSequence=new so;var uo={};Mi(uo,"SemanticPreEncoder",(function(){return lo}));var lo=p((function e(){l(this,e)}),null,[{key:"process",value:function(e,t){var n=e.gvl;if(!n)throw new $i("Unable to encode TCModel without a GVL");if(!n.isReady)throw new $i("Unable to encode TCModel tcModel.gvl.readyPromise is not resolved");(e=e.clone()).consentLanguage=n.language.slice(0,2).toUpperCase(),(null==t?void 0:t.version)>0&&(null==t?void 0:t.version)<=this.processor.length?e.version=t.version:e.version=this.processor.length;var r=e.version-1;if(!this.processor[r])throw new $i("Invalid version: ".concat(e.version));return this.processor[r](e,n)}}]);lo.processor=[function(e){return e},function(e,t){e.publisherRestrictions.gvl=t,e.purposeLegitimateInterests.unset([1,3,4,5,6]);var n=new Map;return n.set("legIntPurposes",e.vendorLegitimateInterests),n.set("purposes",e.vendorConsents),n.forEach((function(n,r){n.forEach((function(i,s){if(i){var o=t.vendors[s];if(!o||o.deletedDate)n.unset(s);else if(0===o[r].length)if("legIntPurposes"===r&&0===o.purposes.length&&0===o.legIntPurposes.length&&o.specialPurposes.length>0)n.set(s);else if("legIntPurposes"===r&&o.purposes.length>0&&0===o.legIntPurposes.length&&o.specialPurposes.length>0)n.set(s);else if(e.isServiceSpecific)if(0===o.flexiblePurposes.length)n.unset(s);else{for(var a=e.publisherRestrictions.getRestrictions(s),c=!1,u=0,l=a.length;u<l&&!c;u++)c=a[u].restrictionType===ss.REQUIRE_CONSENT&&"purposes"===r||a[u].restrictionType===ss.REQUIRE_LI&&"legIntPurposes"===r;c||n.unset(s)}else n.unset(s)}}))})),e.vendorsDisclosed.set(t.vendors),e}],ki(Fi,xi),ki(Fi,qi),ki(Fi,{}),ki(Fi,Ms),ki(Fi,uo),ki(Fi,Fs),ki(Fi,ro),Mi({},"GVL",(function(){return fo})),Mi({},"Json",(function(){return po}));var po=p((function e(){l(this,e)}),null,[{key:"absCall",value:function(e,t,n,r){return new Promise((function(i,s){var o=new XMLHttpRequest;o.withCredentials=n,o.addEventListener("load",(function(){if(o.readyState==XMLHttpRequest.DONE)if(o.status>=200&&o.status<300){var e=o.response;if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}i(e)}else s(new Error("HTTP Status: ".concat(o.status," response type: ").concat(o.responseType)))})),o.addEventListener("error",(function(){s(new Error("error"))})),o.addEventListener("abort",(function(){s(new Error("aborted"))})),null===t?o.open("GET",e,!0):o.open("POST",e,!0),o.responseType="json",o.timeout=r,o.ontimeout=function(){s(new Error("Timeout "+r+"ms "+e))},o.send(t)}))}},{key:"post",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;return this.absCall(e,JSON.stringify(t),n,r)}},{key:"fetch",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return this.absCall(e,null,t,n)}}]),fo=function(){function e(t,n){var r;l(this,e),(r=u(this,e)).isReady_=!1,r.isLatest=!1;var i=e.baseUrl,s=null==n?void 0:n.language;if(s)try{s=e.consentLanguages.parseLanguage(s)}catch(e){throw new Wi("Error during parsing the language: "+e.message)}if(r.lang_=s||e.DEFAULT_LANGUAGE,r.cacheLang_=s||e.DEFAULT_LANGUAGE,r.isVendorList(t))r.populate(t),r.readyPromise=Promise.resolve();else{if(!i)throw new Wi("must specify GVL.baseUrl before loading GVL json");if(t>0){var o=t;e.CACHE.has(o)?(r.populate(e.CACHE.get(o)),r.readyPromise=Promise.resolve()):(i+=e.versionedFilename.replace("[VERSION]",String(o)),r.readyPromise=r.fetchJson(i))}else e.CACHE.has(e.LATEST_CACHE_KEY)?(r.populate(e.CACHE.get(e.LATEST_CACHE_KEY)),r.readyPromise=Promise.resolve()):(r.isLatest=!0,r.readyPromise=r.fetchJson(i+e.latestFilename))}return r}return S(e,rs),p(e,[{key:"cacheLanguage",value:function(){e.LANGUAGE_CACHE.has(this.cacheLang_)||e.LANGUAGE_CACHE.set(this.cacheLang_,{purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories})}},{key:"fetchJson",value:(n=c(I().mark((function e(t){return I().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.t0=this,e.next=4,po.fetch(t);case 4:e.t1=e.sent,e.t0.populate.call(e.t0,e.t1),e.next=11;break;case 8:throw e.prev=8,e.t2=e.catch(0),new Wi(e.t2.message);case 11:case"end":return e.stop()}}),e,this,[[0,8]])}))),function(e){return n.apply(this,arguments)})},{key:"getJson",value:function(){return m(m({gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.clonePurposes(),specialPurposes:this.cloneSpecialPurposes(),features:this.cloneFeatures(),specialFeatures:this.cloneSpecialFeatures(),stacks:this.cloneStacks()},this.dataCategories?{dataCategories:this.cloneDataCategories()}:{}),{},{vendors:this.cloneVendors()})}},{key:"cloneSpecialFeatures",value:function(){for(var t={},n=0,r=Object.keys(this.specialFeatures);n<r.length;n++){var i=r[n];t[i]=e.cloneFeature(this.specialFeatures[i])}return t}},{key:"cloneFeatures",value:function(){for(var t={},n=0,r=Object.keys(this.features);n<r.length;n++){var i=r[n];t[i]=e.cloneFeature(this.features[i])}return t}},{key:"cloneStacks",value:function(){for(var t={},n=0,r=Object.keys(this.stacks);n<r.length;n++){var i=r[n];t[i]=e.cloneStack(this.stacks[i])}return t}},{key:"cloneDataCategories",value:function(){for(var t={},n=0,r=Object.keys(this.dataCategories);n<r.length;n++){var i=r[n];t[i]=e.cloneDataCategory(this.dataCategories[i])}return t}},{key:"cloneSpecialPurposes",value:function(){for(var t={},n=0,r=Object.keys(this.specialPurposes);n<r.length;n++){var i=r[n];t[i]=e.clonePurpose(this.specialPurposes[i])}return t}},{key:"clonePurposes",value:function(){for(var t={},n=0,r=Object.keys(this.purposes);n<r.length;n++){var i=r[n];t[i]=e.clonePurpose(this.purposes[i])}return t}},{key:"cloneVendors",value:function(){for(var t={},n=0,r=Object.keys(this.fullVendorList);n<r.length;n++){var i=r[n];t[i]=e.cloneVendor(this.fullVendorList[i])}return t}},{key:"changeLanguage",value:(t=c(I().mark((function t(n){var r,i,s,o,a;return I().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r=n,t.prev=1,r=e.consentLanguages.parseLanguage(n),t.next=8;break;case 5:throw t.prev=5,t.t0=t.catch(1),new Wi("Error during parsing the language: "+t.t0.message);case 8:if(i=n.toUpperCase(),r.toLowerCase()!==e.DEFAULT_LANGUAGE.toLowerCase()||e.LANGUAGE_CACHE.has(i)){t.next=11;break}return t.abrupt("return");case 11:if(r===this.lang_){t.next=29;break}if(this.lang_=r,!e.LANGUAGE_CACHE.has(i)){t.next=18;break}for(o in s=e.LANGUAGE_CACHE.get(i))s.hasOwnProperty(o)&&(this[o]=s[o]);t.next=29;break;case 18:return a=e.baseUrl+e.languageFilename.replace("[LANG]",this.lang_.toLowerCase()),t.prev=19,t.next=22,this.fetchJson(a);case 22:this.cacheLang_=i,this.cacheLanguage(),t.next=29;break;case 26:throw t.prev=26,t.t1=t.catch(19),new Wi("unable to load language: "+t.t1.message);case 29:case"end":return t.stop()}}),t,this,[[1,5],[19,26]])}))),function(e){return t.apply(this,arguments)})},{key:"language",get:function(){return this.lang_}},{key:"isVendorList",value:function(e){return void 0!==e&&void 0!==e.vendors}},{key:"populate",value:function(t){this.purposes=t.purposes,this.specialPurposes=t.specialPurposes,this.features=t.features,this.specialFeatures=t.specialFeatures,this.stacks=t.stacks,this.dataCategories=t.dataCategories,this.isVendorList(t)&&(this.gvlSpecificationVersion=t.gvlSpecificationVersion,this.tcfPolicyVersion=t.tcfPolicyVersion,this.vendorListVersion=t.vendorListVersion,this.lastUpdated=t.lastUpdated,"string"==typeof this.lastUpdated&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors_=t.vendors,this.fullVendorList=t.vendors,this.mapVendors(),this.isReady_=!0,this.isLatest&&e.CACHE.set(e.LATEST_CACHE_KEY,this.getJson()),e.CACHE.has(this.vendorListVersion)||e.CACHE.set(this.vendorListVersion,this.getJson())),this.cacheLanguage()}},{key:"mapVendors",value:function(e){var t=this;this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach((function(e){t.byPurposeVendorMap[e]={legInt:new Set,consent:new Set,flexible:new Set}})),Object.keys(this.specialPurposes).forEach((function(e){t.bySpecialPurposeVendorMap[e]=new Set})),Object.keys(this.features).forEach((function(e){t.byFeatureVendorMap[e]=new Set})),Object.keys(this.specialFeatures).forEach((function(e){t.bySpecialFeatureVendorMap[e]=new Set})),Array.isArray(e)||(e=Object.keys(this.fullVendorList).map((function(e){return+e}))),this.vendorIds=new Set(e),this.vendors_=e.reduce((function(e,n){var r=t.vendors_[String(n)];return r&&void 0===r.deletedDate&&(r.purposes.forEach((function(e){t.byPurposeVendorMap[String(e)].consent.add(n)})),r.specialPurposes.forEach((function(e){t.bySpecialPurposeVendorMap[String(e)].add(n)})),r.legIntPurposes.forEach((function(e){t.byPurposeVendorMap[String(e)].legInt.add(n)})),r.flexiblePurposes&&r.flexiblePurposes.forEach((function(e){t.byPurposeVendorMap[String(e)].flexible.add(n)})),r.features.forEach((function(e){t.byFeatureVendorMap[String(e)].add(n)})),r.specialFeatures.forEach((function(e){t.bySpecialFeatureVendorMap[String(e)].add(n)})),e[n]=r),e}),{})}},{key:"getFilteredVendors",value:function(e,t,n,r){var i=this,s=e.charAt(0).toUpperCase()+e.slice(1),o={};return("purpose"===e&&n?this["by"+s+"VendorMap"][String(t)][n]:this["by"+(r?"Special":"")+s+"VendorMap"][String(t)]).forEach((function(e){o[String(e)]=i.vendors[String(e)]})),o}},{key:"getVendorsWithConsentPurpose",value:function(e){return this.getFilteredVendors("purpose",e,"consent")}},{key:"getVendorsWithLegIntPurpose",value:function(e){return this.getFilteredVendors("purpose",e,"legInt")}},{key:"getVendorsWithFlexiblePurpose",value:function(e){return this.getFilteredVendors("purpose",e,"flexible")}},{key:"getVendorsWithSpecialPurpose",value:function(e){return this.getFilteredVendors("purpose",e,void 0,!0)}},{key:"getVendorsWithFeature",value:function(e){return this.getFilteredVendors("feature",e)}},{key:"getVendorsWithSpecialFeature",value:function(e){return this.getFilteredVendors("feature",e,void 0,!0)}},{key:"vendors",get:function(){return this.vendors_}},{key:"narrowVendorsTo",value:function(e){this.mapVendors(e)}},{key:"isReady",get:function(){return this.isReady_}},{key:"clone",value:function(){var t=new e(this.getJson());return this.lang_!==e.DEFAULT_LANGUAGE&&t.changeLanguage(this.lang_),t}}],[{key:"baseUrl",get:function(){return this.baseUrl_},set:function(e){if(/^https?:\/\/vendorlist\.consensu\.org\//.test(e))throw new Wi("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");e.length>0&&"/"!==e[e.length-1]&&(e+="/"),this.baseUrl_=e}},{key:"emptyLanguageCache",value:function(t){var n=!1;return null==t&&e.LANGUAGE_CACHE.size>0?(e.LANGUAGE_CACHE=new Map,n=!0):"string"==typeof t&&this.consentLanguages.has(t.toUpperCase())&&(e.LANGUAGE_CACHE.delete(t.toUpperCase()),n=!0),n}},{key:"emptyCache",value:function(t){var n=!1;return Number.isInteger(t)&&t>=0?(e.CACHE.delete(t),n=!0):void 0===t&&(e.CACHE=new Map,n=!0),n}},{key:"clonePurpose",value:function(e){return m(m({id:e.id,name:e.name,description:e.description},e.descriptionLegal?{descriptionLegal:e.descriptionLegal}:{}),e.illustrations?{illustrations:Array.from(e.illustrations)}:{})}},{key:"cloneFeature",value:function(e){return m(m({id:e.id,name:e.name,description:e.description},e.descriptionLegal?{descriptionLegal:e.descriptionLegal}:{}),e.illustrations?{illustrations:Array.from(e.illustrations)}:{})}},{key:"cloneDataCategory",value:function(e){return{id:e.id,name:e.name,description:e.description}}},{key:"cloneStack",value:function(e){return{id:e.id,name:e.name,description:e.description,purposes:Array.from(e.purposes),specialFeatures:Array.from(e.specialFeatures)}}},{key:"cloneDataRetention",value:function(e){return m(m({},"number"==typeof e.stdRetention?{stdRetention:e.stdRetention}:{}),{},{purposes:m({},e.purposes),specialPurposes:m({},e.specialPurposes)})}},{key:"cloneVendorUrls",value:function(e){return e.map((function(e){return m({langId:e.langId,privacy:e.privacy},e.legIntClaim?{legIntClaim:e.legIntClaim}:{})}))}},{key:"cloneVendor",value:function(e){return m(m(m(m(m(m(m(m(m(m(m({id:e.id,name:e.name,purposes:Array.from(e.purposes),legIntPurposes:Array.from(e.legIntPurposes),flexiblePurposes:Array.from(e.flexiblePurposes),specialPurposes:Array.from(e.specialPurposes),features:Array.from(e.features),specialFeatures:Array.from(e.specialFeatures)},e.overflow?{overflow:{httpGetLimit:e.overflow.httpGetLimit}}:{}),"number"==typeof e.cookieMaxAgeSeconds||null===e.cookieMaxAgeSeconds?{cookieMaxAgeSeconds:e.cookieMaxAgeSeconds}:{}),void 0!==e.usesCookies?{usesCookies:e.usesCookies}:{}),e.policyUrl?{policyUrl:e.policyUrl}:{}),void 0!==e.cookieRefresh?{cookieRefresh:e.cookieRefresh}:{}),void 0!==e.usesNonCookieAccess?{usesNonCookieAccess:e.usesNonCookieAccess}:{}),e.dataRetention?{dataRetention:this.cloneDataRetention(e.dataRetention)}:{}),e.urls?{urls:this.cloneVendorUrls(e.urls)}:{}),e.dataDeclaration?{dataDeclaration:Array.from(e.dataDeclaration)}:{}),e.deviceStorageDisclosureUrl?{deviceStorageDisclosureUrl:e.deviceStorageDisclosureUrl}:{}),e.deletedDate?{deletedDate:e.deletedDate}:{})}},{key:"isInstanceOf",value:function(e){return"object"===A(e)&&"function"==typeof e.narrowVendorsTo}}]);var t,n}();(Di=fo).LANGUAGE_CACHE=new Map,Di.CACHE=new Map,Di.LATEST_CACHE_KEY=0,Di.DEFAULT_LANGUAGE="EN",Di.consentLanguages=new Zi,Di.latestFilename="vendor-list.json",Di.versionedFilename="archives/vendor-list-v[VERSION].json",Di.languageFilename="purposes-[LANG].json",Mi({},"TCModel",(function(){return ho}));var ho=function(){function e(t){var n;return l(this,e),(n=u(this,e)).isServiceSpecific_=!1,n.supportOOB_=!0,n.useNonStandardTexts_=!1,n.purposeOneTreatment_=!1,n.publisherCountryCode_="AA",n.version_=2,n.consentScreen_=0,n.policyVersion_=4,n.consentLanguage_="EN",n.cmpId_=0,n.cmpVersion_=0,n.vendorListVersion_=0,n.numCustomPurposes_=0,n.addtlConsent_="",n.enableAdvertiserConsentMode_=!1,n.specialFeatureOptins=new Vs,n.purposeConsents=new Vs,n.purposeLegitimateInterests=new Vs,n.publisherConsents=new Vs,n.publisherLegitimateInterests=new Vs,n.publisherCustomConsents=new Vs,n.publisherCustomLegitimateInterests=new Vs,n.vendorConsents=new Vs,n.vendorLegitimateInterests=new Vs,n.vendorsDisclosed=new Vs,n.vendorsAllowed=new Vs,n.publisherRestrictions=new cs,t&&(n.gvl=t),n.updated(),n}return S(e,rs),p(e,[{key:"gvl",get:function(){return this.gvl_},set:function(e){fo.isInstanceOf(e)||(e=new fo(e)),this.gvl_=e,this.publisherRestrictions.gvl=e}},{key:"cmpId",get:function(){return this.cmpId_},set:function(e){if(e=Number(e),!(Number.isInteger(e)&&e>1))throw new Ki("cmpId",e);this.cmpId_=e}},{key:"cmpVersion",get:function(){return this.cmpVersion_},set:function(e){if(e=Number(e),!(Number.isInteger(e)&&e>-1))throw new Ki("cmpVersion",e);this.cmpVersion_=e}},{key:"consentScreen",get:function(){return this.consentScreen_},set:function(e){if(e=Number(e),!(Number.isInteger(e)&&e>-1))throw new Ki("consentScreen",e);this.consentScreen_=e}},{key:"consentLanguage",get:function(){return this.consentLanguage_},set:function(e){this.consentLanguage_=e}},{key:"publisherCountryCode",get:function(){return this.publisherCountryCode_},set:function(e){if(!/^([A-z]){2}$/.test(e))throw new Ki("publisherCountryCode",e);this.publisherCountryCode_=e.toUpperCase()}},{key:"vendorListVersion",get:function(){return this.gvl?this.gvl.vendorListVersion:this.vendorListVersion_},set:function(e){if((e=0|Number(e))<0)throw new Ki("vendorListVersion",e);this.vendorListVersion_=e}},{key:"policyVersion",get:function(){return this.gvl?this.gvl.tcfPolicyVersion:this.policyVersion_},set:function(e){if(this.policyVersion_=parseInt(e,10),this.policyVersion_<0)throw new Ki("policyVersion",e)}},{key:"version",get:function(){return this.version_},set:function(e){this.version_=parseInt(e,10)}},{key:"isServiceSpecific",get:function(){return this.isServiceSpecific_},set:function(e){this.isServiceSpecific_=e}},{key:"useNonStandardTexts",get:function(){return this.useNonStandardTexts_},set:function(e){this.useNonStandardTexts_=e}},{key:"supportOOB",get:function(){return this.supportOOB_},set:function(e){this.supportOOB_=e}},{key:"addtlConsent",get:function(){return this.addtlConsent_},set:function(e){this.addtlConsent_=e||""}},{key:"enableAdvertiserConsentMode",get:function(){return this.enableAdvertiserConsentMode_},set:function(e){this.enableAdvertiserConsentMode_=!0===e}},{key:"purposeOneTreatment",get:function(){return this.purposeOneTreatment_},set:function(e){this.purposeOneTreatment_=e}},{key:"setAllVendorConsents",value:function(){this.vendorConsents.set(this.gvl.vendors)}},{key:"unsetAllVendorConsents",value:function(){this.vendorConsents.empty()}},{key:"setAllVendorsDisclosed",value:function(){this.vendorsDisclosed.set(this.gvl.vendors)}},{key:"unsetAllVendorsDisclosed",value:function(){this.vendorsDisclosed.empty()}},{key:"setAllVendorsAllowed",value:function(){this.vendorsAllowed.set(this.gvl.vendors)}},{key:"unsetAllVendorsAllowed",value:function(){this.vendorsAllowed.empty()}},{key:"setAllVendorLegitimateInterests",value:function(){this.vendorLegitimateInterests.set(this.gvl.vendors)}},{key:"unsetAllVendorLegitimateInterests",value:function(){this.vendorLegitimateInterests.empty()}},{key:"setAllPurposeConsents",value:function(){this.purposeConsents.set(this.gvl.purposes)}},{key:"unsetAllPurposeConsents",value:function(){this.purposeConsents.empty()}},{key:"setAllPurposeLegitimateInterests",value:function(){this.purposeLegitimateInterests.set(this.gvl.purposes)}},{key:"unsetAllPurposeLegitimateInterests",value:function(){this.purposeLegitimateInterests.empty()}},{key:"setAllPublisherConsents",value:function(){this.publisherConsents.set(this.gvl.purposes)}},{key:"unsetAllPublisherConsents",value:function(){this.publisherConsents.empty()}},{key:"setAllPublisherLegitimateInterests",value:function(){this.publisherLegitimateInterests.set(this.gvl.purposes)}},{key:"unsetAllPublisherLegitimateInterests",value:function(){this.publisherLegitimateInterests.empty()}},{key:"setAllSpecialFeatureOptins",value:function(){this.specialFeatureOptins.set(this.gvl.specialFeatures)}},{key:"unsetAllSpecialFeatureOptins",value:function(){this.specialFeatureOptins.empty()}},{key:"setAll",value:function(){this.setAllVendorConsents(),this.setAllPurposeLegitimateInterests(),this.setAllSpecialFeatureOptins(),this.setAllPurposeConsents(),this.setAllPublisherConsents(),this.setAllPublisherLegitimateInterests(),this.setAllVendorLegitimateInterests()}},{key:"unsetAll",value:function(){this.unsetAllVendorConsents(),this.unsetAllPurposeLegitimateInterests(),this.unsetAllSpecialFeatureOptins(),this.unsetAllPurposeConsents(),this.unsetAllPublisherConsents(),this.unsetAllPublisherLegitimateInterests(),this.unsetAllVendorLegitimateInterests()}},{key:"numCustomPurposes",get:function(){var e=this.numCustomPurposes_;if("object"===A(this.customPurposes)){var t=Object.keys(this.customPurposes).sort((function(e,t){return Number(e)-Number(t)}));e=parseInt(t.pop(),10)}return e},set:function(e){if(this.numCustomPurposes_=parseInt(e,10),this.numCustomPurposes_<0)throw new Ki("numCustomPurposes",e)}},{key:"updated",value:function(){var e=new Date,t=new Date(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()));this.created=t,this.lastUpdated=t}}])}();ho.consentLanguages=fo.consentLanguages,Mi({},"TCString",(function(){return Io}));var go,vo,So,Eo,_o,mo,Io=p((function e(){l(this,e)}),null,[{key:"encode",value:function(e,t){var n,r="";return e=lo.process(e,t),(n=Array.isArray(null==t?void 0:t.segments)?t.segments:new ao(e,t)[""+e.version]).forEach((function(t,i){var s="";i<n.length-1&&(s="."),r+=co.encode(e,t)+s})),r}},{key:"decode",value:function(e,t){var n=e.split("."),r=n.length;t||(t=new ho);for(var i=0;i<r;i++){var s=n[i],o=Ji.decode(s.charAt(0)).substr(0,ks.segmentType),a=hs.ID_TO_KEY[js.decode(o,ks.segmentType).toString()];co.decode(s,t,a)}return t}}]),yo=function(e,t,n){return t?e.map((function(e){return t&&t[e.id]?K(K({},e),{name:"".concat(e.name," (").concat(n,": ").concat(t[e.id],")")}):e})):e},Co=function(e,t,n,r,i,s,o){if(!e)return[];var a=e.gvl,c=e.publisherRestrictions,u=e.vendorConsents,l=e.vendorLegitimateInterests;if(!a)return[];var d=ye.fetchTCFVendorsDisclosedObject(a.vendors),p=Fo(c);return Object.values(a.vendors).map((function(e){var c,f,h,g=Le(e.purposes,a.purposes),v=Le(e.legIntPurposes,a.purposes);i&&(g=g.filter((function(e){return 1!==e.id})));var S=Ro(e.id,d),E=g.map((function(e){return e})),_=v.map((function(e){return e}));p.forEach((function(t){var n=t.purposeId,r=t.restrictionType,i=e.flexiblePurposes.indexOf(n)>-1;switch(r){case ss.NOT_ALLOWED:E=E.filter((function(e){return e.id!==n})),_=_.filter((function(e){return e.id!==n}));break;case ss.REQUIRE_CONSENT:_=_.filter((function(e){return e.id!==n||(i&&E.push(e),!1)}));break;case ss.REQUIRE_LI:E=E.filter((function(e){return e.id!==n||(i&&_.push(e),!1)}))}}));var m="",I="";e.urls&&e.urls.forEach((function(e){var t=e.langId,n=e.legIntClaim,r=e.privacy,i=t&&a.language&&t.toLowerCase()===a.language.toLowerCase();r&&(m&&!i||(m=r)),n&&(I&&!i||(I=n))}));var y,C=s.includes(e.id),T=null===(c=e.dataDeclaration)||void 0===c?void 0:c.reduce((function(e,t){var n=a.dataCategories&&a.dataCategories[t];if(n){var r=K(K({},n),{id:n.id.toString()});return e?Q(Q([],X(e),!1),[r],!1):[r]}return e}),[]);e.dataRetention&&e.dataRetention.stdRetention&&(y=[{id:e.dataRetention.stdRetention.toString(),name:e.dataRetention.stdRetention.toString()}]);var b=Le(e.specialFeatures.filter((function(e){return!t.includes(e)})),a.specialFeatures);return K(K(K({consent:S?u.has(e.id):null,cookieMaxAgeSeconds:e.cookieMaxAgeSeconds&&e.cookieMaxAgeSeconds>=0?e.cookieMaxAgeSeconds:null,cookieRefresh:Ne(e.cookieRefresh,null)},T&&{dataCategories:T}),y&&{dataRetention:y}),{deviceStorage:null,deviceStorageDisclosureUrl:e.deviceStorageDisclosureUrl||null,features:e.features.map((function(e){return{id:e,name:a.features[e].name}})),flexiblePurposes:Le(e.flexiblePurposes,a.purposes),id:e.id,legitimateInterestClaimUrl:I,legitimateInterestConsent:S?l.has(e.id):null,legitimateInterestPurposes:_,name:e.name,policyUrl:e.policyUrl,privacyUrl:m,purposes:yo(E,null===(f=e.dataRetention)||void 0===f?void 0:f.purposes,o),showConsentToggle:E.length>0&&n,showLegitimateInterestConsentToggle:_.length>0&&n&&!r,showVendorOutsideEU:C,specialFeatures:b,specialPurposes:yo(Le(e.specialPurposes,a.specialPurposes),null===(h=e.dataRetention)||void 0===h?void 0:h.specialPurposes,o),usesCookies:Ne(e.usesCookies,!1),usesNonCookieAccess:Ne(e.usesNonCookieAccess,null)})}))},To=function(e){return{legitimateInterestPurposes:(null==e?void 0:e.legIntPurposes)||[],notAllowedPurposes:(null==e?void 0:e.notAllowedPurposes)||[],purposes:(null==e?void 0:e.purposes)||[]}},bo=function(e){return Object.entries(e).map((function(e){var t=X(e,2),n=t[0],r=t[1];return{id:parseInt(n.replace("p",""),10),name:r}}))},Ao=function(e){return e||"/browser-sdk/".concat(ie,"/cookie-bridge.html")},Oo=function(e){return e||"https://usercentrics.mgr.consensu.org:443"},No=function(e,t){if(!t||0===e.length)return[];var n=function(e){return De(e.reduce((function(e,t){return e.concat(t.features.map((function(e){return e.id})))}),[]))}(e);return n.reduce((function(e,n){var r=t[n];return r?Q(Q([],X(e),!1),[r],!1):e}),[])},wo=function(e,t){return!e||!t},Ro=function(e,t){return void 0===t&&(t=ye.fetchTCFVendorsDisclosedObject()),!0===t[e]},Lo=function(e,t,n,r,i){var s=t.reduce((function(e,t){return e.concat(t.purposes.map((function(e){return e.id}))).concat(t.legitimateInterestPurposes.map((function(e){return e.id})))}),[]),o=Mo(e,n,r).reduce((function(e,t){return e.concat(t.purposeIds)}),[]),a=De(s.concat(o));return i?a.filter((function(e){return 1!==e})):a},Do=function(e,t,n,r,i,s,o,a,c){if(!e||!t||!n)return[];var u=Lo(t,r,i,s,o),l=Mo(t,i,s),d=ye.fetchTCFData().vendors,p=e.purposeConsents,f=e.purposeLegitimateInterests,h=De(r.map((function(e){return e.legitimateInterestPurposes.map((function(e){return e.id}))})).reduce((function(e,t){return e.concat(t)}),[])),g=De(r.map((function(e){return e.purposes.map((function(e){return e.id}))})).reduce((function(e,t){return e.concat(t)}),[])),v=u.reduce((function(e,t){var n,i=r.filter((function(e){return e.purposes.find((function(e){return e.id===t}))})),s=r.filter((function(e){return e.legitimateInterestPurposes.find((function(e){return e.id===t}))}));return K(K({},e),((n={})[t]=i.length+s.length,n))}),{});return u.reduce((function(e,t){var r=n[t];if(!r)return e;var i=l.find((function(e){return e.purposeIds.includes(t)}));return Q(Q([],X(e),!1),[{consent:d.length?p.has(t):null,description:r.description,descriptionLegal:r.descriptionLegal,id:r.id,illustrations:r.illustrations,isPartOfASelectedStack:!!i,legitimateInterestConsent:d.length?f.has(t):null,name:r.name,numberOfVendors:v[r.id]||0,showConsentToggle:g.includes(t)&&a,showLegitimateInterestToggle:1!==r.id&&h.includes(t)&&a&&!c,stackId:(null==i?void 0:i.id)||null}],!1)}),[])},Po=function(e){return{onATPListChanged:e.resurfaceATPListChanged,onIABLegalBasisChanged:e.resurfaceIABLegalBasisChanged,onPeriodEnded:e.resurfacePeriodEnded,onPurposeChanged:e.resurfacePurposeChanged,onVendorAdded:e.resurfaceVendorAdded}},Vo=function(e,t,n,r){var i=t.reduce((function(e,t){return e.concat(t.specialFeatures.filter((function(e){return!n.includes(e.id)})).map((function(e){return e.id})))}),[]),s=Mo(e,n,r).reduce((function(e,t){return e.concat(t.specialFeatureIds.filter((function(e){return!n.includes(e)})))}),[]);return De(i.concat(s))},Uo=function(e,t,n,r,i,s){if(!e||!t)return[];var o=Vo(t.stacks,n,r,i),a=Mo(t.stacks,r,i);return o.map((function(n){var r=t.specialFeatures[n],i=a.find((function(e){return e.specialFeatureIds.includes(n)}));return{consent:e.specialFeatureOptins.has(n),description:r.description,descriptionLegal:r.descriptionLegal,id:r.id,illustrations:r.illustrations,isPartOfASelectedStack:!!i,name:r.name,showConsentToggle:s,stackId:(null==i?void 0:i.id)||null}}))},ko=function(e,t){if(!e)return[];var n=function(e){return De(e.reduce((function(e,t){return e.concat(t.specialPurposes.map((function(e){return e.id})))}),[]))}(t);return n.reduce((function(t,n){var r=e[n];return r?Q(Q([],X(t),!1),[{description:r.description,descriptionLegal:r.descriptionLegal,id:r.id,illustrations:r.illustrations,name:r.name}],!1):t}),[])},Mo=function(e,t,n){return e?n.reduce((function(n,r){var i=e[r];return i?Q(Q([],X(n),!1),[{description:i.description,id:i.id,name:i.name,purposeIds:i.purposes,specialFeatureIds:i.specialFeatures.filter((function(e){return!t.includes(e)}))}],!1):n}),[]):[]},Fo=function(e){return(null==e?void 0:e.getRestrictions().map((function(e){return{purposeId:e.purposeId,restrictionType:e.restrictionType}})))||[]},xo=function(e){return!!(null==e?void 0:e.timestamp)&&(Date.now()-e.timestamp)/864e5>390},Go=function(e,t){if(!(null==e?void 0:e.acString)||!(null==e?void 0:e.acString.length))return!1;var n=null==e?void 0:e.acString.split("~")[1].split(".").filter((function(e){return""!==e})).map((function(e){return parseInt(e,10)})),r=null==e?void 0:e.acString.split("dv.")[1].split(".").filter((function(e){return""!==e})).map((function(e){return parseInt(e,10)})),i=Q(Q([],X(n),!1),X(r),!1).sort((function(e,t){return e-t})),s=t.map((function(e){return e.id})).sort((function(e,t){return e-t}));return!(i.length===s.length&&i.every((function(e,t){return e===s[t]})))},Bo=function(e,t){return!(!(null==e?void 0:e.vendors)||!(null==e?void 0:e.vendors.length)||t.every((function(t){return null==e?void 0:e.vendors.find((function(e){return e[pe.ID]===t.id}))})))},Ho=function(e,t){return!(!(null==e?void 0:e.vendors)||!e.vendors.length||e.vendors.length&&!e.vendors[0][pe.SPECIAL_PURPOSES]||e.vendors.every((function(e){var n=t.find((function(t){return t.id===e[pe.ID]}));return!n||e[pe.PURPOSES].sort().toString()===n.purposes.map((function(e){return e.id})).sort().toString()&&e[pe.LEGITIMATE_INTEREST].sort().toString()===n.legitimateInterestPurposes.map((function(e){return e.id})).sort().toString()&&e[pe.SPECIAL_PURPOSES].sort().toString()===n.specialPurposes.map((function(e){return e.id})).sort().toString()})))},jo=function(e){return e.map((function(e){var t=e.id,n=e.legitimateInterestPurposes,r=e.purposes,i=e.specialPurposes;return[t,n.map((function(e){return e.id})),r.map((function(e){return e.id})),i.map((function(e){return e.id}))]}))},$o=function(e){var t=e.replace("_","-");if(fo.consentLanguages.has(t.toLocaleLowerCase())||fo.consentLanguages.has(t.toUpperCase()))return t;var n=t.slice(0,2);if(fo.consentLanguages.has(n.toLocaleLowerCase())||fo.consentLanguages.has(n.toUpperCase()))return n;switch(e){case"pt":return"pt-pt";case"sr":return"sr-cyrl";default:return pn}},Yo=[],Wo=[],zo=function(e,t,n){switch(e){case"queue":return Yo;case"ping":"function"==typeof t&&t({applicableSections:[-1],cmpDisplayStatus:"hidden",cmpId:31,cmpStatus:"stub",gppString:"",gppVersion:"1.1",sectionList:[],supportedAPIs:["2:tcfeuv2","5:tcfcav1","6:uspv1"]},!0);break;case"addEventListener":"lastId"in window.__gpp||(window.__gpp.lastId=0),window.__gpp.lastId+=1;var r=window.__gpp.lastId;Wo.push({callback:t,id:r,parameter:n}),t({data:!0,eventName:"listenerRegistered",listenerId:r,pingData:{applicableSections:[-1],cmpDisplayStatus:"hidden",cmpId:31,cmpStatus:"stub",gppString:"",gppVersion:"1.1",sectionList:[],supportedAPIs:["2:tcfeuv2","5:tcfcav1","9:usva","7:usnat"]}},!0);break;case"removeEventListener":for(var i=!1,s=0;s<Wo.length;s++)if(Wo[s].id===n){Wo.splice(s,1),i=!0;break}t({data:i,eventName:"listenerRemoved",listenerId:n,pingData:{applicableSections:[-1],cmpDisplayStatus:"hidden",cmpId:31,cmpStatus:"stub",gppString:"",gppVersion:"1.1",sectionList:[],supportedAPIs:["2:tcfeuv2","5:tcfcav1","9:usva","7:usnat"]}},!0);break;case"hasSection":t(!1,!0);break;case"getSection":case"getField":t(null,!0);break;default:Yo.push([e,t,n])}},Ko="__gppLocator",Jo=function(e){if(e&&e.source&&e.source.postMessage){var t="string"==typeof e.data,n=e.data;if(t)try{n=JSON.parse(e.data)}catch(e){return}if("object"===A(n)&&n.__gppCall){var r=n.__gppCall;window.__gpp(r.command,r.version,(function(n,i){var s={__gppReturn:{returnValue:n,success:i,callId:r.callId}},o=t?JSON.stringify(s):s;try{e.source.postMessage(o,"*")}catch(e){}}),r.parameter)}}},qo=function(){"undefined"!=typeof window&&(window.__gpp||!function(){for(var e=window,t=!1;e;){try{if(e.frames[Ko]){t=!0;break}}catch(e){}if(e===window.top){t=!1;break}e=e.parent}return t}()&&(We((function(){return!!window.document.body}),"").then((function(){var e=window.document.createElement("iframe");e.style.cssText="display:none",e.name=Ko,window.document.body.appendChild(e)})),1)&&(window.addEventListener("message",Jo,!1),window.__gpp=zo))},Xo=function(e){if(e&&e.source&&e.source.postMessage){var t="string"==typeof e.data,n=e.data;if(t)try{n=JSON.parse(e.data)}catch(e){return}if("object"===A(n)&&n.__tcfapiCall){var r=n.__tcfapiCall;window.__tcfapi(r.command,r.version,(function(n,i){var s={__tcfapiReturn:{returnValue:n,success:i,callId:r.callId}},o=t?JSON.stringify(s):s;try{e.source.postMessage(o,"*")}catch(e){}}),r.parameter)}}},Qo=[],Zo=function(e,t,n,r){if(!e)return Qo;switch(e){case"ping":"function"==typeof n&&n({cmpLoaded:!1,cmpStatus:"stub",gdprApplies:!0});break;case"pending":return Qo;default:Qo.push([e,t,n,r])}},ea="__tcfapiLocator",ta=function(){"undefined"!=typeof window&&(window.__tcfapi||!function(){for(var e=window,t=!1;e;){try{if(e.frames[ea]){t=!0;break}}catch(e){}if(e===window.top){t=!1;break}e=e.parent}return t}()&&(We((function(){return!!window.document.body}),"").then((function(){var e=window.document.createElement("iframe");e.style.cssText="display:none",e.name=ea,window.document.body.appendChild(e)})),1)&&(window.addEventListener("message",Xo,!1),window.__tcfapi=Zo))};!function(e){e.ACCEPT_ALL_SERVICES="onAcceptAllServices",e.DENY_ALL_SERVICES="onDenyAllServices",e.ESSENTIAL_CHANGE="onEssentialChange",e.INITIAL_PAGE_LOAD="onInitialPageLoad",e.NON_EU_REGION="onNonEURegion",e.SESSION_RESTORED="onSessionRestored",e.TCF_STRING_CHANGE="onTcfStringChange",e.UPDATE_SERVICES="onUpdateServices",e.MOBILE_SESSION_RESTORED="onMobileSessionRestore"}(go||(go={})),function(e){e.EXPLICIT="explicit",e.IMPLICIT="implicit"}(vo||(vo={})),function(e){e[e.UNDEFINED=0]="UNDEFINED",e[e.CMP_SHOWN=1]="CMP_SHOWN",e[e.ACCEPT_ALL=2]="ACCEPT_ALL",e[e.DENY_ALL=3]="DENY_ALL",e[e.SAVE=4]="SAVE",e[e.ACCEPT_ALL_L1=5]="ACCEPT_ALL_L1",e[e.DENY_ALL_L1=6]="DENY_ALL_L1",e[e.SAVE_L1=7]="SAVE_L1",e[e.ACCEPT_ALL_L2=8]="ACCEPT_ALL_L2",e[e.DENY_ALL_L2=9]="DENY_ALL_L2",e[e.SAVE_L2=10]="SAVE_L2",e[e.COOKIE_POLICY_LINK=11]="COOKIE_POLICY_LINK",e[e.IMPRINT_LINK=12]="IMPRINT_LINK",e[e.MORE_INFORMATION_LINK=13]="MORE_INFORMATION_LINK",e[e.PRIVACY_POLICY_LINK=14]="PRIVACY_POLICY_LINK",e[e.CCPA_TOGGLES_ON=15]="CCPA_TOGGLES_ON",e[e.CCPA_TOGGLES_OFF=16]="CCPA_TOGGLES_OFF",e[e.SAY_MINE_LINK=17]="SAY_MINE_LINK"}(So||(So={})),function(e){e.API_NAME="__uspapi",e.GET_USP_DATA="getUSPData"}(Eo||(Eo={})),function(e){e[e.FIRST_LAYER=0]="FIRST_LAYER",e[e.NONE=1]="NONE",e[e.PRIVACY_BUTTON=2]="PRIVACY_BUTTON",e[e.SECOND_LAYER=3]="SECOND_LAYER"}(_o||(_o={})),function(e){e[e.CCPA=0]="CCPA",e[e.DEFAULT=1]="DEFAULT",e[e.TCF=2]="TCF"}(mo||(mo={})),void 0!==Ui&&Ui.initialize({modulePath:"/dir"});var na,ra,ia,sa,oa,aa,ca,ua,la,da,pa,fa,ha,ga=function(){function e(e,t){var n;if(this.ampInstance=Et.getInstance(),this.apiInstance=Xt.getInstance(),this.botInstance=nn.getInstance(),this.controllerIdInstance=Ht.getInstance(),this.dataFacadeInstance=$n.getInstance(),this.eventDispatcherInstance=un.getInstance(),this.initOptions={},this.isConsentRequired=null,this.languageInstance=Ti.getInstance(),this.storageServiceInstance=ye.getInstance(),this.locationInstance=$t.getInstance(),this.settingsV2=jn.getInstance(),this.uiInstance=Vi.getInstance(),this.domains=null,this.rulesetRule={name:"",noShow:!0,settingsId:""},this.gppData=null,this.isCurrentDomainAllowed=function(e){var t,n=window.location.hostname.replace("www.",""),r=window.location.pathname,i=null===(t=e.allowedDomainOptions)||void 0===t?void 0:t.list;if((null==i?void 0:i.length)&&(i=i.filter((function(e){return"string"==typeof e&&""!==e.trim()})).map((function(e){return e.trim()}))),!(null==i?void 0:i.length))return!0;var s=i.concat(re),o=!1;return s.forEach((function(e){if(e.includes("*")){if("*."===e.slice(0,2))n.includes(e.slice(2))&&(o=!0);else if("/*"===e.slice(-2)){var t=e.slice(0,-2);t.split("/")[0]===n&&(n+r).includes(t)&&(o=!0)}}else e.includes("/")?n+r===e&&(o=!0):n===e&&(o=!0)})),o},t&&(this.initOptions=t),this.apiInstance.setEuMode(!0===(null==t?void 0:t.euMode)),this.domains=(n=window.UC_UI_DOMAINS)?Object.entries(n).reduce((function(e,t){var n,r,i=t[0],s=t[1];return"/"===s.slice(-1)?K(K({},e),((n={})[i]=s.slice(0,-1),n)):K(K({},e),((r={})[i]=s,r))}),{aggregator:"",app:"",cdn:"",consents:"",consentsV2:"",consentsV2Fetch:"",crossDomainConsentSharingIFrame:"",graphql:"",trackingEvent:""}):null,this.apiInstance.setDomains(!0===(null==t?void 0:t.sandboxEnv),this.domains),(null==t?void 0:t.createTcfApiStub)&&ta(),(null==t?void 0:t.createGppStub)&&qo(),(null==t?void 0:t.useRulesetId)?this.apiInstance.setRulesetId(e):this.apiInstance.setSettingsId(e),this.controllerIdInstance.value="",(null==t?void 0:t.controllerId)&&!(null==t?void 0:t.useRulesetId)&&(this.controllerIdInstance.value=t.controllerId),(null==t?void 0:t.language)&&this.languageInstance.setPrimaryLanguage(t.language),(null==t?void 0:t.settingsCache)&&this.apiInstance.setJsonCacheBustingString(t.settingsCache),(null==t?void 0:t.version)&&this.apiInstance.setJsonFileVersion(t.version),(null==t?void 0:t.userCountryData)&&"object"===A(t.userCountryData)&&Object.keys(t.userCountryData).every((function(e){return"string"==typeof e}))&&this.locationInstance.setUserCountryData(t.userCountryData),(null==t?void 0:t.userSessionData)&&this.dataFacadeInstance.setUserSessionData(t.userSessionData),(null==t?void 0:t.ampEnabled)&&this.ampInstance.setIsAmpEnabled(!0),(null==t?void 0:t.blockDataLayerPush)&&this.eventDispatcherInstance.setBlockDataLayerPush(!0),(null==t?void 0:t.disableUet)?this.eventDispatcherInstance.setUetDisabled(!0):window.uetq=window.uetq||[],(null==t?void 0:t.storeServiceIdToNameMapping)&&this.storageServiceInstance.setStoreServiceIdToNameMapping(null==t?void 0:t.storeServiceIdToNameMapping),(null==t?void 0:t.disableTracking)&&(this.initOptions.disableTracking=!0),(null==t?void 0:t.disableServerConsents)&&this.apiInstance.setDisableServerConsents(!0),(null==t?void 0:t.disableServerConsents)&&(null==t?void 0:t.controllerId))throw new Error("Usercentrics: disableServerConsents and controllerId should not be present at the same time in the InitOptions!");(null==t?void 0:t.enableDeprecatedV1ConsentSaving)&&(this.initOptions.enableDeprecatedV1ConsentSaving=t.enableDeprecatedV1ConsentSaving),this.initOptions.prefetchServices=Ne(null==t?void 0:t.prefetchServices,!0),this.setTrackingPixel=this.setTrackingPixel.bind(this)}return e.prototype.fetchCoreAndDps=function(){return J(this,void 0,void 0,(function(){var e,t,n;return q(this,(function(r){switch(r.label){case 0:return[4,this.loadSettings()];case 1:return e=r.sent(),t={allowedDomainOptions:{list:(i=e).allowedDomains,showErrorCmp:i.showErrorOnUnallowedDomain},buttonDisplayLocation:i.buttonDisplayLocation,buttonPrivacyCloseIcon:i.buttonPrivacyCloseIcon,buttonPrivacyOpenIconUrl:i.buttonPrivacyOpenIconUrl,ccpa:(a=i.framework,c=i.ccpa,{iabAgreementExists:c.iabAgreementExists,isActive:a?["CCPA","UCPA","CTDPA","VCDPA","CPRA","CPA"].includes(a):c.isActive,region:c.region,reshowAfterDays:c.reshowAfterDays,showOnPageLoad:c.showOnPageLoad}),consentAnalytics:i.consentAnalytics,consentAPIv2:i.consentAPIv2,consentSharingIFrameIsActive:i.consentSharingIFrameIsActive,consentXDevice:i.consentXDevice,customization:(o=i.customization,o?K(K({color:o.color?{primary:o.color.primary,privacyButtonBackground:o.color.privacyButtonBackground,privacyButtonIcon:o.color.privacyButtonIcon}:null},o.privacyButtonSizeMobile&&{privacyButtonSizeMobile:o.privacyButtonSizeMobile}),o.privacyButtonSizeDesktop&&{privacyButtonSizeDesktop:o.privacyButtonSizeDesktop}):o),dataExchangeOnPage:i.dataExchangeOnPage,displayOnlyForEU:i.displayOnlyForEU,enableBotDetection:i.enableBotDetection,enablePoweredBy:i.enablePoweredBy,framework:i.framework,googleConsentMode:i.googleConsentMode,integrations:i.integrations,interactionAnalytics:i.interactionAnalytics,labels:{partnerPoweredByLinkText:i.labels.partnerPoweredByLinkText,poweredBy:i.labels.poweredBy},languagesAvailable:i.languagesAvailable,partnerPoweredByUrl:i.partnerPoweredByUrl,privacyButtonIsVisible:i.privacyButtonIsVisible,privacyButtonUrls:i.privacyButtonUrls,renewConsentsTimestamp:i.renewConsentsTimestamp,reshowBanner:i.reshowBanner,settingsId:i.settingsId,showInitialViewForVersionChange:i.showInitialViewForVersionChange,tagLoggerIsActive:i.tagLoggerIsActive,tcf2:(s=i.tcf2,{resurfaceATPListChanged:s.resurfaceATPListChanged,resurfaceIABLegalBasisChanged:s.resurfaceIABLegalBasisChanged,resurfacePeriodEnded:s.resurfacePeriodEnded,resurfacePurposeChanged:s.resurfacePurposeChanged,resurfaceVendorAdded:s.resurfaceVendorAdded}),tcf2Enabled:i.framework?"TCF2"===i.framework:i.tcf2Enabled,variants:i.variants,version:i.version},n=function(e){return{categories:e.categories,consentTemplates:e.consentTemplates}}(e),[2,{core:t,dps:n}]}var i,s,o,a,c}))}))},e.prototype.init=function(){var e,t,n,r,i,s,o,a,c,u,l,d;return J(this,void 0,void 0,(function(){var p,f,h,g,v,S,E,_,m,I,y,C,T,b,A,O,N,w,R,L,D,P,V,U=this;return q(this,(function(k){switch(k.label){case 0:return ye.getInstance().init(),this.initOptions.createGppStub&&(this.gppData=Ci.getInstance(this.settingsV2.legacySettings,this.initOptions.createGppStub)),this.apiInstance.getRulesetId()?(p=this,[4,Pn.getInstance().resolveSettingsId()]):[3,2];case 1:p.rulesetRule=k.sent(),this.apiInstance.setSettingsId(this.rulesetRule.settingsId),this.initOptions.controllerIds&&this.controllerIdInstance.setControllerIdByResolvedSettingsId(this.rulesetRule.settingsId,this.initOptions.controllerIds),k.label=2;case 2:return ye.clearOnNewSettingsId(this.apiInstance.getSettingsId()),ye.migrateLegacySettings(this.apiInstance.getSettingsId()),f=ye.fetchServices(),h=null==f?void 0:f.map((function(e){return e.history})).flat().reduce((function(e,t){return(null==e?void 0:e.timestamp)<t.timestamp?t:e})),g="explicit"===(null==h?void 0:h.type),[4,this.languageInstance.resolveLanguage()];case 3:k.sent(),k.label=4;case 4:return k.trys.push([4,6,,9]),[4,this.fetchCoreAndDps()];case 5:if(E=k.sent(),m=E.core,I=E.dps,!m.languagesAvailable.includes(this.apiInstance.getJsonFileLanguage()))throw new Error("Using non allowed language");return v=m,S=I,[3,9];case 6:return k.sent(),this.languageInstance.setPrimaryLanguage(""),[4,this.languageInstance.resolveLanguage(!0)];case 7:return k.sent(),[4,this.fetchCoreAndDps()];case 8:return _=k.sent(),m=_.core,I=_.dps,v=m,S=I,[3,9];case 9:if(!this.isCurrentDomainAllowed(v))throw(y=new Error).showErrorCmp=null===(e=v.allowedDomainOptions)||void 0===e?void 0:e.showErrorCmp,console.error('Usercentrics: The domain "'.concat(window.location.hostname,'" has not been added to the allowlist for this Usercentrics account.')),y;if(!v||!S)throw new Error;return this.botInstance.isBotEnabled=v.enableBotDetection,!v.consentSharingIFrameIsActive||this.botInstance.isRobot()?[3,11]:[4,Se.init({useEuCdn:this.initOptions.euMode||!1},this.domains).then((function(){return J(U,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return Se.setCrossDomainId(this.apiInstance.getSettingsId()),Se.setIsCrossDomainAvailable(!0),Se.setUseEuCdn(this.initOptions.euMode||!1),this.languageInstance.getPrimaryLanguage()?[3,3]:[4,Se.getCrossDomainLanguage().catch((function(){return console.warn(W.CROSS_DOMAIN_LANGUAGE_NOT_AVAILABLE),""}))];case 1:return e=t.sent(),[4,this.changeLanguage(e)];case 2:t.sent(),t.label=3;case 3:return[2]}}))}))})).catch((function(e){Se.setIsCrossDomainAvailable(!1),Se.removeIFrame(he),console.warn(W.CROSS_DOMAIN_FEATURE_NOT_AVAILABLE,e)}))];case 10:k.sent(),k.label=11;case 11:return this.botInstance.isRobot()?this.initOptions.suppressCmpDisplay=!0:this.initOptions.disableTracking||this.addSessionTrackingPixel(),C=S.consentTemplates.find((function(e){var t;return"Fraud0"===(null===(t=e._meta)||void 0===t?void 0:t.name)||"q-_eu2X2d"===e.templateId})),(null===(t=v.integrations)||void 0===t?void 0:t.fraud0)&&v.integrations.fraud0.length>0&&C&&!(null==C?void 0:C.isDeactivated)&&((T=document.createElement("script")).src="https://bt.fraud0.com/api/v2/fz.js?cid=".concat(v.integrations.fraud0[0].accountId),T.async=!0,T.setAttribute(yn,"Fraud0"),T.type=on.TEXT_PLAIN,document.body.appendChild(T)),b=this.apiInstance.getJsonFileLanguage(),[4,this.settingsV2.init(v,S,b)];case 12:return k.sent(),[4,this.uiInstance.resolveUiVariant(v.tcf2Enabled)];case 13:return A=k.sent(),[4,this.settingsV2.initData(A,[],!1,!0===this.initOptions.euMode,this.initOptions.excludeAcceptAllVendors,g,this.initOptions.enableDeprecatedV1ConsentSaving)];case 14:return k.sent(),[4,this.apiInstance.fetchTranslations()];case 15:return O=k.sent(),!this.initOptions.prefetchServices&&this.settingsV2.allLegacyServicesHaveName?[3,17]:[4,this.settingsV2.extendServices(A,O)];case 16:k.sent(),this.apiInstance.resetAggregatedServicesCache(),k.label=17;case 17:return this.controllerIdInstance.setNeedSessionRestore(),[4,this.dataFacadeInstance.restoreUserSession(S)];case 18:return(N=k.sent())||(this.controllerIdInstance.init(),this.settingsV2.data&&this.settingsV2.setControllerId(this.controllerIdInstance.value)),[4,this.uiInstance.init(this.initOptions)];case 19:return k.sent(),w=this.settingsV2.getTcfData(),ct(this.settingsV2.data)&&w?[4,this.settingsV2.data.init(!0===this.initOptions.euMode)]:[3,21];case 20:k.sent(),k.label=21;case 21:return this.settingsV2.initLabels(A,O),R=[],ye.settingsExist()&&(R=this.processStorageServicesAndSettings(this.settingsV2.data)),this.botInstance.isRobot()||(this.apiInstance.saveConsentsFromBuffer(),this.apiInstance.saveConsentsV2FromBuffer()),[4,this.uiInstance.resolveUIOptions(v)];case 22:return L=k.sent(),D=L.initialLayer,P=ye.fetchTCFData(),N&&w&&(null===(n=this.gppData)||void 0===n||n.setSectionString(P.tcString,"tcfeuv2"),ye.saveTCFData(K(K({},P),{vendors:jo(w.getTCFData().vendors)}))),this.setIsConsentRequired(this.uiInstance.shouldShowFirstLayer(v)),this.isConsentRequired&&ye.setUserActionPerformed(!1),this.settingsV2.initLabels(A,O),[4,this.settingsV2.initUI(D,A)];case 23:return k.sent(),w?!(0===D||(null===(r=this.initOptions)||void 0===r?void 0:r.suppressCmpDisplay)&&this.getIsConsentRequired())||this.botInstance.isRobot()?[3,25]:[4,w.setUIAsOpen()]:[3,27];case 24:return k.sent(),[3,27];case 25:return[4,w.setUIAsClosed()];case 26:k.sent(),k.label=27;case 27:return this.initOptions.createGppStub&&(ct(this.settingsV2.data)||at(this.settingsV2.data))&&(V=this.settingsV2.data,ct(V)?null===(i=this.gppData)||void 0===i||i.setApplicableSections("tcfeuv2"):at(V)?null===(s=this.gppData)||void 0===s||s.setApplicableSections(V.legalFramework):null===(o=this.gppData)||void 0===o||o.setApplicableSections("tcfeuv2"),(0===D||3===D||(null===(a=this.initOptions)||void 0===a?void 0:a.suppressCmpDisplay)&&this.getIsConsentRequired())&&!this.botInstance.isRobot()&&(null===(c=this.gppData)||void 0===c||c.setCmpDisplayVisible()),2!==D&&1!==D||null===(u=this.gppData)||void 0===u||u.setCmpDisplayHidden(),null===(l=this.gppData)||void 0===l||l.setCmpSignalReady(),null===(d=this.gppData)||void 0===d||d.setCmpStatusLoaded()),this.settingsV2.isTagLoggerActive()&&!this.botInstance.isRobot()&&(new Pi).initTagLogger(),this.eventDispatcherInstance.init(this.settingsV2.getDataExchangeSettings()),[4,this.updateStorage(f,R,N)];case 28:return k.sent(),[2,L]}}))}))},e.prototype.acceptAllForTCF=function(e){return J(this,void 0,void 0,(function(){var t;return q(this,(function(n){switch(n.label){case 0:return(t=this.settingsV2.getTcfData())?[4,t.acceptAllDisclosed(e)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}}))}))},e.prototype.acceptAllServices=function(e){return void 0===e&&(e="explicit"),J(this,void 0,void 0,(function(){return q(this,(function(t){switch(t.label){case 0:return this.dataFacadeInstance.execute(this.settingsV2.getUpdatedServicesWithConsent(V.TRUE),this.settingsV2.getUpdatedServicesDataWithConsent(V.TRUE),"onAcceptAllServices",e,{},this.initOptions.enableDeprecatedV1ConsentSaving),[4,this.saveUserActionPerformed()];case 1:return t.sent(),[2]}}))}))},e.prototype.changeLanguage=function(e){var t,n;return J(this,void 0,void 0,(function(){var r,i,s,o,a,c,u,l,d,p,f,h,g,v,S,E;return q(this,(function(_){switch(_.label){case 0:return r=null===(t=this.settingsV2.core)||void 0===t?void 0:t.language.available,i=r&&r.some((function(t){return t===e})),s=e!==this.apiInstance.getJsonFileLanguage()&&i,o=this.settingsV2,a=o.core,c=o.data,s&&c&&a?(this.apiInstance.setJsonFileLanguage(e),this.apiInstance.resetTranslationsCache(),this.settingsV2.language=e,[4,this.fetchCoreAndDps()]):[3,8];case 1:return u=_.sent().dps,this.settingsV2.dpsJson=u,l=this.uiInstance,d=l.selectedLayer,p=l.variant,[4,this.apiInstance.fetchTranslations()];case 2:return f=_.sent(),null===p||null===d||null==u?[3,6]:!this.settingsV2.isAggregatorLoaded&&this.settingsV2.checkIfServiceNameExists(u.consentTemplates)?[3,4]:[4,this.settingsV2.extendServices(p,f)];case 3:return _.sent(),[3,5];case 4:this.settingsV2.initLabels(p,f),_.label=5;case 5:this.apiInstance.resetAggregatedServicesCache(),_.label=6;case 6:return this.settingsV2.updateServicesLanguage(e),h=this.dataFacadeInstance.getMergedServicesAndSettingsFromStorage(c),g=h.mergedServices,v=h.mergedServicesData,S=h.mergedSettingsData,a.language.selected=e,S.categories=this.settingsV2.mergeServicesDataIntoExistingCategories(v),this.settingsV2.data=S,(E=this.settingsV2.getDataTransferSettings())&&ye.saveSettings(ye.mapSettings(E,g),g),ct(S)?[4,S.changeLanguage(e,null===(n=this.settingsV2.legacySettings)||void 0===n?void 0:n.tcf2)]:[3,8];case 7:_.sent(),_.label=8;case 8:return[2]}}))}))},e.prototype.denyAllForTCF=function(e){return J(this,void 0,void 0,(function(){var t;return q(this,(function(n){switch(n.label){case 0:return(t=this.settingsV2.getTcfData())?[4,t.denyAllDisclosed(e)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}}))}))},e.prototype.denyAllServices=function(e){return void 0===e&&(e="explicit"),J(this,void 0,void 0,(function(){return q(this,(function(t){switch(t.label){case 0:return this.dataFacadeInstance.execute(this.settingsV2.getUpdatedServicesWithConsent(V.FALSE),this.settingsV2.getUpdatedServicesDataWithConsent(V.FALSE),"onDenyAllServices",e,{},this.initOptions.enableDeprecatedV1ConsentSaving),[4,this.saveUserActionPerformed()];case 1:return t.sent(),[2]}}))}))},e.prototype.fetchIsUserInEU=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){return[2,this.locationInstance.getIsUserInEU()]}))}))},e.prototype.fetchUserCountry=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){return[2,this.locationInstance.getUserCountryData()]}))}))},e.prototype.getAbTestVariant=function(){return this.apiInstance.getAbTestVariant()},e.prototype.getCategoriesBaseInfo=function(){return this.settingsV2.getCategoriesBaseInfo()},e.prototype.getCategoriesFullInfo=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return[4,this.apiInstance.fetchTranslations()];case 1:return e=t.sent(),[2,this.settingsV2.getCategoriesFullInfo(this.uiInstance.variant,e)]}}))}))},e.prototype.getCcpaOptOutStatus=function(){var e;return(null===(e=this.settingsV2.getCcpaData())||void 0===e?void 0:e.getIsOptedOut())||!1},e.prototype.getCcpaExplicitNoticeStatus=function(){var e;return(null===(e=this.settingsV2.getCcpaData())||void 0===e?void 0:e.getExplicitNotice())||!1},e.prototype.saveOptOutForCcpa=function(e,t){return void 0===t&&(t="explicit"),J(this,void 0,void 0,(function(){var n,r;return q(this,(function(i){switch(i.label){case 0:return(n=this.settingsV2.getCcpaData())&&n.getIsOptedOut()!==e?(n.setCcpaStorage(e),r={consentAction:e?"onDenyAllServices":"onAcceptAllServices",consentStatus:e?V.FALSE:V.TRUE,consentString:{CCPA:ye.getCcpaString()}},this.dataFacadeInstance.execute(this.settingsV2.getUpdatedServicesWithConsent(r.consentStatus),this.settingsV2.getUpdatedServicesDataWithConsent(r.consentStatus),r.consentAction,t,r.consentString,this.initOptions.enableDeprecatedV1ConsentSaving),[4,this.saveUserActionPerformed()]):[2];case 1:return i.sent(),[2]}}))}))},e.prototype.saveDefaultForCcpa=function(){return J(this,void 0,void 0,(function(){var e,t,n;return q(this,(function(r){switch(r.label){case 0:return e=this.settingsV2.getCcpaData(),t=ye.getCcpaString(),e?[3,2]:(ye.setCcpaTimeStamp(),[4,this.saveUserActionPerformed()]);case 1:case 3:return r.sent(),[2];case 2:return"-"===t[1]?(n=e.getIsOptedOut(),null==e||e.setCcpaStorage(n,mi.ACCEPT),window.dispatchEvent(new Event(rn))):ye.setCcpaTimeStamp(),[4,this.saveUserActionPerformed()]}}))}))},e.prototype.getControllerId=function(){return this.controllerIdInstance.value},e.prototype.getServicesBaseInfo=function(){return this.settingsV2.getServicesBaseInfo()},e.prototype.getServicesFullInfo=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return[4,this.apiInstance.fetchTranslations()];case 1:return e=t.sent(),[2,this.settingsV2.getServicesFullInfo(this.uiInstance.variant,e)]}}))}))},e.prototype.getSettingsCore=function(){var e=this.settingsV2.core;if(!e)throw new Error("Usercentrics: You have to call the init method before!");return e},e.prototype.getSettingsLabels=function(){var e=this.settingsV2.labels;if(!e)throw new Error("Usercentrics: You have to call the init method before!");return e},e.prototype.getSettingsData=function(){var e=this.settingsV2.data;if(!e)throw new Error("Usercentrics: You have to call the init method before!");return e},e.prototype.getSettingsUI=function(){return this.settingsV2.ui},e.prototype.getAriaLabels=function(){var e,t;return ft(this.settingsV2.labels)&&null!=(null===(e=this.settingsV2.labels)||void 0===e?void 0:e.ariaLabels)?null===(t=this.settingsV2.labels)||void 0===t?void 0:t.ariaLabels:Je},e.prototype.getTCFData=function(){var e=this.settingsV2.getTcfData();return e?e.getTCFData():null},e.prototype.getTCFDisclosedVendorsSegmentString=function(){return this.settingsV2.getTCFDisclosedVendorsSegmentString()},e.prototype.injectTCString=function(e){return this.settingsV2.injectTCString(e)},e.prototype.setTCFUIAsClosed=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return(e=this.settingsV2.getTcfData())?[4,e.setUIAsClosed()]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}}))}))},e.prototype.setGppDisplayHidden=function(){var e;return J(this,void 0,void 0,(function(){return q(this,(function(t){switch(t.label){case 0:return[4,null===(e=Ci.getInstance())||void 0===e?void 0:e.setCmpDisplayHidden()];case 1:return t.sent(),[2]}}))}))},e.prototype.setGppDisplayVisible=function(){var e;return J(this,void 0,void 0,(function(){return q(this,(function(t){switch(t.label){case 0:return[4,null===(e=Ci.getInstance())||void 0===e?void 0:e.setCmpDisplayVisible()];case 1:return t.sent(),[2]}}))}))},e.prototype.setTCFUIAsOpen=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return(e=this.settingsV2.getTcfData())?[4,e.setUIAsOpen()]:[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}}))}))},e.prototype.updateChoicesForTCF=function(e,t){return J(this,void 0,void 0,(function(){var n;return q(this,(function(r){switch(r.label){case 0:return(n=this.settingsV2.getTcfData())?[4,n.updateChoices(e,t)]:[3,2];case 1:r.sent(),r.label=2;case 2:return[2]}}))}))},e.prototype.areAllConsentsAccepted=function(){return this.settingsV2.isTcfAvailable()?this.settingsV2.areAllVendorsAndPurposesAccepted()&&this.settingsV2.areAllServicesAccepted():this.settingsV2.areAllServicesAccepted()},e.prototype.restoreUserSession=function(e){var t,n;return J(this,void 0,void 0,(function(){var r;return q(this,(function(i){switch(i.label){case 0:return(null===(t=this.settingsV2.core)||void 0===t?void 0:t.consentAPIv2)&&!(null===(n=this.settingsV2.core)||void 0===n?void 0:n.consentXDevice)?(console.warn(Y.CROSS_DEVICE_FEATURE_DISABLED),this.controllerIdInstance.needsSessionRestore=!1,[2]):(this.controllerIdInstance.value=e,this.controllerIdInstance.needsSessionRestore=!0,this.settingsV2.setControllerId(e),(r=this.settingsV2.dpsJson)?[4,this.dataFacadeInstance.restoreUserSession(r)]:[3,2]);case 1:i.sent()&&this.enableServicesScripts(this.processStorageServicesAndSettings(this.settingsV2.data),this.settingsV2.getGoogleConsentMode(),"onSessionRestored"),i.label=2;case 2:return[2]}}))}))},e.prototype.clearStorage=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){switch(e.label){case 0:return[4,ye.clearAll()];case 1:return e.sent(),[2]}}))}))},e.prototype.postMessageAmp=function(e,t,n){return J(this,void 0,void 0,(function(){var r,i,s,o,a,c,u,l,d=this;return q(this,(function(p){switch(p.label){case 0:return r=this.settingsV2.isTcfAvailable(),[4,this.settingsV2.isCcpaAvailable()];case 1:return i=p.sent(),s=function(){var e=d.settingsV2.getTcfData();return r&&(null==e?void 0:e.getTCString)?e.getTCString():i?ye.getCcpaString():""},o=function(){return J(d,void 0,void 0,(function(){var e,t,n,s;return q(this,(function(o){switch(o.label){case 0:return s=this.settingsV2.getTcfData(),r&&s?(e=P.TCF_V2,[4,s.getGdprApplies()]):[3,2];case 1:return t=o.sent(),n=this.settingsV2.getTCFPurposeOneTreatment(),[3,3];case 2:i&&(e=P.CCPA),o.label=3;case 3:return[2,K({consentStringType:e},r&&{gdprApplies:t,purposeOne:n})]}}))}))},c=[{action:t,type:e}],(u=e===L.CONSENT_RESPONSE&&t!==D.DISMISS&&(i||r))?(l={},[4,o()]):[3,3];case 2:l.consentMetadata=p.sent(),l.info=s(),u=l,p.label=3;case 3:return a=K.apply(void 0,[K.apply(void 0,c.concat([u])),n&&{initialHeight:n}]),[2,new Promise((function(e,t){try{window.parent.postMessage(K({},a),"*"),e()}catch(e){t(e)}}))]}}))}))},e.prototype.acceptAllAmp=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){switch(e.label){case 0:return[4,this.postMessageAmp(L.CONSENT_RESPONSE,D.ACCEPT)];case 1:return e.sent(),[2]}}))}))},e.prototype.denyAllAmp=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){switch(e.label){case 0:return[4,this.postMessageAmp(L.CONSENT_RESPONSE,D.REJECT)];case 1:return e.sent(),[2]}}))}))},e.prototype.saveTCFDataAmp=function(e){return J(this,void 0,void 0,(function(){var t;return q(this,(function(n){switch(n.label){case 0:return t=e.every((function(e){return e.status})),0===e.length||t?[4,this.postMessageAmp(L.CONSENT_RESPONSE,D.ACCEPT)]:[3,2];case 1:return n.sent(),[3,4];case 2:return[4,this.postMessageAmp(L.CONSENT_RESPONSE,D.REJECT)];case 3:n.sent(),n.label=4;case 4:return[2]}}))}))},e.prototype.dismissAmp=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){switch(e.label){case 0:return[4,this.postMessageAmp(L.CONSENT_RESPONSE,D.DISMISS)];case 1:return e.sent(),[2]}}))}))},e.prototype.enterFullscreenAmp=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){return[2,this.postMessageAmp(L.CONSENT_UI,D.FULLSCREEN)]}))}))},e.prototype.uiReadyAmp=function(){return J(this,void 0,void 0,(function(){var e,t,n;return q(this,(function(r){return e=this.settingsV2.labels,t="60vh",e&&ft(e)&&(n=e.firstLayer.description.default.length,window.screen.height>700&&n<=250&&(t="50vh")),[2,this.postMessageAmp(L.CONSENT_UI,D.READY,t)]}))}))},e.prototype.saveUserActionPerformed=function(){return J(this,void 0,void 0,(function(){return q(this,(function(e){switch(e.label){case 0:return ye.setUserActionPerformed(!0),this.setIsConsentRequired(!1),this.uiInstance.selectedLayer?[4,this.settingsV2.initUI(this.uiInstance.selectedLayer,this.uiInstance.variant)]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},e.prototype.updateServices=function(e,t){return void 0===t&&(t="explicit"),J(this,void 0,void 0,(function(){var n,r;return q(this,(function(i){switch(i.label){case 0:return n=this.settingsV2.getUpdatedServicesWithDecisions(e),r=this.settingsV2.getUpdatedServicesDataWithDecisions(e),Ae(n)&&this.dataFacadeInstance.execute(n,r,"onUpdateServices",t,{},this.initOptions.enableDeprecatedV1ConsentSaving),[4,this.saveUserActionPerformed()];case 1:return i.sent(),[2]}}))}))},e.prototype.updateLayer=function(e){return J(this,void 0,void 0,(function(){var t;return q(this,(function(n){switch(n.label){case 0:return t=this.uiInstance.selectedLayer,this.uiInstance.selectedLayer=e,e===t?[2]:[4,this.settingsV2.initUI(e,this.uiInstance.variant)];case 1:return n.sent(),3!==e?[3,3]:[4,this.loadServices()];case 2:n.sent(),n.label=3;case 3:return[2]}}))}))},e.prototype.loadServices=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return this.settingsV2.isAggregatorLoaded||!this.uiInstance.selectedLayer?[2]:[4,this.apiInstance.fetchTranslations()];case 1:return e=t.sent(),[4,this.settingsV2.extendServices(this.uiInstance.variant,e)];case 2:return t.sent(),this.apiInstance.resetAggregatedServicesCache(),[2]}}))}))},e.prototype.loadSettings=function(){return J(this,void 0,void 0,(function(){var e;return q(this,(function(t){switch(t.label){case 0:return[4,this.apiInstance.fetchSettingsJson()];case 1:return e=t.sent(),this.settingsV2.legacySettings=e,[2,e]}}))}))},e.prototype.updateStorage=function(e,t,n){return J(this,void 0,void 0,(function(){var r,i,s;return q(this,(function(o){switch(o.label){case 0:return[4,this.settingsV2.shouldDenyAllExplicitlyOnInit()];case 1:return o.sent()?(this.dataFacadeInstance.execute(this.settingsV2.getUpdatedServicesWithConsent(V.FALSE),this.settingsV2.getUpdatedServicesDataWithConsent(V.FALSE),"onDenyAllServices","explicit",{},this.initOptions.enableDeprecatedV1ConsentSaving),[3,7]):[3,2];case 2:return this.uiInstance.isFirstTimeVisit()?[4,this.settingsV2.shouldAcceptAllImplicitlyOnInit()]:[3,4];case 3:return o.sent()?this.dataFacadeInstance.execute(this.settingsV2.getUpdatedServicesWithConsent(V.TRUE),this.settingsV2.getUpdatedServicesDataWithConsent(V.TRUE),"onNonEURegion",this.botInstance.isRobot()?"explicit":"implicit",{},this.initOptions.enableDeprecatedV1ConsentSaving):this.dataFacadeInstance.execute(this.settingsV2.getServicesDataAndLabels(),this.settingsV2.getServicesData(),"onInitialPageLoad","implicit",{},this.initOptions.enableDeprecatedV1ConsentSaving),[3,7];case 4:return r=null!=t?t:this.processStorageServicesAndSettings(this.settingsV2.data),a=this.settingsV2.getServicesData(),(i=(c=e)&&!a.every((function(e){return c.find((function(t){return t.id===e.id}))}))||!1)?[4,this.settingsV2.shouldAcceptAllImplicitlyOnVendorAdded()]:[3,6];case 5:i=o.sent(),o.label=6;case 6:i?this.dataFacadeInstance.execute(this.settingsV2.getUpdatedServicesWithConsent(V.TRUE),this.settingsV2.getUpdatedServicesDataWithConsent(V.TRUE),"onNonEURegion",this.botInstance.isRobot()?"explicit":"implicit",{},this.initOptions.enableDeprecatedV1ConsentSaving):(s=n&&this.dataFacadeInstance.restoreAction?this.dataFacadeInstance.restoreAction:"onInitialPageLoad",this.enableServicesScripts(r,this.settingsV2.getGoogleConsentMode(),s),this.dataFacadeInstance.restoreAction=null),o.label=7;case 7:return[2]}var a,c}))}))},e.prototype.enableScriptsForServicesWithConsent=function(){Cn.enableScriptsForServicesWithConsent(this.settingsV2.getServicesWithConsent())},e.prototype.setTrackingPixel=function(e){var t;!this.initOptions.disableTracking&&(null===(t=this.settingsV2.coreJson)||void 0===t?void 0:t.interactionAnalytics)&&this.apiInstance.setTrackingPixel(e)},e.prototype.addSessionTrackingPixel=function(){(new Image).src=this.apiInstance.createSessionTrackingUrl()},e.prototype.processStorageServicesAndSettings=function(e){if(!e||!this.settingsV2.labels)return[];var t=this.dataFacadeInstance.getMergedServicesAndSettingsFromStorage(e),n=t.dataTransferSettings,r=t.mergedServicesData,i=t.mergedServices,s=t.updatedEssentialServices;return this.dataFacadeInstance.mergeServicesAndSettings(i,r,n,s,this.settingsV2.labels.services,this.initOptions.enableDeprecatedV1ConsentSaving)},e.prototype.enableServicesScripts=function(e,t,n){var r,i=this.settingsV2.getTcfData();i&&i.advertiserConsentModeEnabled&&(r=i.getTCFData().vendors.find((function(e){return 755===e.id}))),Cn.enableScriptsForServicesWithConsent(this.settingsV2.getServicesWithConsent()),this.eventDispatcherInstance.dispatch(e,r,t,n)},e.prototype.setIsConsentRequired=function(e){this.isConsentRequired=!0===e},e.prototype.getIsConsentRequired=function(){return this.isConsentRequired},e.prototype.getThirdPartyCount=function(){return this.settingsV2.getThirdPartyCount()},e}(),va={},Sa=[],Ea=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,_a=Array.isArray;function ma(e,t){for(var n in t)e[n]=t[n];return e}function Ia(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function ya(e,t,n){var r,i,s,o={};for(s in t)"key"==s?r=t[s]:"ref"==s?i=t[s]:o[s]=t[s];if(arguments.length>2&&(o.children=arguments.length>3?na.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(s in e.defaultProps)void 0===o[s]&&(o[s]=e.defaultProps[s]);return Ca(e,o,r,i,null)}function Ca(e,t,n,r,i){var s={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==i?++ia:i,__i:-1,__u:0};return null==i&&null!=ra.vnode&&ra.vnode(s),s}function Ta(){return{current:null}}function ba(e){return e.children}function Aa(e,t){this.props=e,this.context=t}function Oa(e,t){if(null==t)return e.__?Oa(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?Oa(e):null}function Na(e){var t,n;if(null!=(e=e.__)&&null!=e.__c){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e){e.__e=e.__c.base=n.__e;break}return Na(e)}}function wa(e){(!e.__d&&(e.__d=!0)&&oa.push(e)&&!Ra.__r++||aa!==ra.debounceRendering)&&((aa=ra.debounceRendering)||ca)(Ra)}function Ra(){var e,t,n,r,i,s,o,a;for(oa.sort(ua);e=oa.shift();)e.__d&&(t=oa.length,r=void 0,s=(i=(n=e).__v).__e,o=[],a=[],n.__P&&((r=ma({},i)).__v=i.__v+1,ra.vnode&&ra.vnode(r),Fa(n.__P,r,i,n.__n,n.__P.namespaceURI,32&i.__u?[s]:null,o,null==s?Oa(i):s,!!(32&i.__u),a),r.__v=i.__v,r.__.__k[r.__i]=r,xa(o,r,a),r.__e!=s&&Na(r)),oa.length>t&&oa.sort(ua));Ra.__r=0}function La(e,t,n,r,i,s,o,a,c,u,l){var d,p,f,h,g,v,S=r&&r.__k||Sa,E=t.length;for(c=function(e,t,n,r,i){var s,o,a,c,u,l=n.length,d=l,p=0;for(e.__k=new Array(i),s=0;s<i;s++)null!=(o=t[s])&&"boolean"!=typeof o&&"function"!=typeof o?(c=s+p,(o=e.__k[s]="string"==typeof o||"number"==typeof o||"bigint"==typeof o||o.constructor==String?Ca(null,o,null,null,null):_a(o)?Ca(ba,{children:o},null,null,null):void 0===o.constructor&&o.__b>0?Ca(o.type,o.props,o.key,o.ref?o.ref:null,o.__v):o).__=e,o.__b=e.__b+1,a=null,-1!==(u=o.__i=Va(o,n,c,d))&&(d--,(a=n[u])&&(a.__u|=2)),null==a||null===a.__v?(-1==u&&p--,"function"!=typeof o.type&&(o.__u|=4)):u!=c&&(u==c-1?p--:u==c+1?p++:(u>c?p--:p++,o.__u|=4))):e.__k[s]=null;if(d)for(s=0;s<l;s++)null!=(a=n[s])&&!(2&a.__u)&&(a.__e==r&&(r=Oa(a)),Ba(a,a));return r}(n,t,S,c,E),d=0;d<E;d++)null!=(f=n.__k[d])&&(p=-1===f.__i?va:S[f.__i]||va,f.__i=d,v=Fa(e,f,p,i,s,o,a,c,u,l),h=f.__e,f.ref&&p.ref!=f.ref&&(p.ref&&Ga(p.ref,null,f),l.push(f.ref,f.__c||h,f)),null==g&&null!=h&&(g=h),4&f.__u||p.__k===f.__k?c=Da(f,c,e):"function"==typeof f.type&&void 0!==v?c=v:h&&(c=h.nextSibling),f.__u&=-7);return n.__e=g,c}function Da(e,t,n){var r,i;if("function"==typeof e.type){for(r=e.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=e,t=Da(r[i],t,n));return t}e.__e!=t&&(t&&e.type&&!n.contains(t)&&(t=Oa(e)),n.insertBefore(e.__e,t||null),t=e.__e);do{t=t&&t.nextSibling}while(null!=t&&8==t.nodeType);return t}function Pa(e,t){return t=t||[],null==e||"boolean"==typeof e||(_a(e)?e.some((function(e){Pa(e,t)})):t.push(e)),t}function Va(e,t,n,r){var i,s,o=e.key,a=e.type,c=t[n];if(null===c||c&&o==c.key&&a===c.type&&!(2&c.__u))return n;if(r>(null==c||2&c.__u?0:1))for(i=n-1,s=n+1;i>=0||s<t.length;){if(i>=0){if((c=t[i])&&!(2&c.__u)&&o==c.key&&a===c.type)return i;i--}if(s<t.length){if((c=t[s])&&!(2&c.__u)&&o==c.key&&a===c.type)return s;s++}}return-1}function Ua(e,t,n){"-"==t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||Ea.test(t)?n:n+"px"}function ka(e,t,n,r,i){var s;e:if("style"==t)if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||Ua(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||Ua(e.style,t,n[t])}else if("o"==t[0]&&"n"==t[1])s=t!=(t=t.replace(la,"$1")),t=t.toLowerCase()in e||"onFocusOut"==t||"onFocusIn"==t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=n,n?r?n.u=r.u:(n.u=da,e.addEventListener(t,s?fa:pa,s)):e.removeEventListener(t,s?fa:pa,s);else{if("http://www.w3.org/2000/svg"==i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!=t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function Ma(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=da++;else if(t.t<n.u)return;return n(ra.event?ra.event(t):t)}}}function Fa(e,t,n,r,i,s,o,a,c,u){var l,d,p,f,h,g,v,S,E,_,m,I,y,C,T,b,A,O=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(c=!!(32&n.__u),s=[a=t.__e=n.__e]),(l=ra.__b)&&l(t);e:if("function"==typeof O)try{if(S=t.props,E="prototype"in O&&O.prototype.render,_=(l=O.contextType)&&r[l.__c],m=l?_?_.props.value:l.__:r,n.__c?v=(d=t.__c=n.__c).__=d.__E:(E?t.__c=d=new O(S,m):(t.__c=d=new Aa(S,m),d.constructor=O,d.render=Ha),_&&_.sub(d),d.props=S,d.state||(d.state={}),d.context=m,d.__n=r,p=d.__d=!0,d.__h=[],d._sb=[]),E&&null==d.__s&&(d.__s=d.state),E&&null!=O.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=ma({},d.__s)),ma(d.__s,O.getDerivedStateFromProps(S,d.__s))),f=d.props,h=d.state,d.__v=t,p)E&&null==O.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),E&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(E&&null==O.getDerivedStateFromProps&&S!==f&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(S,m),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(S,d.__s,m)||t.__v==n.__v)){for(t.__v!=n.__v&&(d.props=S,d.state=d.__s,d.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some((function(e){e&&(e.__=t)})),I=0;I<d._sb.length;I++)d.__h.push(d._sb[I]);d._sb=[],d.__h.length&&o.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(S,d.__s,m),E&&null!=d.componentDidUpdate&&d.__h.push((function(){d.componentDidUpdate(f,h,g)}))}if(d.context=m,d.props=S,d.__P=e,d.__e=!1,y=ra.__r,C=0,E){for(d.state=d.__s,d.__d=!1,y&&y(t),l=d.render(d.props,d.state,d.context),T=0;T<d._sb.length;T++)d.__h.push(d._sb[T]);d._sb=[]}else do{d.__d=!1,y&&y(t),l=d.render(d.props,d.state,d.context),d.state=d.__s}while(d.__d&&++C<25);d.state=d.__s,null!=d.getChildContext&&(r=ma(ma({},r),d.getChildContext())),E&&!p&&null!=d.getSnapshotBeforeUpdate&&(g=d.getSnapshotBeforeUpdate(f,h)),a=La(e,_a(b=null!=l&&l.type===ba&&null==l.key?l.props.children:l)?b:[b],t,n,r,i,s,o,a,c,u),d.base=t.__e,t.__u&=-161,d.__h.length&&o.push(d),v&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=s)if(e.then){for(t.__u|=c?160:128;a&&8==a.nodeType&&a.nextSibling;)a=a.nextSibling;s[s.indexOf(a)]=null,t.__e=a}else for(A=s.length;A--;)Ia(s[A]);else t.__e=n.__e,t.__k=n.__k;ra.__e(e,t,n)}else null==s&&t.__v==n.__v?(t.__k=n.__k,t.__e=n.__e):a=t.__e=function(e,t,n,r,i,s,o,a,c){var u,l,d,p,f,h,g,v=n.props,S=t.props,E=t.type;if("svg"==E?i="http://www.w3.org/2000/svg":"math"==E?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),null!=s)for(u=0;u<s.length;u++)if((f=s[u])&&"setAttribute"in f==!!E&&(E?f.localName==E:3==f.nodeType)){e=f,s[u]=null;break}if(null==e){if(null==E)return document.createTextNode(S);e=document.createElementNS(i,E,S.is&&S),a&&(ra.__m&&ra.__m(t,s),a=!1),s=null}if(null===E)v===S||a&&e.data===S||(e.data=S);else{if(s=s&&na.call(e.childNodes),v=n.props||va,!a&&null!=s)for(v={},u=0;u<e.attributes.length;u++)v[(f=e.attributes[u]).name]=f.value;for(u in v)if(f=v[u],"children"==u);else if("dangerouslySetInnerHTML"==u)d=f;else if(!(u in S)){if("value"==u&&"defaultValue"in S||"checked"==u&&"defaultChecked"in S)continue;ka(e,u,null,f,i)}for(u in S)f=S[u],"children"==u?p=f:"dangerouslySetInnerHTML"==u?l=f:"value"==u?h=f:"checked"==u?g=f:a&&"function"!=typeof f||v[u]===f||ka(e,u,f,v[u],i);if(l)a||d&&(l.__html===d.__html||l.__html===e.innerHTML)||(e.innerHTML=l.__html),t.__k=[];else if(d&&(e.innerHTML=""),La(e,_a(p)?p:[p],t,n,r,"foreignObject"==E?"http://www.w3.org/1999/xhtml":i,s,o,s?s[0]:n.__k&&Oa(n,0),a,c),null!=s)for(u=s.length;u--;)Ia(s[u]);a||(u="value","progress"==E&&null==h?e.removeAttribute("value"):void 0!==h&&(h!==e[u]||"progress"==E&&!h||"option"==E&&h!==v[u])&&ka(e,u,h,v[u],i),u="checked",void 0!==g&&g!==e[u]&&ka(e,u,g,v[u],i))}return e}(n.__e,t,n,r,i,s,o,c,u);return(l=ra.diffed)&&l(t),128&t.__u?void 0:a}function xa(e,t,n){for(var r=0;r<n.length;r++)Ga(n[r],n[++r],n[++r]);ra.__c&&ra.__c(t,e),e.some((function(t){try{e=t.__h,t.__h=[],e.some((function(e){e.call(t)}))}catch(e){ra.__e(e,t.__v)}}))}function Ga(e,t,n){try{if("function"==typeof e){var r="function"==typeof e.__u;r&&e.__u(),r&&null==t||(e.__u=e(t))}else e.current=t}catch(e){ra.__e(e,n)}}function Ba(e,t,n){var r,i;if(ra.unmount&&ra.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||Ga(r,null,t)),null!=(r=e.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){ra.__e(e,t)}r.base=r.__P=null}if(r=e.__k)for(i=0;i<r.length;i++)r[i]&&Ba(r[i],t,n||"function"!=typeof e.type);n||Ia(e.__e),e.__c=e.__=e.__e=void 0}function Ha(e,t,n){return this.constructor(e,n)}function ja(e,t,n){var r,i,s,o;t==document&&(t=document.documentElement),ra.__&&ra.__(e,t),i=(r="function"==typeof n)?null:n&&n.__k||t.__k,s=[],o=[],Fa(t,e=(!r&&n||t).__k=ya(ba,null,[e]),i||va,va,t.namespaceURI,!r&&n?[n]:i?null:t.firstChild?na.call(t.childNodes):null,s,!r&&n?n:i?i.__e:t.firstChild,r,o),xa(s,e,o)}function $a(e,t){ja(e,t,$a)}function Ya(e,t,n){var r,i,s,o,a=ma({},e.props);for(s in e.type&&e.type.defaultProps&&(o=e.type.defaultProps),t)"key"==s?r=t[s]:"ref"==s?i=t[s]:a[s]=void 0===t[s]&&void 0!==o?o[s]:t[s];return arguments.length>2&&(a.children=arguments.length>3?na.call(arguments,2):n),Ca(e.type,a,r||e.key,i||e.ref,null)}function Wa(e,t){var n={__c:t="__cC"+ha++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=new Set,(r={})[t]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.forEach((function(e){e.__e=!0,wa(e)}))},this.sub=function(e){n.add(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n&&n.delete(e),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}na=Sa.slice,ra={__e:function(e,t,n,r){for(var i,s,o;t=t.__;)if((i=t.__c)&&!i.__)try{if((s=i.constructor)&&null!=s.getDerivedStateFromError&&(i.setState(s.getDerivedStateFromError(e)),o=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,r||{}),o=i.__d),o)return i.__E=i}catch(t){e=t}throw e}},ia=0,sa=function(e){return null!=e&&null==e.constructor},Aa.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=ma({},this.state),"function"==typeof e&&(e=e(ma({},n),this.props)),e&&ma(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),wa(this))},Aa.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),wa(this))},Aa.prototype.render=ba,oa=[],ca="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,ua=function(e,t){return e.__v.__b-t.__v.__b},Ra.__r=0,la=/(PointerCapture)$|Capture$/i,da=0,pa=Ma(!1),fa=Ma(!0),ha=0;var za,Ka,Ja,qa,Xa=0,Qa=[],Za=ra,ec=Za.__b,tc=Za.__r,nc=Za.diffed,rc=Za.__c,ic=Za.unmount,sc=Za.__;function oc(e,t){Za.__h&&Za.__h(Ka,e,Xa||t),Xa=0;var n=Ka.__H||(Ka.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function ac(e){return Xa=1,cc(Tc,e)}function cc(e,t,n){var r=oc(za++,2);if(r.t=e,!r.__c&&(r.__=[n?n(t):Tc(void 0,t),function(e){var t=r.__N?r.__N[0]:r.__[0],n=r.t(t,e);t!==n&&(r.__N=[n,r.__[1]],r.__c.setState({}))}],r.__c=Ka,!Ka.u)){var i=function(e,t,n){if(!r.__c.__H)return!0;var i=r.__c.__H.__.filter((function(e){return!!e.__c}));if(i.every((function(e){return!e.__N})))return!s||s.call(this,e,t,n);var o=r.__c.props!==e;return i.forEach((function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(o=!0)}})),s&&s.call(this,e,t,n)||o};Ka.u=!0;var s=Ka.shouldComponentUpdate,o=Ka.componentWillUpdate;Ka.componentWillUpdate=function(e,t,n){if(this.__e){var r=s;s=void 0,i(e,t,n),s=r}o&&o.call(this,e,t,n)},Ka.shouldComponentUpdate=i}return r.__N||r.__}function uc(e,t){var n=oc(za++,3);!Za.__s&&Cc(n.__H,t)&&(n.__=e,n.i=t,Ka.__H.__h.push(n))}function lc(e,t){var n=oc(za++,4);!Za.__s&&Cc(n.__H,t)&&(n.__=e,n.i=t,Ka.__h.push(n))}function dc(e){return Xa=5,fc((function(){return{current:e}}),[])}function pc(e,t,n){Xa=6,lc((function(){return"function"==typeof e?(e(t()),function(){return e(null)}):e?(e.current=t(),function(){return e.current=null}):void 0}),null==n?n:n.concat(e))}function fc(e,t){var n=oc(za++,7);return Cc(n.__H,t)&&(n.__=e(),n.__H=t,n.__h=e),n.__}function hc(e,t){return Xa=8,fc((function(){return e}),t)}function gc(e){var t=Ka.context[e.__c],n=oc(za++,9);return n.c=e,t?(null==n.__&&(n.__=!0,t.sub(Ka)),t.props.value):e.__}function vc(e,t){Za.useDebugValue&&Za.useDebugValue(t?t(e):e)}function Sc(){var e=oc(za++,11);if(!e.__){for(var t=Ka.__v;null!==t&&!t.__m&&null!==t.__;)t=t.__;var n=t.__m||(t.__m=[0,0]);e.__="P"+n[0]+"-"+n[1]++}return e.__}function Ec(){for(var e;e=Qa.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(Ic),e.__H.__h.forEach(yc),e.__H.__h=[]}catch(t){e.__H.__h=[],Za.__e(t,e.__v)}}Za.__b=function(e){Ka=null,ec&&ec(e)},Za.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),sc&&sc(e,t)},Za.__r=function(e){tc&&tc(e),za=0;var t=(Ka=e.__c).__H;t&&(Ja===Ka?(t.__h=[],Ka.__h=[],t.__.forEach((function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0}))):(t.__h.forEach(Ic),t.__h.forEach(yc),t.__h=[],za=0)),Ja=Ka},Za.diffed=function(e){nc&&nc(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==Qa.push(t)&&qa===Za.requestAnimationFrame||((qa=Za.requestAnimationFrame)||mc)(Ec)),t.__H.__.forEach((function(e){e.i&&(e.__H=e.i),e.i=void 0}))),Ja=Ka=null},Za.__c=function(e,t){t.some((function(e){try{e.__h.forEach(Ic),e.__h=e.__h.filter((function(e){return!e.__||yc(e)}))}catch(n){t.some((function(e){e.__h&&(e.__h=[])})),t=[],Za.__e(n,e.__v)}})),rc&&rc(e,t)},Za.unmount=function(e){ic&&ic(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach((function(e){try{Ic(e)}catch(e){t=e}})),n.__H=void 0,t&&Za.__e(t,n.__v))};var _c="function"==typeof requestAnimationFrame;function mc(e){var t,n=function(){clearTimeout(r),_c&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);_c&&(t=requestAnimationFrame(n))}function Ic(e){var t=Ka,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),Ka=t}function yc(e){var t=Ka;e.__c=e.__(),Ka=t}function Cc(e,t){return!e||e.length!==t.length||t.some((function(t,n){return t!==e[n]}))}function Tc(e,t){return"function"==typeof t?t(e):t}function bc(e,t){for(var n in t)e[n]=t[n];return e}function Ac(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function Oc(e,t){var n=t(),r=ac({t:{__:n,u:t}}),i=r[0].t,s=r[1];return lc((function(){i.__=n,i.u=t,Nc(i)&&s({t:i})}),[e,n,t]),uc((function(){return Nc(i)&&s({t:i}),e((function(){Nc(i)&&s({t:i})}))}),[e]),n}function Nc(e){var t,n,r=e.u,i=e.__;try{var s=r();return!((t=i)===(n=s)&&(0!==t||1/t==1/n)||t!=t&&n!=n)}catch(e){return!0}}function wc(e){e()}function Rc(e){return e}function Lc(){return[!1,wc]}var Dc=lc;function Pc(e,t){this.props=e,this.context=t}function Vc(e,t){function n(e){var n=this.props.ref,r=n==e.ref;return!r&&n&&(n.call?n(null):n.current=null),t?!t(this.props,e)||!r:Ac(this.props,e)}function r(t){return this.shouldComponentUpdate=n,ya(e,t)}return r.displayName="Memo("+(e.displayName||e.name)+")",r.prototype.isReactComponent=!0,r.__f=!0,r}(Pc.prototype=new Aa).isPureReactComponent=!0,Pc.prototype.shouldComponentUpdate=function(e,t){return Ac(this.props,e)||Ac(this.state,t)};var Uc=ra.__b;ra.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),Uc&&Uc(e)};var kc="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function Mc(e){function t(t){var n=bc({},t);return delete n.ref,e(n,t.ref||null)}return t.$$typeof=kc,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(e.displayName||e.name)+")",t}var Fc=function(e,t){return null==e?null:Pa(Pa(e).map(t))},xc={map:Fc,forEach:Fc,count:function(e){return e?Pa(e).length:0},only:function(e){var t=Pa(e);if(1!==t.length)throw"Children.only";return t[0]},toArray:Pa},Gc=ra.__e;ra.__e=function(e,t,n,r){if(e.then)for(var i,s=t;s=s.__;)if((i=s.__c)&&i.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),i.__c(e,t);Gc(e,t,n,r)};var Bc=ra.unmount;function Hc(e,t,n){return e&&(e.__c&&e.__c.__H&&(e.__c.__H.__.forEach((function(e){"function"==typeof e.__c&&e.__c()})),e.__c.__H=null),null!=(e=bc({},e)).__c&&(e.__c.__P===n&&(e.__c.__P=t),e.__c=null),e.__k=e.__k&&e.__k.map((function(e){return Hc(e,t,n)}))),e}function jc(e,t,n){return e&&n&&(e.__v=null,e.__k=e.__k&&e.__k.map((function(e){return jc(e,t,n)})),e.__c&&e.__c.__P===t&&(e.__e&&n.appendChild(e.__e),e.__c.__e=!0,e.__c.__P=n)),e}function $c(){this.__u=0,this.o=null,this.__b=null}function Yc(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function Wc(e){var t,n,r;function i(i){if(t||(t=e()).then((function(e){n=e.default||e}),(function(e){r=e})),r)throw r;if(!n)throw t;return ya(n,i)}return i.displayName="Lazy",i.__f=!0,i}function zc(){this.i=null,this.l=null}ra.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&32&e.__u&&(e.type=null),Bc&&Bc(e)},($c.prototype=new Aa).__c=function(e,t){var n=t.__c,r=this;null==r.o&&(r.o=[]),r.o.push(n);var i=Yc(r.__v),s=!1,o=function(){s||(s=!0,n.__R=null,i?i(a):a())};n.__R=o;var a=function(){if(! --r.__u){if(r.state.__a){var e=r.state.__a;r.__v.__k[0]=jc(e,e.__c.__P,e.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.o.pop();)t.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(o,o)},$c.prototype.componentWillUnmount=function(){this.o=[]},$c.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=Hc(this.__b,n,r.__O=r.__P)}this.__b=null}var i=t.__a&&ya(ba,null,e.fallback);return i&&(i.__u&=-33),[ya(ba,null,t.__a?null:e.children),i]};var Kc=function(e,t,n){if(++n[1]===n[0]&&e.l.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.l.size))for(n=e.i;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.i=n=n[2]}};function Jc(e){return this.getChildContext=function(){return e.context},e.children}function qc(e){var t=this,n=e.h;t.componentWillUnmount=function(){ja(null,t.v),t.v=null,t.h=null},t.h&&t.h!==n&&t.componentWillUnmount(),t.v||(t.h=n,t.v={nodeType:1,parentNode:n,childNodes:[],contains:function(){return!0},appendChild:function(e){this.childNodes.push(e),t.h.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.h.insertBefore(e,n)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.h.removeChild(e)}}),ja(ya(Jc,{context:t.context},e.__v),t.v)}function Xc(e,t){var n=ya(qc,{__v:e,h:t});return n.containerInfo=t,n}(zc.prototype=new Aa).__a=function(e){var t=this,n=Yc(t.__v),r=t.l.get(e);return r[0]++,function(i){var s=function(){t.props.revealOrder?(r.push(i),Kc(t,e,r)):i()};n?n(s):s()}},zc.prototype.render=function(e){this.i=null,this.l=new Map;var t=Pa(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.l.set(t[n],this.i=[1,0,this.i]);return e.children},zc.prototype.componentDidUpdate=zc.prototype.componentDidMount=function(){var e=this;this.l.forEach((function(t,n){Kc(e,n,t)}))};var Qc="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,Zc=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,eu=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,tu=/[A-Z0-9]/g,nu="undefined"!=typeof document,ru=function(e){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(e)};Aa.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach((function(e){Object.defineProperty(Aa.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})}));var iu=ra.event;function su(){}function ou(){return this.cancelBubble}function au(){return this.defaultPrevented}ra.event=function(e){return iu&&(e=iu(e)),e.persist=su,e.isPropagationStopped=ou,e.isDefaultPrevented=au,e.nativeEvent=e};var cu,uu={enumerable:!1,configurable:!0,get:function(){return this.class}},lu=ra.vnode;ra.vnode=function(e){"string"==typeof e.type&&function(e){var t=e.props,n=e.type,r={},i=-1===n.indexOf("-");for(var s in t){var o=t[s];if(!("value"===s&&"defaultValue"in t&&null==o||nu&&"children"===s&&"noscript"===n||"class"===s||"className"===s)){var a=s.toLowerCase();"defaultValue"===s&&"value"in t&&null==t.value?s="value":"download"===s&&!0===o?o="":"translate"===a&&"no"===o?o=!1:"o"===a[0]&&"n"===a[1]?"ondoubleclick"===a?s="ondblclick":"onchange"!==a||"input"!==n&&"textarea"!==n||ru(t.type)?"onfocus"===a?s="onfocusin":"onblur"===a?s="onfocusout":eu.test(s)&&(s=a):a=s="oninput":i&&Zc.test(s)?s=s.replace(tu,"-$&").toLowerCase():null===o&&(o=void 0),"oninput"===a&&r[s=a]&&(s="oninputCapture"),r[s]=o}}"select"==n&&r.multiple&&Array.isArray(r.value)&&(r.value=Pa(t.children).forEach((function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)}))),"select"==n&&null!=r.defaultValue&&(r.value=Pa(t.children).forEach((function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value}))),t.class&&!t.className?(r.class=t.class,Object.defineProperty(r,"className",uu)):(t.className&&!t.class||t.class&&t.className)&&(r.class=r.className=t.className),e.props=r}(e),e.$$typeof=Qc,lu&&lu(e)};var du=ra.__r;ra.__r=function(e){du&&du(e),cu=e.__c};var pu=ra.diffed;ra.diffed=function(e){pu&&pu(e);var t=e.props,n=e.__e;null!=n&&"textarea"===e.type&&"value"in t&&t.value!==n.value&&(n.value=null==t.value?"":t.value),cu=null};var fu={ReactCurrentDispatcher:{current:{readContext:function(e){return cu.__n[e.__c].props.value},useCallback:hc,useContext:gc,useDebugValue:vc,useDeferredValue:Rc,useEffect:uc,useId:Sc,useImperativeHandle:pc,useInsertionEffect:Dc,useLayoutEffect:lc,useMemo:fc,useReducer:cc,useRef:dc,useState:ac,useSyncExternalStore:Oc,useTransition:Lc}}};function hu(e){return!!e&&e.$$typeof===Qc}var gu={useState:ac,useId:Sc,useReducer:cc,useEffect:uc,useLayoutEffect:lc,useInsertionEffect:Dc,useTransition:Lc,useDeferredValue:Rc,useSyncExternalStore:Oc,startTransition:wc,useRef:dc,useImperativeHandle:pc,useMemo:fc,useCallback:hc,useContext:gc,useDebugValue:vc,version:"18.3.1",Children:xc,render:function(e,t,n){return null==t.__k&&(t.textContent=""),ja(e,t),"function"==typeof n&&n(),e?e.__c:null},hydrate:function(e,t,n){return $a(e,t),"function"==typeof n&&n(),e?e.__c:null},unmountComponentAtNode:function(e){return!!e.__k&&(ja(null,e),!0)},createPortal:Xc,createElement:ya,createContext:Wa,createFactory:function(e){return ya.bind(null,e)},cloneElement:function(e){return hu(e)?Ya.apply(null,arguments):e},createRef:Ta,Fragment:ba,isValidElement:hu,isElement:hu,isFragment:function(e){return hu(e)&&e.type===ba},isMemo:function(e){return!!e&&!!e.displayName&&("string"==typeof e.displayName||e.displayName instanceof String)&&e.displayName.startsWith("Memo(")},findDOMNode:function(e){return e&&(e.base||1===e.nodeType&&e)||null},Component:Aa,PureComponent:Pc,memo:Vc,forwardRef:Mc,flushSync:function(e,t){return e(t)},unstable_batchedUpdates:function(e,t){return e(t)},StrictMode:ba,Suspense:$c,SuspenseList:zc,lazy:Wc,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:fu},vu={exports:{}},Su={},Eu=60103,_u=60106,mu=60107,Iu=60108,yu=60114,Cu=60109,Tu=60110,bu=60112,Au=60113,Ou=60120,Nu=60115,wu=60116,Ru=60121,Lu=60122,Du=60117,Pu=60129,Vu=60131;if("function"==typeof Symbol&&Symbol.for){var Uu=Symbol.for;Eu=Uu("react.element"),_u=Uu("react.portal"),mu=Uu("react.fragment"),Iu=Uu("react.strict_mode"),yu=Uu("react.profiler"),Cu=Uu("react.provider"),Tu=Uu("react.context"),bu=Uu("react.forward_ref"),Au=Uu("react.suspense"),Ou=Uu("react.suspense_list"),Nu=Uu("react.memo"),wu=Uu("react.lazy"),Ru=Uu("react.block"),Lu=Uu("react.server.block"),Du=Uu("react.fundamental"),Pu=Uu("react.debug_trace_mode"),Vu=Uu("react.legacy_hidden")}function ku(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case Eu:switch(e=e.type){case mu:case yu:case Iu:case Au:case Ou:return e;default:switch(e=e&&e.$$typeof){case Tu:case bu:case wu:case Nu:case Cu:return e;default:return t}}case _u:return t}}}var Mu=Cu,Fu=Eu,xu=bu,Gu=mu,Bu=wu,Hu=Nu,ju=_u,$u=yu,Yu=Iu,Wu=Au;Su.ContextConsumer=Tu,Su.ContextProvider=Mu,Su.Element=Fu,Su.ForwardRef=xu,Su.Fragment=Gu,Su.Lazy=Bu,Su.Memo=Hu,Su.Portal=ju,Su.Profiler=$u,Su.StrictMode=Yu,Su.Suspense=Wu,Su.isAsyncMode=function(){return!1},Su.isConcurrentMode=function(){return!1},Su.isContextConsumer=function(e){return ku(e)===Tu},Su.isContextProvider=function(e){return ku(e)===Cu},Su.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===Eu},Su.isForwardRef=function(e){return ku(e)===bu},Su.isFragment=function(e){return ku(e)===mu},Su.isLazy=function(e){return ku(e)===wu},Su.isMemo=function(e){return ku(e)===Nu},Su.isPortal=function(e){return ku(e)===_u},Su.isProfiler=function(e){return ku(e)===yu},Su.isStrictMode=function(e){return ku(e)===Iu},Su.isSuspense=function(e){return ku(e)===Au},Su.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===mu||e===yu||e===Pu||e===Iu||e===Au||e===Ou||e===Vu||"object"==typeof e&&null!==e&&(e.$$typeof===wu||e.$$typeof===Nu||e.$$typeof===Cu||e.$$typeof===Tu||e.$$typeof===bu||e.$$typeof===Du||e.$$typeof===Ru||e[0]===Lu)},Su.typeOf=ku,vu.exports=Su;function zu(e){function t(e,r,c,u,p){for(var f,h,g,v,m,y=0,C=0,T=0,b=0,A=0,D=0,V=g=f=0,k=0,M=0,F=0,x=0,G=c.length,B=G-1,H="",j="",$="",Y="";k<G;){if(h=c.charCodeAt(k),k===B&&0!==C+b+T+y&&(0!==C&&(h=47===C?10:47),b=T=y=0,G++,B++),0===C+b+T+y){if(k===B&&(0<M&&(H=H.replace(d,"")),0<H.trim().length)){switch(h){case 32:case 9:case 59:case 13:case 10:break;default:H+=c.charAt(k)}h=59}switch(h){case 123:for(f=(H=H.trim()).charCodeAt(0),g=1,x=++k;k<G;){switch(h=c.charCodeAt(k)){case 123:g++;break;case 125:g--;break;case 47:switch(h=c.charCodeAt(k+1)){case 42:case 47:e:{for(V=k+1;V<B;++V)switch(c.charCodeAt(V)){case 47:if(42===h&&42===c.charCodeAt(V-1)&&k+2!==V){k=V+1;break e}break;case 10:if(47===h){k=V+1;break e}}k=V}}break;case 91:h++;case 40:h++;case 34:case 39:for(;k++<B&&c.charCodeAt(k)!==h;);}if(0===g)break;k++}if(g=c.substring(x,k),0===f&&(f=(H=H.replace(l,"").trim()).charCodeAt(0)),64===f){switch(0<M&&(H=H.replace(d,"")),h=H.charCodeAt(1)){case 100:case 109:case 115:case 45:M=r;break;default:M=L}if(x=(g=t(r,M,g,h,p+1)).length,0<P&&(m=a(3,g,M=n(L,H,F),r,N,O,x,h,p,u),H=M.join(""),void 0!==m&&0===(x=(g=m.trim()).length)&&(h=0,g="")),0<x)switch(h){case 115:H=H.replace(I,o);case 100:case 109:case 45:g=H+"{"+g+"}";break;case 107:g=(H=H.replace(S,"$1 $2"))+"{"+g+"}",g=1===R||2===R&&s("@"+g,3)?"@-webkit-"+g+"@"+g:"@"+g;break;default:g=H+g,112===u&&(j+=g,g="")}else g=""}else g=t(r,n(r,H,F),g,u,p+1);$+=g,g=F=M=V=f=0,H="",h=c.charCodeAt(++k);break;case 125:case 59:if(1<(x=(H=(0<M?H.replace(d,""):H).trim()).length))switch(0===V&&(f=H.charCodeAt(0),45===f||96<f&&123>f)&&(x=(H=H.replace(" ",":")).length),0<P&&void 0!==(m=a(1,H,r,e,N,O,j.length,u,p,u))&&0===(x=(H=m.trim()).length)&&(H="\0\0"),f=H.charCodeAt(0),h=H.charCodeAt(1),f){case 0:break;case 64:if(105===h||99===h){Y+=H+c.charAt(k);break}default:58!==H.charCodeAt(x-1)&&(j+=i(H,f,h,H.charCodeAt(2)))}F=M=V=f=0,H="",h=c.charCodeAt(++k)}}switch(h){case 13:case 10:47===C?C=0:0===1+f&&107!==u&&0<H.length&&(M=1,H+="\0"),0<P*U&&a(0,H,r,e,N,O,j.length,u,p,u),O=1,N++;break;case 59:case 125:if(0===C+b+T+y){O++;break}default:switch(O++,v=c.charAt(k),h){case 9:case 32:if(0===b+y+C)switch(A){case 44:case 58:case 9:case 32:v="";break;default:32!==h&&(v=" ")}break;case 0:v="\\0";break;case 12:v="\\f";break;case 11:v="\\v";break;case 38:0===b+C+y&&(M=F=1,v="\f"+v);break;case 108:if(0===b+C+y+w&&0<V)switch(k-V){case 2:112===A&&58===c.charCodeAt(k-3)&&(w=A);case 8:111===D&&(w=D)}break;case 58:0===b+C+y&&(V=k);break;case 44:0===C+T+b+y&&(M=1,v+="\r");break;case 34:case 39:0===C&&(b=b===h?0:0===b?h:b);break;case 91:0===b+C+T&&y++;break;case 93:0===b+C+T&&y--;break;case 41:0===b+C+y&&T--;break;case 40:if(0===b+C+y){if(0===f)if(2*A+3*D==533);else f=1;T++}break;case 64:0===C+T+b+y+V+g&&(g=1);break;case 42:case 47:if(!(0<b+y+T))switch(C){case 0:switch(2*h+3*c.charCodeAt(k+1)){case 235:C=47;break;case 220:x=k,C=42}break;case 42:47===h&&42===A&&x+2!==k&&(33===c.charCodeAt(x+2)&&(j+=c.substring(x,k+1)),v="",C=0)}}0===C&&(H+=v)}D=A,A=h,k++}if(0<(x=j.length)){if(M=r,0<P&&(void 0!==(m=a(2,j,M,e,N,O,x,u,p,u))&&0===(j=m).length))return Y+j+$;if(j=M.join(",")+"{"+j+"}",0!=R*w){switch(2!==R||s(j,2)||(w=0),w){case 111:j=j.replace(_,":-moz-$1")+j;break;case 112:j=j.replace(E,"::-webkit-input-$1")+j.replace(E,"::-moz-$1")+j.replace(E,":-ms-input-$1")+j}w=0}}return Y+j+$}function n(e,t,n){var i=t.trim().split(g);t=i;var s=i.length,o=e.length;switch(o){case 0:case 1:var a=0;for(e=0===o?"":e[0]+" ";a<s;++a)t[a]=r(e,t[a],n).trim();break;default:var c=a=0;for(t=[];a<s;++a)for(var u=0;u<o;++u)t[c++]=r(e[u]+" ",i[a],n).trim()}return t}function r(e,t,n){var r=t.charCodeAt(0);switch(33>r&&(r=(t=t.trim()).charCodeAt(0)),r){case 38:return t.replace(v,"$1"+e.trim());case 58:return e.trim()+t.replace(v,"$1"+e.trim());default:if(0<1*n&&0<t.indexOf("\f"))return t.replace(v,(58===e.charCodeAt(0)?"":"$1")+e.trim())}return e+t}function i(e,t,n,r){var o=e+";",a=2*t+3*n+4*r;if(944===a){e=o.indexOf(":",9)+1;var c=o.substring(e,o.length-1).trim();return c=o.substring(0,e).trim()+c+";",1===R||2===R&&s(c,1)?"-webkit-"+c+c:c}if(0===R||2===R&&!s(o,1))return o;switch(a){case 1015:return 97===o.charCodeAt(10)?"-webkit-"+o+o:o;case 951:return 116===o.charCodeAt(3)?"-webkit-"+o+o:o;case 963:return 110===o.charCodeAt(5)?"-webkit-"+o+o:o;case 1009:if(100!==o.charCodeAt(4))break;case 969:case 942:return"-webkit-"+o+o;case 978:return"-webkit-"+o+"-moz-"+o+o;case 1019:case 983:return"-webkit-"+o+"-moz-"+o+"-ms-"+o+o;case 883:if(45===o.charCodeAt(8))return"-webkit-"+o+o;if(0<o.indexOf("image-set(",11))return o.replace(A,"$1-webkit-$2")+o;break;case 932:if(45===o.charCodeAt(4))switch(o.charCodeAt(5)){case 103:return"-webkit-box-"+o.replace("-grow","")+"-webkit-"+o+"-ms-"+o.replace("grow","positive")+o;case 115:return"-webkit-"+o+"-ms-"+o.replace("shrink","negative")+o;case 98:return"-webkit-"+o+"-ms-"+o.replace("basis","preferred-size")+o}return"-webkit-"+o+"-ms-"+o+o;case 964:return"-webkit-"+o+"-ms-flex-"+o+o;case 1023:if(99!==o.charCodeAt(8))break;return"-webkit-box-pack"+(c=o.substring(o.indexOf(":",15)).replace("flex-","").replace("space-between","justify"))+"-webkit-"+o+"-ms-flex-pack"+c+o;case 1005:return f.test(o)?o.replace(p,":-webkit-")+o.replace(p,":-moz-")+o:o;case 1e3:switch(t=(c=o.substring(13).trim()).indexOf("-")+1,c.charCodeAt(0)+c.charCodeAt(t)){case 226:c=o.replace(m,"tb");break;case 232:c=o.replace(m,"tb-rl");break;case 220:c=o.replace(m,"lr");break;default:return o}return"-webkit-"+o+"-ms-"+c+o;case 1017:if(-1===o.indexOf("sticky",9))break;case 975:switch(t=(o=e).length-10,a=(c=(33===o.charCodeAt(t)?o.substring(0,t):o).substring(e.indexOf(":",7)+1).trim()).charCodeAt(0)+(0|c.charCodeAt(7))){case 203:if(111>c.charCodeAt(8))break;case 115:o=o.replace(c,"-webkit-"+c)+";"+o;break;case 207:case 102:o=o.replace(c,"-webkit-"+(102<a?"inline-":"")+"box")+";"+o.replace(c,"-webkit-"+c)+";"+o.replace(c,"-ms-"+c+"box")+";"+o}return o+";";case 938:if(45===o.charCodeAt(5))switch(o.charCodeAt(6)){case 105:return c=o.replace("-items",""),"-webkit-"+o+"-webkit-box-"+c+"-ms-flex-"+c+o;case 115:return"-webkit-"+o+"-ms-flex-item-"+o.replace(C,"")+o;default:return"-webkit-"+o+"-ms-flex-line-pack"+o.replace("align-content","").replace(C,"")+o}break;case 973:case 989:if(45!==o.charCodeAt(3)||122===o.charCodeAt(4))break;case 931:case 953:if(!0===b.test(e))return 115===(c=e.substring(e.indexOf(":")+1)).charCodeAt(0)?i(e.replace("stretch","fill-available"),t,n,r).replace(":fill-available",":stretch"):o.replace(c,"-webkit-"+c)+o.replace(c,"-moz-"+c.replace("fill-",""))+o;break;case 962:if(o="-webkit-"+o+(102===o.charCodeAt(5)?"-ms-"+o:"")+o,211===n+r&&105===o.charCodeAt(13)&&0<o.indexOf("transform",10))return o.substring(0,o.indexOf(";",27)+1).replace(h,"$1-webkit-$2")+o}return o}function s(e,t){var n=e.indexOf(1===t?":":"{"),r=e.substring(0,3!==t?n:10);return n=e.substring(n+1,e.length-1),V(2!==t?r:r.replace(T,"$1"),n,t)}function o(e,t){var n=i(t,t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2));return n!==t+";"?n.replace(y," or ($1)").substring(4):"("+t+")"}function a(e,t,n,r,i,s,o,a,c,l){for(var d,p=0,f=t;p<P;++p)switch(d=D[p].call(u,e,f,n,r,i,s,o,a,c,l)){case void 0:case!1:case!0:case null:break;default:f=d}if(f!==t)return f}function c(e){return void 0!==(e=e.prefix)&&(V=null,e?"function"!=typeof e?R=1:(R=2,V=e):R=0),c}function u(e,n){var r=e;if(33>r.charCodeAt(0)&&(r=r.trim()),r=[r],0<P){var i=a(-1,n,r,r,N,O,0,0,0,0);void 0!==i&&"string"==typeof i&&(n=i)}var s=t(L,r,n,0,0);return 0<P&&(void 0!==(i=a(-2,s,r,r,N,O,s.length,0,0,0))&&(s=i)),w=0,O=N=1,s}var l=/^\0+/g,d=/[\0\r\f]/g,p=/: */g,f=/zoo|gra/,h=/([,: ])(transform)/g,g=/,\r+?/g,v=/([\t\r\n ])*\f?&/g,S=/@(k\w+)\s*(\S*)\s*/,E=/::(place)/g,_=/:(read-only)/g,m=/[svh]\w+-[tblr]{2}/,I=/\(\s*(.*)\s*\)/g,y=/([\s\S]*?);/g,C=/-self|flex-/g,T=/[^]*?(:[rp][el]a[\w-]+)[^]*/,b=/stretch|:\s*\w+\-(?:conte|avail)/,A=/([^-])(image-set\()/,O=1,N=1,w=0,R=1,L=[],D=[],P=0,V=null,U=0;return u.use=function e(t){switch(t){case void 0:case null:P=D.length=0;break;default:if("function"==typeof t)D[P++]=t;else if("object"==typeof t)for(var n=0,r=t.length;n<r;++n)e(t[n]);else U=0|!!t}return e},u.set=c,void 0!==e&&c(e),u}var Ku={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function Ju(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var qu=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Xu=Ju((function(e){return qu.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),Qu={exports:{}},Zu={},el="function"==typeof Symbol&&Symbol.for,tl=el?Symbol.for("react.element"):60103,nl=el?Symbol.for("react.portal"):60106,rl=el?Symbol.for("react.fragment"):60107,il=el?Symbol.for("react.strict_mode"):60108,sl=el?Symbol.for("react.profiler"):60114,ol=el?Symbol.for("react.provider"):60109,al=el?Symbol.for("react.context"):60110,cl=el?Symbol.for("react.async_mode"):60111,ul=el?Symbol.for("react.concurrent_mode"):60111,ll=el?Symbol.for("react.forward_ref"):60112,dl=el?Symbol.for("react.suspense"):60113,pl=el?Symbol.for("react.suspense_list"):60120,fl=el?Symbol.for("react.memo"):60115,hl=el?Symbol.for("react.lazy"):60116,gl=el?Symbol.for("react.block"):60121,vl=el?Symbol.for("react.fundamental"):60117,Sl=el?Symbol.for("react.responder"):60118,El=el?Symbol.for("react.scope"):60119;function _l(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case tl:switch(e=e.type){case cl:case ul:case rl:case sl:case il:case dl:return e;default:switch(e=e&&e.$$typeof){case al:case ll:case hl:case fl:case ol:return e;default:return t}}case nl:return t}}}function ml(e){return _l(e)===ul}Zu.AsyncMode=cl,Zu.ConcurrentMode=ul,Zu.ContextConsumer=al,Zu.ContextProvider=ol,Zu.Element=tl,Zu.ForwardRef=ll,Zu.Fragment=rl,Zu.Lazy=hl,Zu.Memo=fl,Zu.Portal=nl,Zu.Profiler=sl,Zu.StrictMode=il,Zu.Suspense=dl,Zu.isAsyncMode=function(e){return ml(e)||_l(e)===cl},Zu.isConcurrentMode=ml,Zu.isContextConsumer=function(e){return _l(e)===al},Zu.isContextProvider=function(e){return _l(e)===ol},Zu.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===tl},Zu.isForwardRef=function(e){return _l(e)===ll},Zu.isFragment=function(e){return _l(e)===rl},Zu.isLazy=function(e){return _l(e)===hl},Zu.isMemo=function(e){return _l(e)===fl},Zu.isPortal=function(e){return _l(e)===nl},Zu.isProfiler=function(e){return _l(e)===sl},Zu.isStrictMode=function(e){return _l(e)===il},Zu.isSuspense=function(e){return _l(e)===dl},Zu.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===rl||e===ul||e===sl||e===il||e===dl||e===pl||"object"==typeof e&&null!==e&&(e.$$typeof===hl||e.$$typeof===fl||e.$$typeof===ol||e.$$typeof===al||e.$$typeof===ll||e.$$typeof===vl||e.$$typeof===Sl||e.$$typeof===El||e.$$typeof===gl)},Zu.typeOf=_l,Qu.exports=Zu;var Il=Qu.exports,yl={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Cl={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Tl={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},bl={};function Al(e){return Il.isMemo(e)?Tl:bl[e.$$typeof]||yl}bl[Il.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},bl[Il.Memo]=Tl;var Ol=Object.defineProperty,Nl=Object.getOwnPropertyNames,wl=Object.getOwnPropertySymbols,Rl=Object.getOwnPropertyDescriptor,Ll=Object.getPrototypeOf,Dl=Object.prototype;var Pl=function e(t,n,r){if("string"!=typeof n){if(Dl){var i=Ll(n);i&&i!==Dl&&e(t,i,r)}var s=Nl(n);wl&&(s=s.concat(wl(n)));for(var o=Al(t),a=Al(n),c=0;c<s.length;++c){var u=s[c];if(!(Cl[u]||r&&r[u]||a&&a[u]||o&&o[u])){var l=Rl(n,u);try{Ol(t,u,l)}catch(e){}}}}return t};function Vl(){return(Vl=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var Ul=function(e,t){for(var n=[e[0]],r=0,i=t.length;r<i;r+=1)n.push(t[r],e[r+1]);return n},kl=function(e){return null!==e&&"object"==typeof e&&"[object Object]"===(e.toString?e.toString():Object.prototype.toString.call(e))&&!vu.exports.typeOf(e)},Ml=Object.freeze([]),Fl=Object.freeze({});function xl(e){return"function"==typeof e}function Gl(e){return e.displayName||e.name||"Component"}function Bl(e){return e&&"string"==typeof e.styledComponentId}var Hl=typeof process==="object"&&typeof process.env==="object"&&void 0!==process.env&&(process.env.REACT_APP_SC_ATTR||process.env.SC_ATTR)||"data-styled",jl="undefined"!=typeof window&&"HTMLElement"in window,$l=Boolean("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:typeof process==="object"&&typeof process.env==="object"&&void 0!==process.env&&(void 0!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==process.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==process.env.REACT_APP_SC_DISABLE_SPEEDY&&process.env.REACT_APP_SC_DISABLE_SPEEDY:void 0!==process.env.SC_DISABLE_SPEEDY&&""!==process.env.SC_DISABLE_SPEEDY&&("false"!==process.env.SC_DISABLE_SPEEDY&&process.env.SC_DISABLE_SPEEDY)));function Yl(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw new Error("An error occurred. See https://git.io/JUIaE#"+e+" for more information."+(n.length>0?" Args: "+n.join(", "):""))}var Wl=function(){function e(e){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=e}var t=e.prototype;return t.indexOfGroup=function(e){for(var t=0,n=0;n<e;n++)t+=this.groupSizes[n];return t},t.insertRules=function(e,t){if(e>=this.groupSizes.length){for(var n=this.groupSizes,r=n.length,i=r;e>=i;)(i<<=1)<0&&Yl(16,""+e);this.groupSizes=new Uint32Array(i),this.groupSizes.set(n),this.length=i;for(var s=r;s<i;s++)this.groupSizes[s]=0}for(var o=this.indexOfGroup(e+1),a=0,c=t.length;a<c;a++)this.tag.insertRule(o,t[a])&&(this.groupSizes[e]++,o++)},t.clearGroup=function(e){if(e<this.length){var t=this.groupSizes[e],n=this.indexOfGroup(e),r=n+t;this.groupSizes[e]=0;for(var i=n;i<r;i++)this.tag.deleteRule(n)}},t.getGroup=function(e){var t="";if(e>=this.length||0===this.groupSizes[e])return t;for(var n=this.groupSizes[e],r=this.indexOfGroup(e),i=r+n,s=r;s<i;s++)t+=this.tag.getRule(s)+"/*!sc*/\n";return t},e}(),zl=new Map,Kl=new Map,Jl=1,ql=function(e){if(zl.has(e))return zl.get(e);for(;Kl.has(Jl);)Jl++;var t=Jl++;return zl.set(e,t),Kl.set(t,e),t},Xl=function(e){return Kl.get(e)},Ql=function(e,t){t>=Jl&&(Jl=t+1),zl.set(e,t),Kl.set(t,e)},Zl="style["+Hl+'][data-styled-version="5.3.11"]',ed=new RegExp("^"+Hl+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),td=function(e,t,n){for(var r,i=n.split(","),s=0,o=i.length;s<o;s++)(r=i[s])&&e.registerName(t,r)},nd=function(e,t){for(var n=(t.textContent||"").split("/*!sc*/\n"),r=[],i=0,s=n.length;i<s;i++){var o=n[i].trim();if(o){var a=o.match(ed);if(a){var c=0|parseInt(a[1],10),u=a[2];0!==c&&(Ql(u,c),td(e,u,a[3]),e.getTag().insertRules(c,r)),r.length=0}else r.push(o)}}},rd=function(e){var t=document.head,n=e||t,r=document.createElement("style"),i=function(e){for(var t=e.childNodes,n=t.length;n>=0;n--){var r=t[n];if(r&&1===r.nodeType&&r.hasAttribute(Hl))return r}}(n),s=void 0!==i?i.nextSibling:null;r.setAttribute(Hl,"active"),r.setAttribute("data-styled-version","5.3.11");var o="undefined"!=typeof __webpack_nonce__?__webpack_nonce__:null;return o&&r.setAttribute("nonce",o),n.insertBefore(r,s),r},id=function(){function e(e){var t=this.element=rd(e);t.appendChild(document.createTextNode("")),this.sheet=function(e){if(e.sheet)return e.sheet;for(var t=document.styleSheets,n=0,r=t.length;n<r;n++){var i=t[n];if(i.ownerNode===e)return i}Yl(17)}(t),this.length=0}var t=e.prototype;return t.insertRule=function(e,t){try{return this.sheet.insertRule(t,e),this.length++,!0}catch(e){return!1}},t.deleteRule=function(e){this.sheet.deleteRule(e),this.length--},t.getRule=function(e){var t=this.sheet.cssRules[e];return void 0!==t&&"string"==typeof t.cssText?t.cssText:""},e}(),sd=function(){function e(e){var t=this.element=rd(e);this.nodes=t.childNodes,this.length=0}var t=e.prototype;return t.insertRule=function(e,t){if(e<=this.length&&e>=0){var n=document.createTextNode(t),r=this.nodes[e];return this.element.insertBefore(n,r||null),this.length++,!0}return!1},t.deleteRule=function(e){this.element.removeChild(this.nodes[e]),this.length--},t.getRule=function(e){return e<this.length?this.nodes[e].textContent:""},e}(),od=function(){function e(e){this.rules=[],this.length=0}var t=e.prototype;return t.insertRule=function(e,t){return e<=this.length&&(this.rules.splice(e,0,t),this.length++,!0)},t.deleteRule=function(e){this.rules.splice(e,1),this.length--},t.getRule=function(e){return e<this.length?this.rules[e]:""},e}(),ad=jl,cd={isServer:!jl,useCSSOMInjection:!$l},ud=function(){function e(e,t,n){void 0===e&&(e=Fl),void 0===t&&(t={}),this.options=Vl({},cd,{},e),this.gs=t,this.names=new Map(n),this.server=!!e.isServer,!this.server&&jl&&ad&&(ad=!1,function(e){for(var t=document.querySelectorAll(Zl),n=0,r=t.length;n<r;n++){var i=t[n];i&&"active"!==i.getAttribute(Hl)&&(nd(e,i),i.parentNode&&i.parentNode.removeChild(i))}}(this))}e.registerId=function(e){return ql(e)};var t=e.prototype;return t.reconstructWithOptions=function(t,n){return void 0===n&&(n=!0),new e(Vl({},this.options,{},t),this.gs,n&&this.names||void 0)},t.allocateGSInstance=function(e){return this.gs[e]=(this.gs[e]||0)+1},t.getTag=function(){return this.tag||(this.tag=(n=(t=this.options).isServer,r=t.useCSSOMInjection,i=t.target,e=n?new od(i):r?new id(i):new sd(i),new Wl(e)));var e,t,n,r,i},t.hasNameForId=function(e,t){return this.names.has(e)&&this.names.get(e).has(t)},t.registerName=function(e,t){if(ql(e),this.names.has(e))this.names.get(e).add(t);else{var n=new Set;n.add(t),this.names.set(e,n)}},t.insertRules=function(e,t,n){this.registerName(e,t),this.getTag().insertRules(ql(e),n)},t.clearNames=function(e){this.names.has(e)&&this.names.get(e).clear()},t.clearRules=function(e){this.getTag().clearGroup(ql(e)),this.clearNames(e)},t.clearTag=function(){this.tag=void 0},t.toString=function(){return function(e){for(var t=e.getTag(),n=t.length,r="",i=0;i<n;i++){var s=Xl(i);if(void 0!==s){var o=e.names.get(s),a=t.getGroup(i);if(o&&a&&o.size){var c=Hl+".g"+i+'[id="'+s+'"]',u="";void 0!==o&&o.forEach((function(e){e.length>0&&(u+=e+",")})),r+=""+a+c+'{content:"'+u+'"}/*!sc*/\n'}}}return r}(this)},e}(),ld=/(a)(d)/gi,dd=function(e){return String.fromCharCode(e+(e>25?39:97))};function pd(e){var t,n="";for(t=Math.abs(e);t>52;t=t/52|0)n=dd(t%52)+n;return(dd(t%52)+n).replace(ld,"$1-$2")}var fd=function(e,t){for(var n=t.length;n;)e=33*e^t.charCodeAt(--n);return e},hd=function(e){return fd(5381,e)};var gd=hd("5.3.11"),vd=function(){function e(e,t,n){this.rules=e,this.staticRulesId="",this.isStatic=(void 0===n||n.isStatic)&&function(e){for(var t=0;t<e.length;t+=1){var n=e[t];if(xl(n)&&!Bl(n))return!1}return!0}(e),this.componentId=t,this.baseHash=fd(gd,t),this.baseStyle=n,ud.registerId(t)}return e.prototype.generateAndInjectStyles=function(e,t,n){var r=this.componentId,i=[];if(this.baseStyle&&i.push(this.baseStyle.generateAndInjectStyles(e,t,n)),this.isStatic&&!n.hash)if(this.staticRulesId&&t.hasNameForId(r,this.staticRulesId))i.push(this.staticRulesId);else{var s=Pd(this.rules,e,t,n).join(""),o=pd(fd(this.baseHash,s)>>>0);if(!t.hasNameForId(r,o)){var a=n(s,"."+o,void 0,r);t.insertRules(r,o,a)}i.push(o),this.staticRulesId=o}else{for(var c=this.rules.length,u=fd(this.baseHash,n.hash),l="",d=0;d<c;d++){var p=this.rules[d];if("string"==typeof p)l+=p;else if(p){var f=Pd(p,e,t,n),h=Array.isArray(f)?f.join(""):f;u=fd(u,h+d),l+=h}}if(l){var g=pd(u>>>0);if(!t.hasNameForId(r,g)){var v=n(l,"."+g,void 0,r);t.insertRules(r,g,v)}i.push(g)}}return i.join(" ")},e}(),Sd=/^\s*\/\/.*$/gm,Ed=[":","[",".","#"];function _d(e){var t,n,r,i,s=void 0===e?Fl:e,o=s.options,a=void 0===o?Fl:o,c=s.plugins,u=void 0===c?Ml:c,l=new zu(a),d=[],p=function(e){function t(t){if(t)try{e(t+"}")}catch(e){}}return function(n,r,i,s,o,a,c,u,l,d){switch(n){case 1:if(0===l&&64===r.charCodeAt(0))return e(r+";"),"";break;case 2:if(0===u)return r+"/*|*/";break;case 3:switch(u){case 102:case 112:return e(i[0]+r),"";default:return r+(0===d?"/*|*/":"")}case-2:r.split("/*|*/}").forEach(t)}}}((function(e){d.push(e)})),f=function(e,r,s){return 0===r&&-1!==Ed.indexOf(s[n.length])||s.match(i)?e:"."+t};function h(e,s,o,a){void 0===a&&(a="&");var c=e.replace(Sd,""),u=s&&o?o+" "+s+" { "+c+" }":c;return t=a,n=s,r=new RegExp("\\"+n+"\\b","g"),i=new RegExp("(\\"+n+"\\b){2,}"),l(o||!s?"":s,u)}return l.use([].concat(u,[function(e,t,i){2===e&&i.length&&i[0].lastIndexOf(n)>0&&(i[0]=i[0].replace(r,f))},p,function(e){if(-2===e){var t=d;return d=[],t}}])),h.hash=u.length?u.reduce((function(e,t){return t.name||Yl(15),fd(e,t.name)}),5381).toString():"",h}var md=gu.createContext();md.Consumer;var Id=gu.createContext(),yd=(Id.Consumer,new ud),Cd=_d();function Td(){return gc(md)||yd}function bd(e){var t=ac(e.stylisPlugins),n=t[0],r=t[1],i=Td(),s=fc((function(){var t=i;return e.sheet?t=e.sheet:e.target&&(t=t.reconstructWithOptions({target:e.target},!1)),e.disableCSSOMInjection&&(t=t.reconstructWithOptions({useCSSOMInjection:!1})),t}),[e.disableCSSOMInjection,e.sheet,e.target]),o=fc((function(){return _d({options:{prefix:!e.disableVendorPrefixes},plugins:n})}),[e.disableVendorPrefixes,n]);return uc((function(){(function(e,t,n,r){var i=n?n.call(r,e,t):void 0;if(void 0!==i)return!!i;if(e===t)return!0;if("object"!=typeof e||!e||"object"!=typeof t||!t)return!1;var s=Object.keys(e),o=Object.keys(t);if(s.length!==o.length)return!1;for(var a=Object.prototype.hasOwnProperty.bind(t),c=0;c<s.length;c++){var u=s[c];if(!a(u))return!1;var l=e[u],d=t[u];if(!1===(i=n?n.call(r,l,d,u):void 0)||void 0===i&&l!==d)return!1}return!0})(n,e.stylisPlugins)||r(e.stylisPlugins)}),[e.stylisPlugins]),gu.createElement(md.Provider,{value:s},gu.createElement(Id.Provider,{value:o},e.children))}var Ad=function(){function e(e,t){var n=this;this.inject=function(e,t){void 0===t&&(t=Cd);var r=n.name+t.hash;e.hasNameForId(n.id,r)||e.insertRules(n.id,r,t(n.rules,r,"@keyframes"))},this.toString=function(){return Yl(12,String(n.name))},this.name=e,this.id="sc-keyframes-"+e,this.rules=t}return e.prototype.getName=function(e){return void 0===e&&(e=Cd),this.name+e.hash},e}(),Od=/([A-Z])/,Nd=/([A-Z])/g,wd=/^ms-/,Rd=function(e){return"-"+e.toLowerCase()};function Ld(e){return Od.test(e)?e.replace(Nd,Rd).replace(wd,"-ms-"):e}var Dd=function(e){return null==e||!1===e||""===e};function Pd(e,t,n,r){if(Array.isArray(e)){for(var i,s=[],o=0,a=e.length;o<a;o+=1)""!==(i=Pd(e[o],t,n,r))&&(Array.isArray(i)?s.push.apply(s,i):s.push(i));return s}return Dd(e)?"":Bl(e)?"."+e.styledComponentId:xl(e)?"function"!=typeof(c=e)||c.prototype&&c.prototype.isReactComponent||!t?e:Pd(e(t),t,n,r):e instanceof Ad?n?(e.inject(n,r),e.getName(r)):e:kl(e)?function e(t,n){var r,i,s=[];for(var o in t)t.hasOwnProperty(o)&&!Dd(t[o])&&(Array.isArray(t[o])&&t[o].isCss||xl(t[o])?s.push(Ld(o)+":",t[o],";"):kl(t[o])?s.push.apply(s,e(t[o],o)):s.push(Ld(o)+": "+(r=o,(null==(i=t[o])||"boolean"==typeof i||""===i?"":"number"!=typeof i||0===i||r in Ku||r.startsWith("--")?String(i).trim():i+"px")+";")));return n?[n+" {"].concat(s,["}"]):s}(e):e.toString();var c}var Vd=function(e){return Array.isArray(e)&&(e.isCss=!0),e};function Ud(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return xl(e)||kl(e)?Vd(Pd(Ul(Ml,[e].concat(n)))):0===n.length&&1===e.length&&"string"==typeof e[0]?e:Vd(Pd(Ul(e,n)))}var kd=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,Md=/(^-|-$)/g;function Fd(e){return e.replace(kd,"-").replace(Md,"")}var xd=function(e){return pd(hd(e)>>>0)};function Gd(e){return"string"==typeof e&&!0}var Bd=function(e){return"function"==typeof e||"object"==typeof e&&null!==e&&!Array.isArray(e)},Hd=function(e){return"__proto__"!==e&&"constructor"!==e&&"prototype"!==e};function jd(e,t,n){var r=e[n];Bd(t)&&Bd(r)?$d(r,t):e[n]=t}function $d(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var i=0,s=n;i<s.length;i++){var o=s[i];if(Bd(o))for(var a in o)Hd(a)&&jd(e,o[a],a)}return e}var Yd=gu.createContext();function Wd(e){var t=gc(Yd),n=fc((function(){return function(e,t){return e?xl(e)?e(t):Array.isArray(e)||"object"!=typeof e?Yl(8):t?Vl({},t,{},e):e:Yl(14)}(e.theme,t)}),[e.theme,t]);return e.children?gu.createElement(Yd.Provider,{value:n},e.children):null}Yd.Consumer;var zd={};function Kd(e,t,n){var r=Bl(e),i=!Gd(e),s=t.attrs,o=void 0===s?Ml:s,a=t.componentId,c=void 0===a?function(e,t){var n="string"!=typeof e?"sc":Fd(e);zd[n]=(zd[n]||0)+1;var r=n+"-"+xd("5.3.11"+n+zd[n]);return t?t+"-"+r:r}(t.displayName,t.parentComponentId):a,u=t.displayName,l=void 0===u?function(e){return Gd(e)?"styled."+e:"Styled("+Gl(e)+")"}(e):u,d=t.displayName&&t.componentId?Fd(t.displayName)+"-"+t.componentId:t.componentId||c,p=r&&e.attrs?Array.prototype.concat(e.attrs,o).filter(Boolean):o,f=t.shouldForwardProp;r&&e.shouldForwardProp&&(f=t.shouldForwardProp?function(n,r,i){return e.shouldForwardProp(n,r,i)&&t.shouldForwardProp(n,r,i)}:e.shouldForwardProp);var h,g=new vd(n,d,r?e.componentStyle:void 0),v=g.isStatic&&0===o.length,S=function(e,t){return function(e,t,n,r){var i=e.attrs,s=e.componentStyle,o=e.defaultProps,a=e.foldedComponentIds,c=e.shouldForwardProp,u=e.styledComponentId,l=e.target,d=function(e,t,n){void 0===e&&(e=Fl);var r=Vl({},t,{theme:e}),i={};return n.forEach((function(e){var t,n,s,o=e;for(t in xl(o)&&(o=o(r)),o)r[t]=i[t]="className"===t?(n=i[t],s=o[t],n&&s?n+" "+s:n||s):o[t]})),[r,i]}(function(e,t,n){return void 0===n&&(n=Fl),e.theme!==n.theme&&e.theme||t||n.theme}(t,gc(Yd),o)||Fl,t,i),p=d[0],f=d[1],h=function(e,t,n){var r=Td(),i=gc(Id)||Cd;return t?e.generateAndInjectStyles(Fl,r,i):e.generateAndInjectStyles(n,r,i)}(s,r,p),g=n,v=f.$as||t.$as||f.as||t.as||l,S=Gd(v),E=f!==t?Vl({},t,{},f):t,_={};for(var m in E)"$"!==m[0]&&"as"!==m&&("forwardedAs"===m?_.as=E[m]:(c?c(m,Xu,v):!S||Xu(m))&&(_[m]=E[m]));return t.style&&f.style!==t.style&&(_.style=Vl({},t.style,{},f.style)),_.className=Array.prototype.concat(a,u,h!==u?h:null,t.className,f.className).filter(Boolean).join(" "),_.ref=g,ya(v,_)}(h,e,t,v)};return S.displayName=l,(h=gu.forwardRef(S)).attrs=p,h.componentStyle=g,h.displayName=l,h.shouldForwardProp=f,h.foldedComponentIds=r?Array.prototype.concat(e.foldedComponentIds,e.styledComponentId):Ml,h.styledComponentId=d,h.target=r?e.target:e,h.withComponent=function(e){var r=t.componentId,i=function(e,t){if(null==e)return{};var n,r,i={},s=Object.keys(e);for(r=0;r<s.length;r++)n=s[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(t,["componentId"]),s=r&&r+"-"+(Gd(e)?e:Fd(Gl(e)));return Kd(e,Vl({},i,{attrs:p,componentId:s}),n)},Object.defineProperty(h,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(t){this._foldedDefaultProps=r?$d({},e.defaultProps,t):t}}),Object.defineProperty(h,"toString",{value:function(){return"."+h.styledComponentId}}),i&&Pl(h,e,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),h}var Jd=function(e){return function e(t,n,r){if(void 0===r&&(r=Fl),!vu.exports.isValidElementType(n))return Yl(1,String(n));var i=function(){return t(n,r,Ud.apply(void 0,arguments))};return i.withConfig=function(i){return e(t,n,Vl({},r,{},i))},i.attrs=function(i){return e(t,n,Vl({},r,{attrs:Array.prototype.concat(r.attrs,i).filter(Boolean)}))},i}(Kd,e)};function qd(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var i=Ud.apply(void 0,[e].concat(n)).join(""),s=xd(i);return new Ad(s,i)}["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach((function(e){Jd[e]=Jd(e)}));var Xd=function(){return gc(Yd)},Qd=Jd;const Zd=["ar","ur","he","fa"],ep="usercentrics-root";let tp=function(e){return e.AMP_ENABLED="ampEnabled",e.AVOID_PREFETCH_SERVICES="avoidPrefetchServices",e.BLOCK_DATA_LAYER_PUSH="blockDataLayerPush",e.CONTROLLER_ID="controllerId",e.CONTROLLER_IDS="controllerIds",e.CLIENT_CONFIG="clientConfig",e.DISABLE_TRACKING="disableTracking",e.EU_MODE="euMode",e.ID="id",e.LANGUAGE="language",e.DISABLE_SERVER_CONSENTS="disableServerConsents",e.RULESET_ID="rulesetId",e.SANDBOX_ENV="sandbox",e.SETTINGS_CACHE="settingsCache",e.SETTINGS_ID="settingsId",e.STORE_SERVICE_ID_TO_NAME_MAPPING="storeServiceIdToNameMapping",e.SUPPRESS_CMP_DISPLAY="suppressCmpDisplay",e.TCF_ENABLED="tcfEnabled",e.GPP_ENABLED="gppEnabled",e.VERSION="version",e.DEPRECATED_V1_CONSENT_SAVING_ENABLED="enableV1ConsentSaving",e.DISABLE_UET="disableUet",e.DIALOG_MODE="dialogMode",e}({});const np="UC_UI",rp="uc-tab-list",ip="uc-banner-content",sp="uc-center-container",op="uc-side-container",ap="uc-vertical-scroller",cp="uc-heading-title",up="uc-show-more",lp="language-tooltip-id";let dp=function(e){return e.API="UC_UI_API",e.FIRST_LAYER="FIRST_LAYER",e.PRIVACY_BUTTON="PRIVACY_BUTTON",e.SECOND_LAYER="SECOND_LAYER",e}({}),pp=function(e){return e.CMP_SHOWN="CMP_SHOWN",e.ACCEPT_ALL="ACCEPT_ALL",e.DENY_ALL="DENY_ALL",e.SAVE="SAVE",e.COOKIE_POLICY_LINK="COOKIE_POLICY_LINK",e.IMPRINT_LINK="IMPRINT_LINK",e.MORE_INFORMATION_LINK="MORE_INFORMATION_LINK",e.PRIVACY_POLICY_LINK="PRIVACY_POLICY_LINK",e.INTEGRATIONS_LINK="INTEGRATIONS_LINK",e.CCPA_TOGGLES_ON="CCPA_TOGGLES_ON",e.CCPA_TOGGLES_OFF="CCPA_TOGGLES_OFF",e}({}),fp=function(e){return e.FIRST_LAYER="FIRST_LAYER",e.NONE="NONE",e.PRIVACY_BUTTON="PRIVACY_BUTTON",e.SECOND_LAYER="SECOND_LAYER",e}({}),hp=function(e){return e.ANALYTICS="UC_UI_CMP_EVENT",e.INITIALIZED="UC_UI_INITIALIZED",e.VIEW_CHANGED="UC_UI_VIEW_CHANGED",e}({}),gp=function(e){return e.SERVICE_MISSING_ID="Usercentrics: service ID is missing",e.DUPLICATED_SCRIPT="Usercentrics: duplicated script tag",e.TCF_NOT_ENABLED="Usercentrics: TCF is not enabled",e.CLIENT_CONFIG_NOT_AVAILABLE="AMP clientConfig is not available",e.INACCESSIBLE_LOCAL_STORAGE="Usercentrics: CMP is not fully functional due to inaccessible local storage",e.USE_LOADER="Usercentrics: If you're not using Content Security Policy, please use loader.js instead of bundle.js according to https://docs.usercentrics.com/#/browser-ui?id=implementation",e}({}),vp=function(e){return e.MARGIN_LEFT="margin-left:",e.MARGIN_RIGHT="margin-right:",e.PADDING_LEFT="padding-left:",e.PADDING_RIGHT="padding-right:",e.FLOAT_LEFT="float: left",e.FLOAT_RIGHT="float: right",e.FLEX_ALIGNMENT_LEFT="justify-content: flex-start",e.FLEX_ALIGNMENT_RIGHT="justify-content: flex-end",e.TEXT_ALIGNMENT_LEFT="text-align: left",e.TEXT_ALIGNMENT_RIGHT="text-align: right",e.BORDER_LEFT="border-left:",e.BORDER_RIGHT="border-right:",e.BORDER_RADIUS_TOP_LEFT="border-top-left-radius:",e.BORDER_RADIUS_TOP_RIGHT="border-top-right-radius:",e.BORDER_RADIUS_BOTTOM_LEFT="border-bottom-left-radius:",e.BORDER_RADIUS_BOTTOM_RIGHT="border-bottom-right-radius:",e.OBJECT_POSITION_RIGHT="object-position: right",e.OBJECT_POSITION_LEFT="object-position: left",e.OBJECT_POSITION_CENTER="object-position: center",e.POSITION_LEFT="left:",e.POSITION_RIGHT="right:",e}({}),Sp=function(e){return e.SOLID="solid",e.NONE="none",e.DOTTED="dotted",e.DASHED="dashed",e.DOUBLE="double",e}({}),Ep=function(e){return e.PRIMARY="primary",e.SECONDARY="secondary",e.TERTIARY="tertiary",e.QUATERNARY="quaternary",e.NEUTRAL="neutral",e}({}),_p=function(e){return e.PRIMARY="primary",e.SECONDARY="secondary",e.TERTIARY="tertiary",e.QUATERNARY="quaternary",e}({}),mp=function(e){return e.LARGE="large",e.MEDIUM="medium",e.SMALL="small",e.XSMALL="xsmall",e}({}),Ip=function(e){return e.MAX_WIDTH="max-width:",e.MIN_WIDTH="min-width:",e.MAX_HEIGHT="max-height:",e.MIN_HEIGHT="min-height:",e}({}),yp=function(e){return e.CENTER="CENTER",e.BOTTOM="BOTTOM",e.SIDE="SIDE",e.TOP="TOP",e}({}),Cp=function(e){return e.CATEGORIES_PURPOSES="categories_purposes",e.SERVICES_VENDORS="services_vendors",e}({});const Tp=()=>{try{const{clientConfig:e}=JSON.parse(window.name);return e}catch(e){console.error(gp.CLIENT_CONFIG_NOT_AVAILABLE)}return null},bp=e=>{const t=e.replace(/\s+/g,""),n=e.match(/[^\r\n]+/g);if(!((null==n?void 0:n.reduce(((e,t)=>e+t.replace(/\s+/g,"").length),0))===t.length))return e;const r=[{bulletPoints:[],title:""}];let i=0;if(n){n.forEach(((e,t)=>{e.startsWith("*")?r[i].bulletPoints.push(" "===e.substr(1,1)?e.substr(2):e.substr(1)):(t>0&&(i+=1),r[i]={bulletPoints:[],title:e})}));if(r.reduce(((e,t)=>e+t.title.replace(/\s|\u002a+/g,"").length+t.bulletPoints.reduce(((e,t)=>e+t.replace(/\s|\u002a+/g,"").length),0)),0)===t.replace(/\u002a+/g,"").length)return r}return e},Ap=e=>/\S+@\S+\.\S+/.test(e),Op=(e,t)=>{switch(e){case F.BANNER:return{layerPlacement:yp.BOTTOM,testId:t.firstLayerBanner};case F.WALL:return{layerPlacement:yp.CENTER,testId:t.firstLayerWall};case x.CENTER:return{layerPlacement:yp.CENTER,testId:t.secondLayer};case x.SIDE:return{layerPlacement:yp.SIDE,testId:t.secondLayer};default:return{layerPlacement:null,testId:""}}},Np=e=>null!=e&&"object"==typeof e,wp=(e,t)=>{const n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(const r of n){const n=e[r],i=t[r],s=Np(n)&&Np(i);if(s&&!wp(n,i)||!s&&n!==i)return!1}return!0},Rp=(e,t,n,r)=>{const i={source:e===fp.NONE||e===fp.PRIVACY_BUTTON&&t!==pp.CMP_SHOWN?dp.API:e,type:t};n&&n.length&&(i.abTestVariant=n);const s=new window.CustomEvent(hp.ANALYTICS,{detail:i});window.dispatchEvent(s);let o=(e=>{switch(e){case pp.CMP_SHOWN:return So.CMP_SHOWN;case pp.ACCEPT_ALL:return So.ACCEPT_ALL;case pp.DENY_ALL:return So.DENY_ALL;case pp.SAVE:return So.SAVE;case pp.COOKIE_POLICY_LINK:return So.COOKIE_POLICY_LINK;case pp.IMPRINT_LINK:return So.IMPRINT_LINK;case pp.MORE_INFORMATION_LINK:return So.MORE_INFORMATION_LINK;case pp.PRIVACY_POLICY_LINK:return So.PRIVACY_POLICY_LINK;case pp.INTEGRATIONS_LINK:return So.SAY_MINE_LINK;case pp.CCPA_TOGGLES_ON:return So.CCPA_TOGGLES_ON;case pp.CCPA_TOGGLES_OFF:return So.CCPA_TOGGLES_OFF;default:return So.UNDEFINED}})(t),a=0;o>1&&o<5&&(e===fp.FIRST_LAYER?a=3:e===fp.SECOND_LAYER&&(a=6)),o+=a;r({abTestVariant:n,eventType:o})},Lp=e=>{for(;e.lastElementChild;)e.removeChild(e.lastElementChild)},Dp="BlinkMacSystemFont, -apple-system, Segoe UI, Roboto, Oxygen-Sans, Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, Helvetica, Arial, sans-serif",Pp="hsl(0.0, 0%, 19%)",Vp="#ffffff",Up="#0045A5",kp=400,Mp=700,Fp=Vp,xp=.7,Gp="1px solid #dedede",Bp=Vp,Hp={lg:992,md:768,sm:600,xl:1200,xs:400,xxl:1472,xxxl:1e4};function jp(){return jp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},jp.apply(null,arguments)}function $p(e,t){return $p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},$p(e,t)}function Yp(e){return Yp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Yp(e)}function Wp(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Wp=function(){return!!e})()}function zp(e){var t="function"==typeof Map?new Map:void 0;return zp=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return function(e,t,n){if(Wp())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,t);var i=new(e.bind.apply(e,r));return n&&$p(i,n.prototype),i}(e,arguments,Yp(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),$p(n,e)},zp(e)}var Kp=function(e){function t(t){return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e.call(this,"An error occurred. See https://github.com/styled-components/polished/blob/main/src/internalHelpers/errors.md#"+t+" for more information.")||this)}return function(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,$p(e,t)}(t,e),t}(zp(Error));function Jp(e){return Math.round(255*e)}function qp(e,t,n){return Jp(e)+","+Jp(t)+","+Jp(n)}function Xp(e,t,n,r){if(void 0===r&&(r=qp),0===t)return r(n,n,n);var i=(e%360+360)%360/60,s=(1-Math.abs(2*n-1))*t,o=s*(1-Math.abs(i%2-1)),a=0,c=0,u=0;i>=0&&i<1?(a=s,c=o):i>=1&&i<2?(a=o,c=s):i>=2&&i<3?(c=s,u=o):i>=3&&i<4?(c=o,u=s):i>=4&&i<5?(a=o,u=s):i>=5&&i<6&&(a=s,u=o);var l=n-s/2;return r(a+l,c+l,u+l)}var Qp={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"};var Zp=/^#[a-fA-F0-9]{6}$/,ef=/^#[a-fA-F0-9]{8}$/,tf=/^#[a-fA-F0-9]{3}$/,nf=/^#[a-fA-F0-9]{4}$/,rf=/^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i,sf=/^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i,of=/^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i,af=/^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;function cf(e){if("string"!=typeof e)throw new Kp(3);var t=function(e){if("string"!=typeof e)return e;var t=e.toLowerCase();return Qp[t]?"#"+Qp[t]:e}(e);if(t.match(Zp))return{red:parseInt(""+t[1]+t[2],16),green:parseInt(""+t[3]+t[4],16),blue:parseInt(""+t[5]+t[6],16)};if(t.match(ef)){var n=parseFloat((parseInt(""+t[7]+t[8],16)/255).toFixed(2));return{red:parseInt(""+t[1]+t[2],16),green:parseInt(""+t[3]+t[4],16),blue:parseInt(""+t[5]+t[6],16),alpha:n}}if(t.match(tf))return{red:parseInt(""+t[1]+t[1],16),green:parseInt(""+t[2]+t[2],16),blue:parseInt(""+t[3]+t[3],16)};if(t.match(nf)){var r=parseFloat((parseInt(""+t[4]+t[4],16)/255).toFixed(2));return{red:parseInt(""+t[1]+t[1],16),green:parseInt(""+t[2]+t[2],16),blue:parseInt(""+t[3]+t[3],16),alpha:r}}var i=rf.exec(t);if(i)return{red:parseInt(""+i[1],10),green:parseInt(""+i[2],10),blue:parseInt(""+i[3],10)};var s=sf.exec(t.substring(0,50));if(s)return{red:parseInt(""+s[1],10),green:parseInt(""+s[2],10),blue:parseInt(""+s[3],10),alpha:parseFloat(""+s[4])>1?parseFloat(""+s[4])/100:parseFloat(""+s[4])};var o=of.exec(t);if(o){var a="rgb("+Xp(parseInt(""+o[1],10),parseInt(""+o[2],10)/100,parseInt(""+o[3],10)/100)+")",c=rf.exec(a);if(!c)throw new Kp(4,t,a);return{red:parseInt(""+c[1],10),green:parseInt(""+c[2],10),blue:parseInt(""+c[3],10)}}var u=af.exec(t.substring(0,50));if(u){var l="rgb("+Xp(parseInt(""+u[1],10),parseInt(""+u[2],10)/100,parseInt(""+u[3],10)/100)+")",d=rf.exec(l);if(!d)throw new Kp(4,t,l);return{red:parseInt(""+d[1],10),green:parseInt(""+d[2],10),blue:parseInt(""+d[3],10),alpha:parseFloat(""+u[4])>1?parseFloat(""+u[4])/100:parseFloat(""+u[4])}}throw new Kp(5)}function uf(e){return function(e){var t,n=e.red/255,r=e.green/255,i=e.blue/255,s=Math.max(n,r,i),o=Math.min(n,r,i),a=(s+o)/2;if(s===o)return void 0!==e.alpha?{hue:0,saturation:0,lightness:a,alpha:e.alpha}:{hue:0,saturation:0,lightness:a};var c=s-o,u=a>.5?c/(2-s-o):c/(s+o);switch(s){case n:t=(r-i)/c+(r<i?6:0);break;case r:t=(i-n)/c+2;break;default:t=(n-r)/c+4}return t*=60,void 0!==e.alpha?{hue:t,saturation:u,lightness:a,alpha:e.alpha}:{hue:t,saturation:u,lightness:a}}(cf(e))}var lf=function(e){return 7===e.length&&e[1]===e[2]&&e[3]===e[4]&&e[5]===e[6]?"#"+e[1]+e[3]+e[5]:e};function df(e){var t=e.toString(16);return 1===t.length?"0"+t:t}function pf(e){return df(Math.round(255*e))}function ff(e,t,n){return lf("#"+pf(e)+pf(t)+pf(n))}function hf(e,t,n){return Xp(e,t,n,ff)}function gf(e,t,n){if("number"==typeof e&&"number"==typeof t&&"number"==typeof n)return lf("#"+df(e)+df(t)+df(n));if("object"==typeof e&&void 0===t&&void 0===n)return lf("#"+df(e.red)+df(e.green)+df(e.blue));throw new Kp(6)}function vf(e,t,n,r){if("string"==typeof e&&"number"==typeof t){var i=cf(e);return"rgba("+i.red+","+i.green+","+i.blue+","+t+")"}if("number"==typeof e&&"number"==typeof t&&"number"==typeof n&&"number"==typeof r)return r>=1?gf(e,t,n):"rgba("+e+","+t+","+n+","+r+")";if("object"==typeof e&&void 0===t&&void 0===n&&void 0===r)return e.alpha>=1?gf(e.red,e.green,e.blue):"rgba("+e.red+","+e.green+","+e.blue+","+e.alpha+")";throw new Kp(7)}function Sf(e){if("object"!=typeof e)throw new Kp(8);if(function(e){return"number"==typeof e.red&&"number"==typeof e.green&&"number"==typeof e.blue&&"number"==typeof e.alpha}(e))return vf(e);if(function(e){return"number"==typeof e.red&&"number"==typeof e.green&&"number"==typeof e.blue&&("number"!=typeof e.alpha||void 0===e.alpha)}(e))return gf(e);if(function(e){return"number"==typeof e.hue&&"number"==typeof e.saturation&&"number"==typeof e.lightness&&"number"==typeof e.alpha}(e))return function(e,t,n,r){if("number"==typeof e&&"number"==typeof t&&"number"==typeof n&&"number"==typeof r)return r>=1?hf(e,t,n):"rgba("+Xp(e,t,n)+","+r+")";if("object"==typeof e&&void 0===t&&void 0===n&&void 0===r)return e.alpha>=1?hf(e.hue,e.saturation,e.lightness):"rgba("+Xp(e.hue,e.saturation,e.lightness)+","+e.alpha+")";throw new Kp(2)}(e);if(function(e){return"number"==typeof e.hue&&"number"==typeof e.saturation&&"number"==typeof e.lightness&&("number"!=typeof e.alpha||void 0===e.alpha)}(e))return function(e,t,n){if("number"==typeof e&&"number"==typeof t&&"number"==typeof n)return hf(e,t,n);if("object"==typeof e&&void 0===t&&void 0===n)return hf(e.hue,e.saturation,e.lightness);throw new Kp(1)}(e);throw new Kp(8)}function Ef(e,t,n){return function(){var r=n.concat(Array.prototype.slice.call(arguments));return r.length>=t?e.apply(this,r):Ef(e,t,r)}}function _f(e){return Ef(e,e.length,[])}function mf(e,t,n){return Math.max(e,Math.min(t,n))}_f((function(e,t){if("transparent"===t)return t;var n=uf(t);return Sf(jp({},n,{hue:n.hue+parseFloat(e)}))}));var If=_f((function(e,t){if("transparent"===t)return t;var n=uf(t);return Sf(jp({},n,{lightness:mf(0,1,n.lightness-parseFloat(e))}))}));function yf(e){if("transparent"===e)return 0;var t=cf(e),n=Object.keys(t).map((function(e){var n=t[e]/255;return n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4)})),r=n[0],i=n[1],s=n[2];return parseFloat((.2126*r+.7152*i+.0722*s).toFixed(3))}_f((function(e,t){if("transparent"===t)return t;var n=uf(t);return Sf(jp({},n,{saturation:mf(0,1,n.saturation-parseFloat(e))}))}));var Cf=_f((function(e,t){if("transparent"===t)return t;var n=uf(t);return Sf(jp({},n,{lightness:mf(0,1,n.lightness+parseFloat(e))}))}));function Tf(e,t){var n=function(e,t){var n=yf(e),r=yf(t);return parseFloat((n>r?(n+.05)/(r+.05):(r+.05)/(n+.05)).toFixed(2))}(e,t);return{AA:n>=4.5,AALarge:n>=3,AAA:n>=7,AAALarge:n>=4.5}}var bf=_f((function(e,t,n){if("transparent"===t)return n;if("transparent"===n)return t;if(0===e)return n;var r=cf(t),i=jp({},r,{alpha:"number"==typeof r.alpha?r.alpha:1}),s=cf(n),o=jp({},s,{alpha:"number"==typeof s.alpha?s.alpha:1}),a=i.alpha-o.alpha,c=2*parseFloat(e)-1,u=((c*a==-1?c:c+a)/(1+c*a)+1)/2,l=1-u;return vf({red:Math.floor(i.red*u+o.red*l),green:Math.floor(i.green*u+o.green*l),blue:Math.floor(i.blue*u+o.blue*l),alpha:i.alpha*parseFloat(e)+o.alpha*(1-parseFloat(e))})})),Af=bf;_f((function(e,t){if("transparent"===t)return t;var n=cf(t);return vf(jp({},n,{alpha:mf(0,1,(100*("number"==typeof n.alpha?n.alpha:1)+100*parseFloat(e))/100)}))})),_f((function(e,t){if("transparent"===t)return t;var n=uf(t);return Sf(jp({},n,{saturation:mf(0,1,n.saturation+parseFloat(e))}))})),_f((function(e,t){return"transparent"===t?t:Sf(jp({},uf(t),{hue:parseFloat(e)}))})),_f((function(e,t){return"transparent"===t?t:Sf(jp({},uf(t),{lightness:parseFloat(e)}))})),_f((function(e,t){return"transparent"===t?t:Sf(jp({},uf(t),{saturation:parseFloat(e)}))})),_f((function(e,t){return"transparent"===t?t:Af(parseFloat(e),"rgb(0, 0, 0)",t)}));var Of=_f((function(e,t){return"transparent"===t?t:Af(parseFloat(e),"rgb(255, 255, 255)",t)}));_f((function(e,t){if("transparent"===t)return t;var n=cf(t);return vf(jp({},n,{alpha:mf(0,1,+(100*("number"==typeof n.alpha?n.alpha:1)-100*parseFloat(e)).toFixed(2)/100)}))}));const Nf={base:{xxs:"4px",xs:"8px",sm:"12px",md:"16px",lg:"20px",xl:"24px",xxl:"28px",xxxl:"32px",xxxxl:"36px"},mobile:{xxs:"2px",xs:"4px",sm:"6px",md:"8px",lg:"10px",xl:"12px",xxl:"14px",xxxl:"16px",xxxxl:"18px"}},wf=(e,t=Ip.MIN_WIDTH)=>`(${t} ${e}px)`,{sm:Rf,md:Lf}=Hp,Df={ie11:" @media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none)",mobileLandscape:`@media ${wf(Lf,Ip.MAX_HEIGHT)} and (orientation: landscape)`,mobileLandscapeXS:`@media ${wf(Rf,Ip.MAX_HEIGHT)} and (orientation: landscape)`,mobilePortrait:`@media ${wf(Lf,Ip.MAX_WIDTH)} and (orientation: portrait)`,mobilePortraitXS:`@media ${wf(Rf,Ip.MAX_WIDTH)}`};function Pf(e,t){return Tf(e,t.primary).AA?t.primary:t.quaternary}const Vf=e=>({dark:If(.1,e),default:e});function Uf(e){const t=Of(.2,e),n=Of(.84,e),r=Of(.98,e);return{[Ep.PRIMARY]:Vf(e),[Ep.SECONDARY]:Vf(t),[Ep.TERTIARY]:Vf(n),[Ep.QUATERNARY]:Vf(r),[Ep.NEUTRAL]:Vf("hsl(0.0, 0%, 96.0%)")}}function kf(e){return e&&e!==Dp?`${e},${Dp}`:Dp}function Mf(e,t=16){return`${e/t}rem`}function Ff(e=Dp,t=14,n=Pp,r=!1){return{color:{[_p.PRIMARY]:n,[_p.SECONDARY]:Of(.2,n),[_p.TERTIARY]:Of(.84,n),[_p.QUATERNARY]:Of(.98,n)},font:kf(e),rtlEnabled:r,scaleFactor:t/14,size:{[mp.LARGE]:"calc(var(--uc-typography-scale, 1) * 1.125em)",[mp.MEDIUM]:"calc(var(--uc-typography-scale, 1) * 1em)",[mp.SMALL]:"calc(var(--uc-typography-scale, 1) * 0.875em)",[mp.XSMALL]:"calc(var(--uc-typography-scale, 1) * 0.75em)",root:`calc(var(--uc-typography-scale, 1) * ${Mf(t)})`},weight:{bold:700,light:250,regular:400,semiBold:550}}}const xf=(e,t,n,r,i)=>{const s=(e=>"auto"===e?e:"number"==typeof e&&0!==e||"string"==typeof e&&!e.includes("px")?`${e}px`:"string"==typeof e?e:"0")(n),o=i||"";let a="";r&&(a=r!==Sp.NONE?`${s} ${r} ${o}`:`${Sp.NONE}`);const c=e.typography.rtlEnabled;switch(t){case vp.FLEX_ALIGNMENT_LEFT:return c?`${vp.FLEX_ALIGNMENT_RIGHT}`:`${vp.FLEX_ALIGNMENT_LEFT}`;case vp.FLEX_ALIGNMENT_RIGHT:return c?`${vp.FLEX_ALIGNMENT_LEFT}`:`${vp.FLEX_ALIGNMENT_RIGHT}`;case vp.TEXT_ALIGNMENT_LEFT:return c?`${vp.TEXT_ALIGNMENT_RIGHT}`:`${vp.TEXT_ALIGNMENT_LEFT}`;case vp.TEXT_ALIGNMENT_RIGHT:return c?`${vp.TEXT_ALIGNMENT_LEFT}`:`${vp.TEXT_ALIGNMENT_RIGHT}`;case vp.FLOAT_LEFT:return c?`${vp.FLOAT_RIGHT}`:`${vp.FLOAT_LEFT}`;case vp.FLOAT_RIGHT:return c?`${vp.FLOAT_LEFT}`:`${vp.FLOAT_RIGHT}`;case vp.PADDING_LEFT:return c?`${vp.PADDING_RIGHT} ${s}`:`${vp.PADDING_LEFT} ${s}`;case vp.PADDING_RIGHT:return c?`${vp.PADDING_LEFT} ${s}`:`${vp.PADDING_RIGHT} ${s}`;case vp.MARGIN_LEFT:return c?`${vp.MARGIN_RIGHT} ${s}`:`${vp.MARGIN_LEFT} ${s}`;case vp.MARGIN_RIGHT:return c?`${vp.MARGIN_LEFT} ${s}`:`${vp.MARGIN_RIGHT} ${s}`;case vp.BORDER_LEFT:return c?`${vp.BORDER_RIGHT} ${a};`:`${vp.BORDER_LEFT} ${a}`;case vp.BORDER_RIGHT:return c?`${vp.BORDER_LEFT} ${a};`:`${vp.BORDER_RIGHT} ${a}`;case vp.BORDER_RADIUS_BOTTOM_LEFT:return c?`${vp.BORDER_RADIUS_BOTTOM_RIGHT} ${s}`:`${vp.BORDER_RADIUS_BOTTOM_LEFT} ${s}`;case vp.BORDER_RADIUS_BOTTOM_RIGHT:return c?`${vp.BORDER_RADIUS_BOTTOM_LEFT} ${s}`:`${vp.BORDER_RADIUS_BOTTOM_RIGHT} ${s}`;case vp.BORDER_RADIUS_TOP_LEFT:return c?`${vp.BORDER_RADIUS_TOP_RIGHT} ${s}`:`${vp.BORDER_RADIUS_TOP_LEFT} ${s}`;case vp.BORDER_RADIUS_TOP_RIGHT:return c?`${vp.BORDER_RADIUS_TOP_LEFT} ${s}`:`${vp.BORDER_RADIUS_TOP_RIGHT} ${s}`;case vp.OBJECT_POSITION_LEFT:return c?`${vp.OBJECT_POSITION_RIGHT} ${s}`:`${vp.OBJECT_POSITION_LEFT} ${s}`;case vp.OBJECT_POSITION_RIGHT:return c?`${vp.OBJECT_POSITION_LEFT} ${s}`:`${vp.OBJECT_POSITION_RIGHT} ${s}`;case vp.POSITION_RIGHT:return c?`${vp.POSITION_LEFT} ${s}`:`${vp.POSITION_RIGHT} ${s}`;case vp.POSITION_LEFT:return c?`${vp.POSITION_RIGHT} ${s}`:`${vp.POSITION_LEFT} ${s}`;default:return""}},Gf=(e,t,n)=>Vf(n||Pf(e,t)),Bf=e=>`${e}px`,Hf=(e,t)=>t&&!St(t)?{color:Uf(t.primaryColor),typography:Ff(t.font.family,t.font.size,Pp,e)}:{color:Uf("hsl(214.9, 100%, 32.4%)"),typography:Ff(Dp,14,Pp)},jf=(e,t)=>{const{color:n,typography:r}=Hf(e,t),i=(({neutral:e,primary:t},{color:n},r)=>{var i,s,o,a,c,u,l,d;return{accept:{bg:Vf(r&&!St(r)&&!ht(r)&&r.buttons.accept.backgroundColor?null==r?void 0:r.buttons.accept.backgroundColor:t.default),text:Gf(t.default,n,!r||St(r)||ht(r)?null:r.buttons.accept.textColor)},alignment:!r||St(r)||ht(r)?j.HORIZONTAL:r.buttons.alignment,borderRadius:r&&!St(r)?r.buttons.borderRadius:"4px",ccpa:{bg:Vf(r&&!St(r)&&ht(r)&&null!==(i=r.buttons.ok)&&void 0!==i&&i.backgroundColor?r.buttons.ok.backgroundColor:e.default),text:Gf(e.default,n,r&&!St(r)&&ht(r)&&null!==(s=r.buttons.ok)&&void 0!==s&&s.textColor?r.buttons.ok.textColor:null)},deny:{bg:Vf(r&&!St(r)&&!ht(r)&&null!==(o=r.buttons.deny)&&void 0!==o&&o.backgroundColor?r.buttons.deny.backgroundColor:e.default),text:Gf(e.default,n,r&&!St(r)&&!ht(r)&&null!==(a=r.buttons.deny)&&void 0!==a&&a.textColor?null==r?void 0:r.buttons.deny.textColor:null)},more:{bg:Vf(vt(r)&&null!==(c=r.buttons.more)&&void 0!==c&&c.backgroundColor?null==r?void 0:r.buttons.more.backgroundColor:Up),text:Gf(e.default,n,vt(r)&&null!==(u=r.buttons.more)&&void 0!==u&&u.textColor?r.buttons.more.textColor:null)},privacy:{bg:(()=>{if(r){if(St(r)&&r.backgroundColor)return Vf(r.backgroundColor);if(!St(r)&&r.layer.backgroundColor)return r.layer.backgroundColor.toLowerCase()!==Vp?Vf(r.layer.backgroundColor):r.primaryColor&&r.primaryColor.toLowerCase()!==Vp?Vf(r.primaryColor):t.default&&t.default.toLowerCase()!==Vp?Vf(t.default):Vf(Up);if(!St(r)&&r.primaryColor&&r.primaryColor.toLowerCase()!==Vp)return Vf(r.primaryColor)}return t.default&&t.default.toLowerCase()!==Vp?Vf(t.default):Vf(Up)})(),desktopSize:Bf(St(r)&&r.desktopSize?r.desktopSize:64),icon:St(r)&&r.iconColor?r.iconColor:Vp,mobileSize:Bf(St(r)&&r.mobileSize?r.mobileSize:44),text:Vf(Vp)},save:{bg:Vf(r&&!St(r)&&!ht(r)&&null!==(l=r.buttons.save)&&void 0!==l&&l.backgroundColor?r.buttons.save.backgroundColor:e.default),text:Gf(e.default,n,r&&!St(r)&&!ht(r)&&null!==(d=r.buttons.save)&&void 0!==d&&d.textColor?r.buttons.save.textColor:null)}}})(n,r,t);return{a11yFocus:{color:"#767676"},accentColor:!St(t)&&(null==t?void 0:t.accentColor)||"#DDDDDD",buttons:i,expandableLink:{color:!St(t)&&(null==t?void 0:t.links.fontColor)||Pp},firstLayer:{secondLayerTrigger:!St(t)&&(null==t?void 0:t.firstLayer.secondLayerTrigger)||B.LINK},layer:{backgroundColor:!St(t)&&(null==t?void 0:t.layer.backgroundColor)||Fp,borderRadius:!St(t)&&(null==t?void 0:t.layer.borderRadius)||"8px",darkerBackgroundColor:If(.1,!St(t)&&(null==t?void 0:t.layer.backgroundColor)||Fp)},links:{fontColor:!St(t)&&(null==t?void 0:t.links.fontColor)||Pp,iconColor:!St(t)&&(null==t?void 0:t.links.iconColor)||Pp},overlay:{color:!St(t)&&(null==t?void 0:t.overlay.backgroundColor)||"#333",opacity:!St(t)&&(null==t?void 0:t.overlay.opacity)||.7},scroll:{background:"rgba(0,0,0,0)",color:!St(t)&&null!=t&&t.textColor?Of(.6,t.textColor):n.secondary.default},secondLayer:{backgroundColor:Cf(.05,!St(t)&&(null==t?void 0:t.layer.backgroundColor)||Fp),content:{backgroundColor:Of(.98,!St(t)&&(null==t?void 0:t.layer.backgroundColor)||Fp)},dividers:Of(.84,!St(t)&&(null==t?void 0:t.textColor)||n.primary.default)},tabs:{color:!St(t)&&(null==t?void 0:t.secondLayer.tabColor)||n.primary.default},text:{color:!St(t)&&(null==t?void 0:t.textColor)||n.primary.default},toggle:{active:{backgroundColor:!St(t)&&(null==t?void 0:t.toggle.active.backgroundColor)||"#336AB7",iconColor:!St(t)&&(null==t?void 0:t.toggle.active.iconColor)||Bp},disabled:{backgroundColor:!St(t)&&(null==t?void 0:t.toggle.disabled.backgroundColor)||"#dedede",iconColor:!St(t)&&(null==t?void 0:t.toggle.disabled.iconColor)||Bp},inactive:{backgroundColor:!St(t)&&(null==t?void 0:t.toggle.inactive.backgroundColor)||"#595959",iconColor:!St(t)&&(null==t?void 0:t.toggle.inactive.iconColor)||Bp}},useBackgroundShadow:!(!St(t)&&null!=(null==t?void 0:t.useBackgroundShadow))||(null==t?void 0:t.useBackgroundShadow)}};function $f(e,t,n,r){const{color:i,typography:s}=Hf(t,e),o=r||{maxBannerWidth:5e3,maxSideAndCenterContainerWidth:625};return{color:i,spacing:Nf,typography:s,ui:jf(t,e),uiSizes:o,uiVariant:n}}const{color:Yf,typography:Wf}=Hf(!1),zf={color:Yf,spacing:Nf,typography:Wf,ui:jf(!1),uiSizes:{maxBannerWidth:5e3,maxSideAndCenterContainerWidth:625},uiVariant:mo.DEFAULT},Kf=()=>{const e=window.navigator.userAgent.indexOf("MSIE ")>0||window.navigator.userAgent.indexOf("Trident/")>0,t=window.matchMedia("all and (orientation:landscape)").matches,n=window.matchMedia("all and (orientation:portrait)").matches,r=n?window.innerWidth:window.innerHeight;let i=!1,s=!1,o=!1,a=!1;const c=window.innerWidth>Hp.md&&!("ontouchstart"in document.documentElement||window.ontouchstart);return c||(i=r<=Hp.md,s=r<=Hp.md&&r>Hp.sm,o=r<=Hp.sm&&r>Hp.xs,a=r<=Hp.xs),{isDesktop:c,isIE:e,isLandscape:t,isMobile:i,isMobileExtraSmall:a,isMobileMd:s,isMobileSmall:o,isPortrait:n}},Jf=()=>{const[e,t]=ac(Kf());return uc((()=>{const e=()=>{t(Kf())};return window.addEventListener("resize",e,!0),()=>{window.removeEventListener("resize",e,!0)}}),[]),e},qf=e=>e.some((e=>null!=e.dataDistribution)),Xf=e=>e.reduce(((e,t)=>t.isHidden?e:[...e,t]),[]),Qf={allCategories:[],allServices:[],categories:[],getUpdatedCategories:()=>Promise.resolve([]),selectedSubService:null,services:[],setCategories:()=>null,setSelectedSubService:()=>null},Zf=Wa(Qf);Zf.displayName="CategoryContext";const eh=(e,t)=>e.isEssential&&t.isEssential?e.name.toLowerCase()>t.name.toLowerCase()?1:-1:e.isEssential&&!t.isEssential?-1:!e.isEssential&&t.isEssential||e.name.toLowerCase()>t.name.toLowerCase()?1:-1,th=(e,t)=>t.services?[...e,...t.services]:e,nh=Vc((({children:e,UC:t})=>{const n=t.getCategoriesBaseInfo(),[r,i]=ac({all:n,visible:Xf(n)}),[s,o]=ac(Qf.selectedSubService),a=fc((()=>({all:r.all.reduce(th,[]).sort(eh),visible:r.visible.reduce(th,[]).sort(eh)})),[r]),c=hc((async e=>e===fp.SECOND_LAYER||(e=>e.some((e=>qf(e.services))))(r.visible)?Xf(await t.getCategoriesFullInfo()):Xf(t.getCategoriesBaseInfo())),[t,r]);return ya(Zf.Provider,{value:{allCategories:r.all,allServices:a.all,categories:r.visible,getUpdatedCategories:c,selectedSubService:s,services:a.visible,setCategories:e=>{i({all:r.all.length&&r.all.length!==e.length?r.all.map((t=>{const n=e.find((e=>e.slug===t.slug));return n||t})):e,visible:Xf(e)})},setSelectedSubService:o}},e)})),rh=()=>gc(Zf),ih={embeddingContainerClassName:"uc-embed",embeddingOptionsAttributeName:"uc-data",embeddingServiceAttributeName:"uc-consent-name",embeddingShowAllOptions:"all",embeddingShowAllServices:"all",embeddingShowHiddenCategories:"uc-embedding-show-hidden-categories",embeddingShowToggle:"uc-show-toggle",embeddingStyle:"uc-embedding-type",embeddingTitle:"uc-embedding-title",embeddingUseStyling:"uc-styling"},sh="#0045A5",oh="#dedede",ah="#595959",ch="#ffffff",uh={dataCollected:"uc-dataCollected",dataProtectionOfficer:"uc-dataProtectionOfficer",dataPurposes:"uc-dataPurposes",dataRecipients:"uc-dataRecipients",description:"uc-description",furtherInformation:"uc-furtherInformation",legalBasis:"uc-legalBasis",optInCheckboxWithLabel:"uc-optInCheckboxWithLabel",processingLocation:"uc-processingLocation",processorNames:"uc-processorNames",retentionPeriod:"uc-retentionPeriod",technologiesUsed:"uc-technologiesUsed",thirdCountryTransfer:"uc-thirdCountryTransfer",toggleButton:"uc-embed-toggle-button",toggleDisabled:"toggle-disabled",toggleOff:"toggle-off",toggleOn:"toggle-on"},lh={toggleButton:{style:"display: flex; justify-content: center; align-items: center; background: none; border: none; height: 20px; width: 40px; min-height: 20px; min-width: 40px; outline: none; padding: 0"},toggleButtonMargin:{style:"display: flex; justify-content: center; align-items: center; background: none; border: none; height: 20px; width: 40px; min-height: 20px; min-width: 40px; outline: none; padding: 0; margin-right: 15px"}},dh="uc-embed-tcf",ph="uc-embedding-title",fh="uc-embedding-type",hh="uc-embedding-atp-vendors",gh="uc-embedding-purpose",vh="uc-embedding-vendors",Sh="uc-embedding-non-iab-purpose",Eh="uc-embedding-non-iab-vendors",_h="uc-embedding-show-hidden-categories",mh="uc-styling",Ih={link:"uc-embed-link",subElementBlockList:"uc-embed-subelement-block-list",subElementBlockListElement:"uc-embed-subelement-block-list-element",subElementDescription:"uc-embed-subelement-description",subElementHeadline:"uc-embed-subelement-headline",subElementItemList:"uc-embed-subelement-item-list",viewMoreButton:"uc-embed-block-list-view-more-button"},yh={subElementBlockList:{style:"margin: 0px 0px 0px; padding: 0px 15px"},subElementBlockListElement:{style:`font-size: 12px; list-style: none; border: 1px solid ${"#e2e2e2"}; border-radius: 5px; padding: 10px; margin-top: 5px`},viewMoreButton:{style:`border: none; background-color: ${"#fff"}; text-align: left; padding: 15px; color: ${"#0000ff"}`}};let Ch=function(e){return e.CAT="CAT",e.SRV="SRV",e}({});const Th=e=>e.reduce(((e,t)=>e.concat(t.services)),[]).reduce(((e,t)=>(e.push({serviceId:t.id,status:t.consent.status}),e)),[]),bh=(e,t)=>{if(void 0===t)throw new Error("altElement of nullishOperation can not be undefined");return null!=e?e:t},Ah=e=>({purposes:e.purposes.map((e=>({consent:e.consent||!1,id:e.id,legitimateInterestConsent:!!e.showLegitimateInterestToggle&&bh(e.legitimateInterestConsent,!0)}))),specialFeatures:e.specialFeatures.map((e=>({consent:e.consent||!1,id:e.id}))),vendors:e.vendors.map((e=>({consent:e.consent||!1,id:e.id,legitimateInterestConsent:e.specialPurposes.length&&0===e.legitimateInterestPurposes.length&&0===e.purposes.length||!!e.legitimateInterestPurposes.length&&bh(e.legitimateInterestConsent,!0)})))}),Oh=(e,t)=>e.map((e=>{const n=t.find((t=>e.slug===t.slug)),r=e.services.map((e=>{const{consent:t}=(null==n?void 0:n.services.find((t=>e.id===t.id)))||e,r=e;return r.consent=t,r}));return i(i({},e),{},{services:r})})),Nh=(e,t)=>{let n=Cp.CATEGORIES_PURPOSES;if(e&&vt(e)){const{defaultView:t,hideDataProcessingServices:r}=e.secondLayer;n=t===Ch.CAT?Cp.CATEGORIES_PURPOSES:Cp.SERVICES_VENDORS,t===Ch.SRV&&r&&(n=Cp.CATEGORIES_PURPOSES)}if(e&&St(e)&&t===mo.DEFAULT){const{defaultView:t,hideDataProcessingServices:r}=e;n=t===Ch.CAT?Cp.CATEGORIES_PURPOSES:Cp.SERVICES_VENDORS,t===Ch.SRV&&r&&(n=Cp.CATEGORIES_PURPOSES)}return n},wh=Wa({core:null,data:null,labels:null,setSettingsCore:()=>{},setSettingsData:()=>{},setSettingsLabels:()=>{},setSettingsUI:()=>{},ui:null});wh.displayName="SettingsContext";const Rh=Vc((({children:e,UC:t})=>{const[n,r]=ac(t.getSettingsCore()),[i,s]=ac(t.getSettingsData()),[o,a]=ac(t.getSettingsLabels()),[c,u]=ac(t.getSettingsUI());return ya(wh.Provider,{value:{core:n,data:i,labels:o,setSettingsCore:r,setSettingsData:s,setSettingsLabels:a,setSettingsUI:u,ui:c}},e)})),Lh=()=>gc(wh),Dh=(e,t)=>{if(null===e)return" - ";if(e<=0)return t.session;let n=e,r="";if(n>=86400){const e=Math.floor(n/31536e3);e>0&&(n%=31536e3,r=1===e?r.concat(`${e} ${t.year}, `):r.concat(`${e} ${t.years}, `));const i=Math.floor(n/2628e3);i>0&&(n%=2628e3,r=1===i?r.concat(`${i} ${t.month}, `):r.concat(`${i} ${t.months}, `));const s=Math.floor(e/4),o=Math.floor(n/86400),a=0===s?o:o-s;return a>0&&(r=1===a?r.concat(`${a} ${t.day}`):r.concat(`${a} ${t.days}`)),r=r.replace(/,\s*$/,""),r}const i=Math.floor(n/3600);i>0&&(n%=3600,r=1===i?r.concat(`${i} ${t.hour}, `):r.concat(`${i} ${t.hours}, `));const s=Math.floor(n/60);s>0&&(r=1===s?r.concat(`${s} ${t.minute}, `):r.concat(`${s} ${t.minutes}, `));const o=n%60;return o>0&&(r=1===o?r.concat(`${o} ${t.second}, `):r.concat(`${o} ${t.seconds}, `)),r=r.replace(/,\s*$/,""),r};async function Ph(e){let t;try{t=await window.fetch(e)}catch(e){console.warn(`Usercentrics: Failed to fetch data from server: ${e}`)}return t&&t.status>=400&&console.warn("Usercentrics: Failed to fetch data from server"),t}function Vh(e,t,n){return"*"===e?`${e} (${t})`:e.includes("*")?`${e} (${n})`:e}const Uh=function(){const e=(typeof window["safari"]!=="undefined"&&window["safari"].pushNotification||!!navigator.userAgent.match(/Version\/[\d.]+.*Safari/)&&!navigator.userAgent.match(/Version\/[\d.]+.*Chrome/)).toString();return"[object SafariRemoteNotification]"===e||"true"===e}(),kh="#0000ff",Mh="#848484",Fh="#595959",xh="#e2e2e2",Gh={collapsedArrow:"uc-embed-collapsed-arrow",collapsedButton:"uc-embed-collapsed-button",collapsedButtonContent:"uc-embed-collapsed-button-content",collapsedContent:"uc-embed-collapsed-content",collapsedLink:"uc-embed-collapsed-link",collapsedLinkContent:"uc-embed-collapsed-link-content",headline:"uc-embed-headline",listDiv:"uc-embed-list",listHeadline:"uc-embed-list-headline",listHeadlineDescription:"uc-embed-list-headline-description",listItem:"uc-embed-list-item",listItemContent:"uc-embed-list-item-content",listItemHeadline:"uc-embed-list-item-headline",openedArrow:"uc-embed-opened-arrow",openedButton:"uc-embed-opened-button",openedButtonContent:"uc-embed-opened-button-content",openedContent:"uc-embed-opened-content",openedLink:"uc-embed-opened-link",openedLinkContent:"uc-embed-opened-link-content",outerDiv:"uc-embed-container",storedInfoAction:"uc-embed-stored-info-action",storedInfoActionItem:"uc-embed-stored-info-action-item",storedInfoItem:"uc-embed-stored-info-item",storedInfoItemData:"uc-embed-stored-info-item-data",storedInfoItemDataElement:"uc-embed-stored-info-item-data-element",storedInfoItemHeadline:"uc-embed-stored-info-item-headline",subElement:"uc-embed-subelement",subElementItemListElement:"uc-embed-subelement-item-list-element",subElementStoredInfo:"uc-embed-subelement-stored-info",subservice:"uc-subservice",subserviceLinkArrow:"uc-subservice-link-arrow",subserviceLinkButton:"uc-subservice-link-button",subserviceLinkText:"uc-subservice-link-text",subserviceName:"uc-subservice-name",subservicesInfo:"uc-subservices-info",subservicesSection:"uc-subservices"},Bh={backArrow:{style:`border: solid ${kh}; border-width: 1px 0 0 1px; display: inline-block; padding: 3px; height: 0px; margin-top: 4px; ransform: rotate(-45deg); -webkit-transform: rotate(-45deg);`},backButton:{style:"flex: 1; display: flex; justify-content: flex-start; border: none; background-color: white; text-align: left; margin: 0px; padding: 0px 15px 15px 15px; width:100%; cursor: pointer"},backText:{style:`display: inline-block; font-weight: normal; font-size: 0.90em; margin: 0; margin-left: 4px; color: ${kh}`},categoryOnlyDiv:{style:`display: flex; border-bottom: 1px solid ${xh}; justify-content: space-between; align-items: center`},collapsableButton:{style:"border: none; display: inline-flex; background-color: white; text-align: left; padding: 15px; justify-content: space-between; width:100%"},collapsableDiv:{style:`border-bottom: 1px solid ${xh}`},collapsableDivContentHidden:{style:"display: none"},collapsableDivContentShown:{style:"display: inline-block; padding-bottom: 15px; width: 100%"},collapsableLink:{style:"border: none; display: inline-flex; background-color: white; text-align: left; padding: 7px 15px; margin: 0px; width:100%; justify-content: flex-start"},collapsedArrow:{style:`border: solid ${Mh}; border-width: 0 1px 1px 0; display: inline-block; padding: 3px; height: 0px; margin-top: 4px; margin-left:10px; transform: rotate(45deg); -webkit-transform: rotate(45deg);`},collapsedButtonContent:{style:"display: inline-block; font-weight: normal; font-size: 1.17em; margin: 0;"},collapsedLinkArrow:{style:`border: solid ${kh}; border-width: 0 1px 1px 0; display: inline-block; padding: 3px; height: 0px; margin-top: 4px; margin-left:10px; transform: rotate(45deg); -webkit-transform: rotate(45deg);`},collapsedLinkContent:{style:`display: inline-block; font-weight: bold; font-size: 1.0em; color: ${kh}; margin-top: 1px`},detailsArrow:{style:`border: solid ${kh}; border-width: 0 1px 1px 0; display: inline-block; padding: 3px; height: 0px; margin-top: 4px; ransform: rotate(-45deg); -webkit-transform: rotate(-45deg);`},detailsButton:{style:`width: 100%; display: flex; justify-content: space-between; padding: 12px; margin-top: 12px; border: 1px solid ${"#dedede"}; border-radius: 4px; cursor: pointer; background: none`},detailsText:{style:`display: inline-block; font-weight: normal; font-size: 0.90em; margin: 0; margin-right: 4px; color: ${kh}`},flexContainer:{style:"display: flex"},headlines:{style:"padding: 7px 15px; margin: 0px"},headlinesBorderBottom:{style:`padding: 30px 15px 24px; margin: 0px; border-bottom: 1px solid ${xh}`},link:{style:"font-size: 12px; padding: 0px 15px; text-decoration: none"},list:{style:"display: inline-grid"},openedArrow:{style:`border: solid ${Mh}; border-width: 0 1px 1px 0; display: inline-block; padding: 3px; height: 0px; margin-top: 7px; margin-left:10px; transform: rotate(-135deg); -webkit-transform: rotate(-135deg);`},openedButtonContent:{style:"display: inline-block; font-weight: bold; font-size: 1.17em; margin: 0;"},openedLinkArrow:{style:`border: solid ${kh}; border-width: 0 1px 1px 0; display: inline-block; padding: 3px; height: 0px; margin-top: 7px; margin-left:10px; transform: rotate(-135deg); -webkit-transform: rotate(-135deg);`},outerBox:{style:'display: grid; padding: 24px 0px; background-color: white; border-radius: 10px; font-family: "Arial"'},outerBoxNoTopPadding:{style:'display: grid; padding-bottom: 24px; background-color: white; border-radius: 10px; font-family: "Arial"'},storedInfoAction:{style:"display: flex; flex-direction: row; justify-content: center; width: 100%;"},storedInfoActionLink:{style:`font-size: 14px; margin: 0; cursor: pointer; color: ${kh}`},storedInfoActionText:{style:"font-size: 14px; margin: 0"},storedInfoItem:{style:`display: flex; flex-direction: column; padding: 12px; border: 1px solid ${xh};`},storedInfoItemDescription:{style:`font-size: 12px; color: ${Fh}; margin: 0px 0px 4px 0px;`},storedInfoItemHeader:{style:"font-size: 12px; font-weight: bold; margin: 0; margin-bottom: 4px;"},storedInfoItemSubElement:{style:"font-size: 12px; margin: 0;"},storedInfoItemSubElementRow:{style:"display: flex; font-size: 12px; margin: 0; margin-top: 4px;"},storedInfoItemSubElementTitle:{style:"min-width: 30%; margin: 0"},subElement:{style:"margin: 5px 0px 15px"},subElementDescription:{style:"font-size: 12px; padding: 0px 15px; margin: 5px 0px 0px"},subElementExpandableLink:{style:"margin: 5px 15px 15px; width: calc(100% - 30px)"},subElementItemList:{style:"margin: 5px 0px 10px; padding-right: 15px"},subElementItemListElement:{style:"font-size: 12px; padding: 7px 0px 5px 0px"},subserviceInfo:{style:`font-size: 12px; padding: 0px 15px; margin: 0px 0px 24px; color: ${Fh}`},subserviceList:{style:"padding: 0 15px;"},subserviceListItem:{style:`display: flex; flex-direction: row; justify-content: space-between; padding: 12px; margin-top: 12px; border: 1px solid ${xh}; border-radius: 4px;`},subserviceListItemName:{style:"font-weight: normal; font-size: 0.90em; margin: 0;"},verticalSpacer:{style:"display: block; min-height: 16px"}};let Hh=function(e){return e.DATA_COLLECTED="dataCollected",e.DATA_PROTECTION_OFFICER="dataProtectionOfficer",e.DATA_PURPOSES="dataPurposes",e.DATA_RECIPIENTS="dataRecipients",e.DESCRIPTION="description",e.FURTHER_INFORMATION="furtherInformation",e.LEGAL_BASIS="legalBasis",e.OPT_IN_CHECKBOX_WITH_LABEL="optInCheckboxWithLabel",e.PROCESSING_LOCATION="processingLocation",e.PROCESSOR_NAMES="processorNames",e.RETENTION_PERIOD="retentionPeriod",e.TECHNOLOGIES_USED="technologiesUsed",e.THIRD_COUNTRY_TRANSFER="thirdCountryTransfer",e}({}),jh=function(e){return e.ALL="all",e.CATEGORY="category",e.CATEGORY_ONLY="category-only",e}({}),$h=function(e){return e.VENDOR="vendor",e.PURPOSE="purpose",e}({});const Yh=e=>e&&e.some((e=>e.processingCompany));var Wh,zh=new Uint8Array(16);function Kh(){if(!Wh&&!(Wh="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Wh(zh)}var Jh=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var qh=[],Xh=0;Xh<256;++Xh)qh.push((Xh+256).toString(16).substr(1));function Qh(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(qh[e[t+0]]+qh[e[t+1]]+qh[e[t+2]]+qh[e[t+3]]+"-"+qh[e[t+4]]+qh[e[t+5]]+"-"+qh[e[t+6]]+qh[e[t+7]]+"-"+qh[e[t+8]]+qh[e[t+9]]+"-"+qh[e[t+10]]+qh[e[t+11]]+qh[e[t+12]]+qh[e[t+13]]+qh[e[t+14]]+qh[e[t+15]]).toLowerCase();if(!function(e){return"string"==typeof e&&Jh.test(e)}(n))throw TypeError("Stringified UUID is invalid");return n}function Zh(e,t,n){var r=(e=e||{}).random||(e.rng||Kh)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var i=0;i<16;++i)t[n+i]=r[i];return t}return Qh(r)}const eg=5,tg=(e,t)=>{var n;const r=bh(e.getAttribute(ih.embeddingServiceAttributeName),ih.embeddingShowAllServices),i=(e=>{const t=e.getAttribute(ih.embeddingOptionsAttributeName);if(!t||t.includes(ih.embeddingShowAllOptions))return Object.values(Hh);const n=t.replace(" ","").split(",");return Object.values(Hh).filter((e=>n.includes(e)))})(e),s=bh(e.getAttribute(ih.embeddingTitle),t),o=null!==(n=e.getAttribute(ih.embeddingStyle))&&void 0!==n?n:"all",a=bh(e.getAttribute(ih.embeddingShowToggle),"false"),c=bh(e.getAttribute(ih.embeddingUseStyling),"false");return{embedService:r,embedTitle:s,embedType:o,sections:i,showHiddenCategories:"true"===e.getAttribute(ih.embeddingShowHiddenCategories),showToggle:"true"===a,useUcStyling:"true"===c}},ng=(e,t)=>{const n=e.getAttribute(fh)===$h.PURPOSE?$h.PURPOSE:$h.VENDOR,r=e.getAttribute(ph)||(n===$h.PURPOSE?t.titles.purposes:t.secondLayer.vendorsTab),i=e.getAttribute(vh)||t.titles.iabVendors,s=e.getAttribute(Eh)||t.titles.nonIabVendors,o=e.getAttribute(gh)||t.titles.purposes,a=e.getAttribute(Sh)||t.titles.nonIabPurposes,c=e.getAttribute(hh)||t.titles.acmVendors,u=e.getAttribute(mh);return{embedTitle:r,embedType:n,googleAtpVendorsTitle:c,iabPurposeTitle:o,iabVendorsTitle:i,nonIabPurposeTitle:a,nonIabVendorsTitle:s,showHiddenCategories:"true"===e.getAttribute(_h),useUcStyling:"false"!==u}},rg=e=>{for(;e.lastElementChild;)e.removeChild(e.lastElementChild)},ig=(e,...t)=>{t.forEach((t=>{Object.entries(t).forEach((([t,n])=>{e.setAttribute(t,n)}))}))},sg=(e,t,n,r=[],i=!1)=>{const s=document.createElement(e);if(t&&"string"==typeof t&&""!==t?s.className=t:t&&Array.isArray(t)&&s.classList.add(...t),n&&n.length>0&&n.forEach((e=>{(!e.style||e.style&&i)&&ig(s,e)})),r)if("string"==typeof r)s.innerText=r;else if(Array.isArray(r)){const e=document.createDocumentFragment();r.forEach((t=>e.appendChild(t))),s.appendChild(e)}else s.appendChild(r);return s},og=(e,t)=>{const{isTCF:n,useUcStyling:r,isSubservice:i,nonTcfClassName:s}=t;let o="h5";return r||n||i||(o="h4"),sg(o,n?Ih.subElementHeadline:`uc uc-heading ${s}`,[Bh.headlines],e,r)},ag=(e,t)=>{const{isTCF:n,useUcStyling:r,nonTcfClassName:i}=t;return sg("p",n?Ih.subElementDescription:`uc uc-p ${i}`,[Bh.subElementDescription],e,r)},cg=(e,t,n,r,i)=>{let s,o=uh.toggleButton,a=[uh.toggleButton];e?(s=(()=>{const e=`<svg width="42px" height="21px" viewBox="0 0 42 23" version="1.1" xmlns="http://www.w3.org/2000/svg">\n  <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">\n    <g transform="translate(-86.000000, -31.000000)">\n      <g transform="translate(87.000000, 32.949632)">\n        <path\n          d="M30.0094932,-0.5 C32.8534346,-0.5 35.4330697,0.632406246 37.3226603,2.47154687 C39.2173227,4.31562389 40.4181217,6.87029149 40.4959736,9.70631513 C40.4981622,12.7440885 39.3375699,15.4233885 37.4620437,17.3539966 C35.5609243,19.3109495 32.9241909,20.5 30.0094932,20.5 L30.0094932,20.5 L9.99050677,20.5 C7.14626154,20.5 4.56639324,19.3679366 2.67675543,17.5287725 C0.782448937,15.6850644 -0.418130086,13.1306858 -0.49597372,10.2937978 C-0.498162151,7.25597731 0.662425032,4.576654 2.53794226,2.64603293 C4.43906304,0.689056047 7.07580212,-0.5 9.99050677,-0.5 L9.99050677,-0.5 Z"\n          stroke="${ch}"\n          fill="${oh}"\n        />\n        <path\n          d="M30,18 C34.418278,18 38,14.418278 38,10 C38,5.581722 34.418278,2 30,2 C25.581722,2 22,5.581722 22,10 C22,14.418278 25.581722,18 30,18 Z"\n          fill="${ch}"\n        />\n        <path\n          d="M15.8228065,6.13470278 C16.038065,6.32450074 16.0587048,6.65286384 15.8689068,6.8681223 C15.8665182,6.87083131 15.8641014,6.87351535 15.8616569,6.87617398 L9.47642761,13.8205455 C9.39995063,13.9037194 9.30336409,13.9576046 9.20086748,13.9814572 C9.01166585,14.0332136 8.80114396,13.9796182 8.65955286,13.8256282 L8.65955286,13.8256282 L6.13834311,11.083641 L6.13834311,11.083641 L6.13109318,11.0755893 C5.94129522,10.8603308 5.96193502,10.5319677 6.17719348,10.3421698 C6.39831738,10.1472001 6.73475108,10.164828 6.93428873,10.3818388 L6.93428873,10.3818388 L9.06521734,12.6992981 L15.0657113,6.17437179 C15.2652489,5.95736102 15.6016826,5.93973313 15.8228065,6.13470278 Z"\n          fill="${ch}"\n        />\n      </g>\n    </g>\n  </g>\n</svg>`;return(new DOMParser).parseFromString(e,"image/svg+xml").documentElement})(),o+="-disabled",a=[...a,uh.toggleDisabled]):(s=t?(()=>{const e=`<svg width="100%" height="100%" viewBox="0 0 42 22" xmlns="http://www.w3.org/2000/svg">\n      <g fill="none" fillRule="evenodd">\n      <path\n        d="M0 11C0 4.925 4.92 0 11.008 0h19.984C37.072 0 42 4.924 42 11c0 6.075-4.92 11-11.008 11H11.008C4.928 22 0 17.08 0 11z"\n        fill="${sh}"\n      />\n      <path d="M31 20a9 9 0 100-18 9 9 0 000 18z" fill="white" />\n      <path d="M8 6h10v10H8z" />\n      <path\n        d="M17.225 7.671c.192.173.208.47.035.662l-5.59 6.209a.467.467 0 01-.202.131l-.02.006a.467.467 0 01-.492-.133l-2.209-2.452a.469.469 0 01.697-.627l1.866 2.072 5.253-5.833a.469.469 0 01.662-.035z"\n        fill="${ch}"\n      />\n    </g>\n  </svg>`;return(new DOMParser).parseFromString(e,"image/svg+xml").documentElement})():(()=>{const e=`<svg width="100%" height="100%" viewBox="0 0 42 22" xmlns="http://www.w3.org/2000/svg">\n      <g fill="none" fillRule="evenodd">\n      <path\n        d="M0 11C0 4.925 4.92 0 11.008 0h19.984C37.072 0 42 4.924 42 11c0 6.075-4.92 11-11.008 11H11.008C4.928 22 0 17.08 0 11z"\n        fill="${ah}"\n      />\n      <path d="M11 20a9 9 0 100-18 9 9 0 000 18z" fill="white" />\n      <path\n        d="M29.725 11.204l3.318-3.315a.51.51 0 10-.724-.724L29 10.48l-3.319-3.315a.51.51 0 10-.724.724l3.318 3.315-3.318 3.317a.51.51 0 10.724.723L29 11.928l3.319 3.317a.509.509 0 00.724 0c.2-.2.2-.525 0-.724l-3.318-3.317z"\n        fill="${ch}"\n        fillRule="nonzero"\n      />\n    </g>\n  </svg>`;return(new DOMParser).parseFromString(e,"image/svg+xml").documentElement})(),t?(o+="-on",a=[...a,uh.toggleOn]):(o+="-off",a=[...a,uh.toggleOff]));const c=sg("button",a,[n?lh.toggleButtonMargin:lh.toggleButton],[s],r);return c.setAttribute("data-testid",o),c.setAttribute("type","button"),c.setAttribute("role","switch"),c.setAttribute("aria-disabled",e?"true":"false"),c.setAttribute("aria-checked",t?"true":"false"),c.setAttribute("aria-readonly",e?"true":"false"),e||(c.onclick=i),c},ug=(e,t,n)=>sg("p",`${n.isTCF?"uc uc-p ":""}${Gh.subservicesInfo}`,[Bh.subserviceInfo],`${e} ${e>1?t.subservices:t.subservice}`,n.useUcStyling),lg=(e,t)=>sg("h4",t.isTCF?Ih.subElementHeadline:"uc uc-title",[],e,t.useUcStyling),dg=(e,t,n)=>{const{isTCF:r,useUcStyling:i}=n,{name:s,parentName:o,scrollToId:a}=e,c=!!e.parentName;let u;const l=sg("div",`${r?"":"uc "}${Gh.subserviceLinkText}`,c?[Bh.backText]:[Bh.detailsText],c?`${t.back} ${o}`:t.details,i),d=sg("i",`${r?"":"uc "}${Gh.subserviceLinkArrow}`,c?[Bh.backArrow]:[Bh.detailsArrow],[],i);if(c)u=sg("button",`${r?"":"uc "}${Gh.subserviceLinkButton}`,[Bh.backButton],[d,l],i);else{const e=sg("p",`${r?"":"uc "}${Gh.subserviceName}`,[Bh.subserviceListItemName],s,i),t=sg("div",`${r?"":"uc "}${Gh.subserviceLinkText}`,[],[l,d],i);u=sg("button",`${r?"":"uc "}${Gh.subserviceLinkButton}`,[Bh.detailsButton],[e,t],i)}return u.setAttribute("type","button"),u.addEventListener("click",(()=>{const e=document.getElementById(c?`uc-service-${a}`:`uc-subservice-${a}`);"uc-embed-collapsed-button"===(null==e?void 0:e.className)&&e.click(),null==e||e.scrollIntoView({behavior:"smooth"})})),u},pg=(e,t,n,r,i)=>{e.appendChild(sg("div",Gh.subElement,[Bh.subElement],[...n?[og(n,t)]:[],...r?[ag(r,t)]:[],...i?[i]:[]],t.useUcStyling))},fg=(e,t,n,r)=>{n.length>0&&e.appendChild(sg("div",Gh.subElement,[Bh.subElement],[...r?[og(r,t)]:[],...n.map((e=>ag(e,t)))],t.useUcStyling))},hg=(e,t,n,r,i,s)=>{const{isTCF:o,useUcStyling:a,nonTcfClassName:c}=t,u=sg("a",o?Ih.link:`uc uc-a ${c}`,[{href:n},Bh.link],r,a);pg(e,t,i,s,u)},gg=(e,t,n,r,i)=>{const{isTCF:s,useUcStyling:o,nonTcfClassName:a}=t,c=sg("ul",s?Ih.subElementItemList:`uc uc-list ${a}`,[Bh.subElementItemList],n.map((e=>sg("li",Gh.subElementItemListElement,[Bh.subElementItemListElement],e,o))),o);pg(e,t,r,i,c)},vg=(e,t,n,r,i)=>{const s=document.getElementById(r);if(s){const{collapsed:r,collapsedContent:o,collapsedStyles:a,opened:c,openedContent:u,openedStyles:l}="button"===i?{collapsed:Gh.collapsedButton,collapsedContent:Gh.collapsedButtonContent,collapsedStyles:Bh.collapsedButtonContent,opened:Gh.openedButton,openedContent:Gh.openedButtonContent,openedStyles:Bh.openedButtonContent}:{collapsed:Gh.collapsedLink,collapsedContent:Gh.collapsedLinkContent,collapsedStyles:Bh.collapsedLinkContent,opened:Gh.openedLink,openedContent:Gh.openedLinkContent,openedStyles:Bh.openedLinkArrow};"inline-block"===s.style.display?(ig(s,Bh.collapsableDivContentHidden),s.setAttribute("class",Gh.collapsedContent),ig(t,a),ig(n,"button"===i?Bh.collapsedArrow:Bh.collapsedLinkArrow),n.setAttribute("class",Gh.collapsedArrow),t.setAttribute("class",o),e.setAttribute("class",r)):(ig(s,Bh.collapsableDivContentShown),s.setAttribute("class",Gh.openedContent),"button"===i?(ig(t,l),ig(n,Bh.openedArrow)):ig(n,Bh.openedLinkArrow),n.setAttribute("class",Gh.openedArrow),t.setAttribute("class",u),e.setAttribute("class",c))}},Sg=(e,t,n,r,i)=>{let s;if(r){let e=null,o=null;const a=sg("h4",Gh.collapsedButtonContent,[Bh.collapsedButtonContent],t,r),c=sg("i",Gh.collapsedArrow,[Bh.collapsedArrow],[],r);if(i&&i.showToggle){const{service:t}=i;a.id=`${t.id}-title`,e=cg(t.isEssential,t.consent.status,!1,r,(e=>{const n=window[np];return e.stopPropagation(),n?t.consent.status?n.rejectService(t.id):n.acceptService(t.id):Promise.reject()})),e.setAttribute("aria-labelledby",`${t.id}-title`),o=sg("div","",[Bh.flexContainer],[e,c],r)}const u=sg("button",Gh.collapsedButton,[Bh.collapsableButton],null!=i&&i.showToggle&&e&&o?[a,o]:[a,c],r);u.setAttribute("data-testid","uc-embed-collapsable-button"),u.setAttribute("type","button"),i&&u.setAttribute("id",i.isSubservice?`uc-subservice-${i.service.id}`:`uc-service-${i.service.id}`);const l=sg("div",Gh.collapsedContent,[Bh.collapsableDivContentHidden,{id:`uc-embed-collapsable-${Zh()}`}],n,r);l.setAttribute("data-testid","uc-embed-collapsable-content"),u.addEventListener("click",(()=>vg(u,a,c,l.id,"button"))),s=sg("div",Gh.listItem,[Bh.collapsableDiv],[u,l],r)}else s=sg("div",Gh.listItem,[],[sg("h4",Gh.listItemHeadline,[],t,r),sg("div",Gh.listItemContent,[],n,r)],r);e.appendChild(s)},Eg=(e,t,n,r)=>{const{cookieMaxAgeSeconds:i,usesNonCookieAccess:s,cookieRefresh:o,usesCookies:a}=t;if(i||s||o||a){const t=((e,t,n,r,i)=>{const{maximumAge:s,nonCookieStorage:o,yes:a,no:c,cookieRefresh:u,cookieStorage:l}=e,d=[];return null!==i&&d.push(`${l}: ${i?`${a}`:`${c}`}`),null!==t&&d.push(`${s}: ${Dh(t,e)}`),null!==r&&d.push(`${u}: ${r?`${a}`:`${c}`}`),null!==n&&d.push(`${o}: ${n?`${a}`:`${c}`}`),d})(n,i,s,o,a);gg(e,r,t,n.title,null)}},_g=(e,t,n,r,i,s)=>{const o=[],{useUcStyling:a}=i;switch(r){case"loading":o.push(sg("p",Gh.storedInfoActionItem,[Bh.storedInfoActionText],n.loading,a));break;case"retry":{o.push(sg("p",Gh.storedInfoActionItem,[Bh.storedInfoActionText],n.error,a));const r=sg("p",Gh.storedInfoActionItem,[Bh.storedInfoActionLink],n.tryAgain,a);r.addEventListener("click",(()=>{Ig(e,null,t,n,i,s)})),o.push(r);break}}const c=sg("div",Gh.storedInfoAction,[Bh.storedInfoAction],o,a),u=sg("div",Gh.storedInfoItem,[Bh.storedInfoItem],c,a);e.appendChild(sg("div",Gh.subElementStoredInfo,[Bh.subElementExpandableLink],u,a))},mg=(e,t,n,r)=>{const i=sg("p",Gh.storedInfoItemDataElement,[Bh.storedInfoItemSubElementTitle],`${t}: `,r),s=sg("p",Gh.storedInfoItemDataElement,[Bh.storedInfoItemSubElement],`${n}`,r);e.push(sg("div",Gh.storedInfoItemData,[Bh.storedInfoItemSubElementRow],[i,s],r))},Ig=async(e,t,n,r,i,s)=>{const o=()=>{for(;e.firstChild;)e.removeChild(e.firstChild)},a=t=>{o(),_g(e,t,r,"retry",i)},c=t=>{o(),((e,t,n,r,i)=>{const{useUcStyling:s,isTCF:o}=r,a=t.disclosures.map((e=>{const t=[],{cookieRefresh:r,description:o,domain:a,identifier:c,maxAgeSeconds:u,name:l,purposes:d,type:p}=e,f=c?`${c}`:`${l}`;if(t.push(sg("p",Gh.storedInfoItemHeadline,[Bh.storedInfoItemHeader],f,s)),o&&t.push(sg("p",Gh.storedInfoItemHeadline,[Bh.storedInfoItemDescription],o,s)),mg(t,n.type,`${p}`,s),"cookie"===p.toString()&&(mg(t,n.duration,Dh(u,n),s),null!=r&&mg(t,n.cookieRefresh,r?n.yes:n.no,s)),a&&mg(t,n.domain,Vh(a,n.anyDomain,n.multipleDomains),s),d&&d.length>0){const e=[];d.forEach((t=>{var n;e.push((null==i||null===(n=i.find((e=>e.id===t)))||void 0===n?void 0:n.name)||"")})),mg(t,n.purposes,e.join("; "),s)}return sg("div",Gh.storedInfoItem,[Bh.storedInfoItem],t,s)}));if(o&&t.domains&&t.domains.length>0){const e=t.domains.map((e=>{const t=[],{domain:r,use:i}=e;return t.push(sg("p",Gh.storedInfoItemHeadline,[Bh.storedInfoItemHeader],r,s)),i&&mg(t,n.purposes,i,s),sg("div",Gh.storedInfoItem,[Bh.storedInfoItem],t,s)}));a.push(sg("div","",[Bh.verticalSpacer],"",s)),a.push(...e)}e.appendChild(sg("div",Gh.subElementStoredInfo,[Bh.subElementExpandableLink],a,s))})(e,t,r,i,s)};if(o(),_g(e,"",r,"loading",i),n&&""!==n)try{const e=await Ph(n),t=e?await e.json():null;if(null!=t&&t.disclosures){const{disclosures:e,domains:n}=t,r={disclosures:e,domains:n};null!=r&&r.disclosures&&c(r)}else a(n)}catch(e){a(n)}else t&&t.disclosures.length>0&&c(t)},yg=async(e,t,n,r,i)=>{let s=null;const o={disclosures:[]};if(n&&""!==n)try{const e=await Ph(n),t=e?await e.json():null;if(null!=t&&t.disclosures){const{disclosures:e,domains:n}=t;s={disclosures:e,domains:n}}return o}catch(e){return o}else{if(!(t&&t.disclosures.length>0))return o;s=t}return s.disclosures&&(o.disclosures=s.disclosures.map((t=>{const{cookieRefresh:n,description:r,domain:s,identifier:o,maxAgeSeconds:a,name:c,purposes:u,type:l}=t,d=o?`${e.name}: ${o};`:`${e.name}: ${c};`,p=r?`${r};`:"",f=`${e.type}: ${l};`,h=`${e.duration}: ${Dh(a,e)}`,g=s?`${e.domain}: ${Vh(s,e.anyDomain,e.multipleDomains)};`:"",v=null!=n?`${e.cookieRefresh}: ${n?e.yes:e.no}`:"";let S="";if(u&&u.length>0){const t=[];u.forEach((e=>{var n;t.push((null==i||null===(n=i.find((t=>t.id===e)))||void 0===n?void 0:n.name)||"")})),S=`${e.purposes}: ${t.join("; ")}`}let E="";return"cookie"===l.toString()&&(E=`${h};`,""!==v&&(E+=` ${v};`)),`${d} ${p} ${f} ${E} ${g} ${S}`}))),r.isTCF&&s.domains&&(o.domains=s.domains.map((e=>{const{domain:t,use:n}=e;return`${t}: ${n}`}))),o},Cg=(e,t,n,r,s)=>{const{deviceStorage:o,deviceStorageDisclosureUrl:a}=t;if((null==o?void 0:o.disclosures)&&o.disclosures.length>0||a&&""!==a){const t=document.createDocumentFragment();((e,t,n,r,s,o,a,c)=>{const{isTCF:u,useUcStyling:l,nonTcfClassName:d}=a;let p;if(l){const e=sg("div",Gh.collapsedLinkContent,[Bh.collapsedLinkContent],t,l),i=sg("i",Gh.collapsedArrow,[Bh.collapsedLinkArrow],[],l),u=sg("button",Gh.collapsedLink,[Bh.collapsableLink],[e,i],l),d=sg("div",Gh.collapsedContent,[Bh.collapsableDivContentHidden,{id:`uc-embed-collapsable-${Zh()}`}],n,l);u.setAttribute("type","button"),u.addEventListener("click",(()=>{Ig(d,r,s,o,a,c),vg(u,e,i,d.id,"link")})),p=sg("div",Gh.subElement,[],[u,d],l)}else{const e=sg("div",Gh.listItemContent,[],n,l);p=sg("div",Gh.subElement,[],[sg(l||u?"h5":"h4",u?Gh.listItemHeadline:`uc uc-heading ${d}`,[],t,l),e],l),yg(o,r,s,a,c).then((t=>{t.disclosures.length>0?(gg(e,a,t.disclosures,"",null),t.domains&&t.domains.length>0&&(e.appendChild(sg("div","",[Bh.verticalSpacer],"",l)),gg(e,a,t.domains,"",null))):gg(e,i(i({},a),{},{nonTcfClassName:u?null:`${d}Error`}),[o.error],"",null)}))}e.appendChild(p)})(e,n.storedInformation,t,o,a,n,r,s)}},Tg=(e,t,n,r)=>{const i=document.createDocumentFragment();t.forEach((e=>{i.appendChild(dg({name:e.name,scrollToId:e.id},n,r))})),e.appendChild(i)},bg={tcfData:null,setTcfData:()=>{}},Ag=Wa(bg);Ag.displayName="TcfContext";const Og=Vc((({children:e})=>{const[t,n]=ac(bg.tcfData);return ya(Ag.Provider,{value:{setTcfData:n,tcfData:t}},e)})),Ng=()=>gc(Ag),wg={abTestVariant:"",baseHref:"",clearCategoryPurposeScrollToId:()=>{},clearServiceVendorScrollToId:()=>{},closeView:()=>{},currentLanguage:"",firstLayerVariant:null,isAmpEnabled:!1,isInFullScreen:!1,previousLanguage:"",previousView:fp.NONE,scrollToIdTabViewCategoryPurpose:"",scrollToIdTabViewServiceVendors:"",scrollToSubServices:!1,secondLayerVariant:null,setBaseHref:()=>{},setIsInFullScreen:()=>{},setKeyPressed:()=>{},setLanguage:()=>{},setScrollToIdTabViewCategoryPurpose:()=>{},setScrollToIdTabViewServiceVendor:()=>{},setScrollToSubServices:()=>{},setTabView:()=>{},settingsCloseView:fp.NONE,setView:()=>Promise.resolve(),tabView:Cp.CATEGORIES_PURPOSES,uiVariant:mo.DEFAULT,view:fp.NONE},Rg=Wa(wg);Rg.displayName="UiContext";const Lg=Vc((({children:e,initialUIValues:t,UC:n})=>{const{ui:r,setSettingsLabels:i,setSettingsUI:s}=Lh(),[o]=ac(n.getAbTestVariant()),[a,c]=ac(wg.baseHref),[u]=ac(t.ampEnabled),[l]=ac(t.variant),[d,p]=ac(null),[f,h]=ac(null),[g,v]=ac(null),S=dc(!1),E=dc(null);uc((()=>{(vt(r)||ht(r))&&(d!==r.firstLayer.variant&&p(r.firstLayer.variant),f!==r.secondLayer.variant&&h(r.secondLayer.variant))}),[r]);const[_,m]=ac(wg.scrollToIdTabViewCategoryPurpose),[I,y]=ac(wg.scrollToIdTabViewServiceVendors),[C,T]=ac(wg.scrollToSubServices),[b,A]=ac(wg.tabView),[O,N]=ac((()=>{switch(t.initialLayer){case _o.FIRST_LAYER:return{current:fp.FIRST_LAYER,previous:wg.previousView};case _o.PRIVACY_BUTTON:return{current:fp.PRIVACY_BUTTON,previous:wg.previousView};case _o.SECOND_LAYER:return{current:fp.SECOND_LAYER,previous:wg.previousView};default:return{current:fp.NONE,previous:wg.previousView}}})),[w,R]=ac(wg.settingsCloseView),[L,D]=ac(wg.isInFullScreen),[P,V]=ac({currentLanguage:wg.currentLanguage,previousLanguage:wg.previousLanguage}),{getUpdatedCategories:U,setCategories:k,categories:M}=rh(),{setTcfData:F}=Ng(),x=e=>{S.current=e},G=async e=>{(e=>{if(!(O.current!==fp.NONE&&O.current!==fp.PRIVACY_BUTTON||e!==fp.FIRST_LAYER&&e!==fp.SECOND_LAYER)){var t,n,r;const e=(null===(t=document)||void 0===t||null===(t=t.activeElement)||void 0===t?void 0:t.querySelector(":focus"))||(null===(n=document)||void 0===n||null===(n=n.activeElement)||void 0===n||null===(n=n.shadowRoot)||void 0===n?void 0:n.querySelector(":focus"))||(null===(r=document)||void 0===r?void 0:r.activeElement);e&&v(e)}O.current!==fp.FIRST_LAYER&&O.current!==fp.SECOND_LAYER||e!==fp.NONE&&e!==fp.PRIVACY_BUTTON||(g&&S.current&&g.focus(),v(null),x(!1))})(e),await n.updateLayer((e=>{switch(e){case fp.FIRST_LAYER:return _o.FIRST_LAYER;case fp.SECOND_LAYER:return _o.SECOND_LAYER;case fp.PRIVACY_BUTTON:return _o.PRIVACY_BUTTON;case fp.NONE:default:return _o.NONE}})(e)).then((async()=>{if(e!==fp.NONE){if(e!==fp.FIRST_LAYER&&e!==fp.SECOND_LAYER){const t=await U(e);k(t)}else k(Oh(await U(e),M));i(n.getSettingsLabels()),s(n.getSettingsUI()),l===mo.TCF&&F(n.getTCFData())}else if((()=>{const e=Array.from(document.getElementsByClassName(dh));return Array.from(document.getElementsByClassName(ih.embeddingContainerClassName)).length>0||e.length>0})()){const t=await U(e);k(t)}})),e!==fp.SECOND_LAYER?R(e):R(O.current),N((t=>({current:e,previous:t.current})))};return ya(Rg.Provider,{value:{abTestVariant:o,baseHref:a,clearCategoryPurposeScrollToId:()=>{m("")},clearServiceVendorScrollToId:()=>{y("")},closeView:async()=>{if(r&&!St(r)){const e=window.location.href;let t=!r.privacyButtonUrls||0===r.privacyButtonUrls.contains.length;r.privacyButtonUrls&&r.privacyButtonUrls.contains.length>0&&r.privacyButtonUrls.contains.some((t=>e.includes(t)))&&(t=!0),r.enablePrivacyButton&&t?await G(fp.PRIVACY_BUTTON):await G(fp.NONE)}else await G(fp.NONE)},currentLanguage:P.currentLanguage,firstLayerVariant:d,isAmpEnabled:u,isInFullScreen:L,languageModalRef:E,previousLanguage:P.previousLanguage,previousView:O.previous,scrollToIdTabViewCategoryPurpose:_,scrollToIdTabViewServiceVendors:I,scrollToSubServices:C,secondLayerVariant:f,setBaseHref:c,setIsInFullScreen:D,setKeyPressed:x,setLanguage:e=>{V((t=>({currentLanguage:e,previousLanguage:t.currentLanguage})))},setScrollToIdTabViewCategoryPurpose:m,setScrollToIdTabViewServiceVendor:y,setScrollToSubServices:T,setTabView:A,settingsCloseView:w,setView:G,tabView:b,uiVariant:l,view:O.current}},ya("div",{ref:E}),e)})),Dg=()=>gc(Rg),Pg=()=>({service:Array.from(document.getElementsByClassName(ih.embeddingContainerClassName)),tcf:Array.from(document.getElementsByClassName(dh))}),Vg=(e,t,n)=>{const[r,i]=ac(null),{core:s,ui:o}=Lh(),{abTestVariant:a,setBaseHref:c,view:u,previousView:l}=Dg(),{setBodyScrolling:d}=(()=>{const[e,t]=ac(!0);return uc((()=>{if(document.body){if(!e){if(!document.getElementById("uc-overflow-style")){const e=document.createElement("style");e.id="uc-overflow-style",e.innerHTML=".overflowHidden {overflow: hidden !important;}",document.body.appendChild(e)}document.body.classList.add("overflowHidden")}e&&document.body&&document.body.classList.contains("overflowHidden")&&document.body.classList.remove("overflowHidden")}}),[e,t]),{setBodyScrolling:t}})(),p=Xd();uc((()=>{if(t.ampEnabled){const e=Tp();c((null==e?void 0:e.baseHref)||"")}switch(t.initialLayer){case _o.FIRST_LAYER:d(!1);break;case _o.PRIVACY_BUTTON:d(!0);break;case _o.SECOND_LAYER:d(!1);break;default:d(!0)}}),[]),uc((()=>{if(s){const e=$f(o,Zd.includes(s.language.selected),t.variant);wp(e,p)||n(e)}}),[o]),uc((()=>{if(u!==l){const e=new window.CustomEvent(hp.VIEW_CHANGED,{detail:{previousView:l,view:u}});window.dispatchEvent(e)}}),[l,u]),uc((()=>{if(o&&s&&u!==l&&u!==r){switch(u){case fp.FIRST_LAYER:if(!St(o)){const{isOverlayEnabled:t}=o.firstLayer;d(!t),l!==fp.SECOND_LAYER&&Rp(u,pp.CMP_SHOWN,a,e.setTrackingPixel)}break;case fp.SECOND_LAYER:if(!St(o)){const{isOverlayEnabled:t}=o.secondLayer;d(!t),l!==fp.FIRST_LAYER&&Rp(u,pp.CMP_SHOWN,a,e.setTrackingPixel)}break;case fp.PRIVACY_BUTTON:case fp.NONE:default:d(!0)}i(u)}}),[e.setTrackingPixel,l,d,u,a,o,r])};class Ug{constructor({scope:e,method:n,result:r,setResult:i}){t(this,"scope",void 0),t(this,"isMounted",!1),t(this,"method",void 0),t(this,"mostRecentMutationId",void 0),t(this,"previousResult",void 0),t(this,"result",void 0),t(this,"setResult",void 0),this.scope=e,this.method=n,this.mostRecentMutationId=Zh(),this.result=r,this.setResult=i,this.runMutation=this.runMutation.bind(this)}execute(e){return this.isMounted=!0,[this.runMutation,e]}async runMutation(...e){this.onMutationStart();const t=this.generateNewMutationId();return this.mutate(...e).then((e=>(this.onMutationCompleted(e,t),e||{data:null}))).catch((e=>{throw this.onMutationError(e,t),e}))}mutate(...e){return this.method.call(this.scope,...e)}onMutationStart(){this.result.loading||this.updateResult({called:!0,data:null,error:null,loading:!0})}onMutationCompleted(e,t){this.isMostRecentMutation(t)&&this.updateResult({called:!0,data:e?e.data:null,error:null,loading:!1})}onMutationError(e,t){this.isMostRecentMutation(t)&&this.updateResult({called:!0,data:null,error:e,loading:!1})}generateNewMutationId(){const e=Zh();return this.mostRecentMutationId=e,e}isMostRecentMutation(e){return this.mostRecentMutationId===e}updateResult(e){!this.isMounted||this.previousResult&&wp(this.previousResult,e)||(this.setResult(e),this.previousResult=e)}}const kg=(e,t)=>{const[n,r]=ac({called:!1,loading:!1}),i=dc();return(i.current||(i.current=new Ug({method:e,result:n,scope:t,setResult:r})),i.current).execute(n)};let Mg=function(e){return e.AUTO="auto",e.START="start",e.CENTER="center",e.END="end",e}({}),Fg=function(e){return e.HORIZONTAL="horizontal",e.VERTICAL="vertical",e}({}),xg=function(e){return e.OBSERVED="observed",e.REQUESTED="requested",e}({});Fg.VERTICAL,Fg.HORIZONTAL;const Gg={[Fg.VERTICAL]:"height",[Fg.HORIZONTAL]:"width"},Bg={[Fg.VERTICAL]:"top",[Fg.HORIZONTAL]:"left"},Hg={[Fg.VERTICAL]:"marginTop",[Fg.HORIZONTAL]:"marginLeft"},jg={[Fg.VERTICAL]:"marginBottom",[Fg.HORIZONTAL]:"marginRight"},$g=50;var Yg="3.76.0";const Wg=e=>{switch(e){case mo.CCPA:return"CCPA";case mo.DEFAULT:return"GDPR";case mo.TCF:return"TCF";default:return""}},zg=({UC:e,children:t})=>{const{core:n,data:r}=Lh(),{uiVariant:i,view:s}=Dg(),o=async t=>{try{var o,a,c,u;const{code:d,regionCode:p}=await e.fetchUserCountry(),f=(l={cmpLayer:s,controllerId:null!==(o=null==r?void 0:r.controllerId)&&void 0!==o?o:"",msg:`EUD ERROR[V2]: ${t.message}`,origin:window.location.origin,settingsId:null!==(a=null==n?void 0:n.id)&&void 0!==a?a:"",settingsVersion:null!==(c=null==n?void 0:n.version)&&void 0!==c?c:"",stackTrace:null!==(u=t.stack)&&void 0!==u?u:"",timestamp:Date.now().toString(),uiVariant:Wg(i),uiVersion:Yg,userAgent:window.navigator.userAgent,userCountry:d,userRegion:p},JSON.stringify(l));throw new Error(f)}catch(e){throw new Error(`Error: ${e}`)}var l},[a]=function(e){var t=oc(za++,10),n=ac();return t.__=e,Ka.componentDidCatch||(Ka.componentDidCatch=function(e,r){t.__&&t.__(e,r),n[1](e)}),[n[0],function(){n[1](void 0)}]}((e=>o(e)));return a?null:ya(ba,null,t)},Kg=["children","id"],Jg=e=>{let{children:t,id:r="focus-lock-id"}=e,i=s(e,Kg);const o=dc(null),a=dc([]),c=['a[href]:not([tabindex="-1"])','button:not([disabled]):not([tabindex="-1"])','input:not([disabled]):not([type="hidden"]):not([tabindex="-1"])','select:not([disabled]):not([tabindex="-1"])','textarea:not([disabled]):not([tabindex="-1"])','[contenteditable]:not([tabindex="-1"])','[tabindex]:not([tabindex="-1"])'].join(", "),u=e=>!(null===e.offsetParent||"hidden"===getComputedStyle(e).visibility);return uc((()=>{const e=()=>{null!=(null==o?void 0:o.current)&&(a.current=Array.from(o.current.querySelectorAll(c)).filter((e=>u(e))))},t=new MutationObserver((()=>{e()}));return e(),null!=(null==o?void 0:o.current)&&t.observe(o.current,{childList:!0}),()=>{t.disconnect()}}),[o.current]),uc((()=>{const e=e=>{const{code:t,shiftKey:n}=e;if("Tab"===t){var r;if(null!=(null==o?void 0:o.current)&&(a.current=Array.from(o.current.querySelectorAll(c)).filter((e=>u(e)))),!a.current)return;const{length:t,0:i,[t-1]:s}=a.current;if(1===t)return void e.preventDefault();const l=null===(r=document.activeElement)||void 0===r||null===(r=r.shadowRoot)||void 0===r?void 0:r.querySelector(":focus-visible");if(void 0===l&&i&&(e.preventDefault(),i.focus()),!n&&l===s&&i)return e.preventDefault(),void i.focus();n&&l===i&&s&&(e.preventDefault(),s.focus())}};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}}),[a]),ya($c,{fallback:ya(ba,null)},ya("div",n({id:r},i,{ref:o}),t))},qg=({children:e})=>{const{setKeyPressed:t}=Dg(),n=hc((e=>{const{code:n}=e;t("Enter"===n||"Escape"===n)}),[t]);return uc((()=>(window.addEventListener("keydown",n),()=>{window.removeEventListener("keydown",n)})),[n]),ya($c,{fallback:ya(ba,null)},ya(ba,null,e))},Xg=Wc((()=>dynamicImportPolyfill("./index-bd2f3674.js",import.meta.url))),Qg=Wc((()=>dynamicImportPolyfill("./PrivacyButton-cdd6962a.js",import.meta.url))),Zg=({initialUIValues:e,restartCMP:t,onThemeChange:n,UC:r,isHtmlDialog:i})=>{const{view:s,clearCategoryPurposeScrollToId:o,clearServiceVendorScrollToId:a,setTabView:c,setView:u,uiVariant:l}=Dg(),{isDesktop:d}=Jf(),{ui:p}=Lh();Vg(r,e,n);const[f]=(e=>{const[t,n]=ac(!1),[r,i]=ac(Pg()),{categories:s,allCategories:o,services:a,allServices:c,setCategories:u}=rh(),{core:l,labels:d,ui:p}=Lh(),{uiVariant:f}=Dg(),h=e.getTCFData(),{setSettingsUI:g,setSettingsLabels:v}=Lh();return uc((()=>{(r.service.length>0||r.tcf.length>0)&&Array.isArray(o)&&!t&&(n(!0),e.loadServices().then((async()=>{const t=await e.getCategoriesFullInfo();u(Oh(t,o)),g(e.getSettingsUI()),v(e.getSettingsLabels())})))}),[o,t,r]),uc((()=>{if(null!=l&&l.isEmbeddingsEnabled&&null!=f&&qf(c)){n(!0);const e={categories:{all:o,visible:s},services:{all:c,visible:a}};if(r.tcf.length>0&&l.isTcfEnabled&&h&&lt(d)&&dynamicImportPolyfill("./tcf-153b2f5b.js",import.meta.url).then((t=>{t.embedTCF(h,e,r.tcf,d)})),d&&r.service.length>0&&Array.isArray(o)&&o.length>0){const t=dt(d)?d.secondLayer.serviceTab:"Services";dynamicImportPolyfill("./gdpr-5103092f.js",import.meta.url).then((n=>{n.embedGDPR(e,d,t,r.service)}))}}}),[l,f,a,s,o,c,p,r]),[async()=>{i(Pg())}]})(r),{acceptAllDefault:h,acceptAllTCF:g,closeCMP:v,denyAllDefault:S,denyAllTCF:E,updateLanguage:_,showSecondLayer:m}=(({initialUIValues:e,onThemeChange:t,restartCMP:n,restartEmbeddings:r,UC:i})=>{const[s,o]=ac(!0),[,a]=ac(!1),{abTestVariant:c,clearServiceVendorScrollToId:u,closeView:l,isAmpEnabled:d,setIsInFullScreen:p,setLanguage:f,setScrollToIdTabViewServiceVendor:h,setTabView:g,setView:v,settingsCloseView:S,uiVariant:E,view:_}=Dg(),{categories:m,getUpdatedCategories:I,services:y,setCategories:C}=rh(),{setTcfData:T}=Ng(),{core:b,ui:A,setSettingsLabels:O,setSettingsUI:N}=Lh(),[w,{loading:R}]=kg(i.dismissAmp,i),[L,{loading:D}]=kg(i.changeLanguage,i),[P,{loading:V}]=kg(i.saveOptOutForCcpa,i),[U,{loading:k}]=kg(i.updateServices,i),[M,{loading:F}]=kg(i.acceptAllServices,i),[x,{loading:G}]=kg(i.acceptAllForTCF,i),[B,{loading:H}]=kg(i.acceptAllAmp,i),[j,{loading:$}]=kg(i.denyAllServices,i),[Y,{loading:W}]=kg(i.denyAllForTCF,i),[z,{loading:K}]=kg(i.denyAllAmp,i),[J]=kg(i.updateChoicesForTCF,i),q=hc((async()=>{!F&&b&&M().then((async()=>{d&&await B(),Rp(_,pp.ACCEPT_ALL,c,i.setTrackingPixel),C(await I(_)),O(i.getSettingsLabels()),N(i.getSettingsUI())}))}),[i,B,M,F,b,d,I,C,_]),X=hc((async()=>{if(F||G||H||!b)return;const e=_===fp.FIRST_LAYER?fe.FIRST_LAYER:fe.SECOND_LAYER;await x(e),await M(),d&&await B(),Rp(_,pp.ACCEPT_ALL,c,i.setTrackingPixel),C(await I(_)),T(i.getTCFData()),O(i.getSettingsLabels()),N(i.getSettingsUI())}),[i,B,H,x,G,M,F,b,d,I,C,T,_]),Q=hc((async(e,t)=>{G||W||!b||J(e,t).then((()=>{Rp(_,pp.SAVE,c,i.setTrackingPixel),T(i.getTCFData())}))}),[i,B,H,x,G,M,F,b,d,I,C,T,_]),Z=hc((async e=>{if(V)return;const t=!e;Rp(_,t?pp.CCPA_TOGGLES_ON:pp.CCPA_TOGGLES_OFF,c,i.setTrackingPixel),a(t),P(t),C(await I(_)),O(i.getSettingsLabels()),N(i.getSettingsUI())}),[P,V]),ee=hc((async(e,t,n=vo.EXPLICIT)=>{if(0===e.length)throw new Error(gp.SERVICE_MISSING_ID);if(k||!b)return;const r=e.reduce(((e,n)=>y.some((e=>e.id===n))?[...e,{serviceId:n,status:t}]:e),[]);U(r,n).then((async()=>{Rp(_,pp.SAVE,c,i.setTrackingPixel),C(await I(_)),O(i.getSettingsLabels()),N(i.getSettingsUI())}))}),[i,b,I,C,U,k,_]),te=hc((()=>{switch(e.variant){case mo.CCPA:return Z(!0);case mo.DEFAULT:return q();case mo.TCF:return X();default:return null}}),[q,X,Z,e.variant]),ne=hc((()=>i.areAllConsentsAccepted()),[i]),re=hc((()=>i.getIsConsentRequired()),[i]),ie=hc((()=>i.clearStorage()),[i]),se=hc((async()=>{R||(!d||_===fp.SECOND_LAYER&&S===fp.FIRST_LAYER||(await w(),p(!1)),_===fp.SECOND_LAYER?await v(S):l())}),[l,w,R,d,p,S,v,_]),oe=hc((async()=>{$||K||!b||j().then((async()=>{d&&await z(),Rp(_,pp.DENY_ALL,c,i.setTrackingPixel),C(await I(_)),O(i.getSettingsLabels()),N(i.getSettingsUI())}))}),[i,b,I,z,K,j,$,d,C,_]),ae=hc((async()=>{if($||W||!b)return;const e=_===fp.FIRST_LAYER?fe.FIRST_LAYER:fe.SECOND_LAYER;await Y(e),await j(),d&&await i.denyAllAmp(),Rp(_,pp.DENY_ALL,c,i.setTrackingPixel),C(await I(_)),T(i.getTCFData()),O(i.getSettingsLabels()),N(i.getSettingsUI())}),[i,Y,W,j,$,I,d,b,C,T,_]),ce=hc((()=>{switch(e.variant){case mo.CCPA:return Z(!1);case mo.DEFAULT:return oe();case mo.TCF:return ae();default:return null}}),[Z,oe,ae,e.variant]),ue=hc((()=>i.enableScriptsForServicesWithConsent()),[i]),le=hc((()=>i.getSettingsCore().language.selected),[i]),de=hc((()=>i.getServicesBaseInfo()),[i]),pe=hc((()=>i.getServicesFullInfo()),[i]),he=hc((()=>i.getSettingsCore()),[i]),ge=hc((()=>i.getSettingsLabels()),[i]),ve=hc((()=>i.getSettingsUI()),[i]),Se=hc((()=>i.getControllerId()),[i]),Ee=hc((()=>{var e;return null===(e=i.getTCFData())||void 0===e?void 0:e.vendors}),[i]),_e=hc((()=>i.getTCFDisclosedVendorsSegmentString()),[i]),me=hc((()=>i.getThirdPartyCount()),[i]),Ie=hc((async e=>!!await i.injectTCString(e)&&(await n(),!0)),[i]),ye=hc((async()=>v(fp.FIRST_LAYER)),[v]),Ce=hc((async e=>{let t=!1;return"string"==typeof e&&(t=["purposes","vendors","services","categories"].includes(e),t&&("purposes"!==e&&"categories"!==e||g(Cp.CATEGORIES_PURPOSES),"vendors"!==e&&"services"!==e||g(Cp.SERVICES_VENDORS))),e&&!t?(g(Cp.SERVICES_VENDORS),setTimeout((()=>{h(e)}))):(t||g(Nh(A,E)),u()),_!==fp.SECOND_LAYER?v(fp.SECOND_LAYER):new Promise((e=>e()))}),[v,g,h,u]),Te=hc((async n=>{b&&Zd.includes(b.language.selected)!==Zd.includes(n.toLowerCase())&&t($f(A,Zd.includes(n.toLowerCase()),e.variant)),D||(await L(n.toLowerCase()),C(Oh(await I(_),m)),O(i.getSettingsLabels()),f(n.toLowerCase()),E===mo.TCF&&T(i.getTCFData()))}),[i,m,L,D,e.variant,I,t,C,f,T,E,_]),be=hc((async e=>{await i.restoreUserSession(e),n()}),[i]);return uc((()=>{window[np]={acceptAllConsents:te,acceptService:(e,t=vo.EXPLICIT)=>ee([e],!0,t),acceptServices:(e,t=vo.EXPLICIT)=>ee(e,!0,t),areAllConsentsAccepted:ne,clearStorage:ie,closeCMP:se,denyAllConsents:ce,denyAndCloseCcpa:async()=>{await Z(!1),se()},enableScriptsForServicesWithConsent:ue,getActiveLanguage:le,getControllerId:Se,getServicesBaseInfo:de,getServicesFullInfo:pe,getSettingsCore:he,getSettingsLabels:ge,getSettingsUI:ve,getTCFDisclosedVendorsSegmentString:_e,getTCFVendors:Ee,getThirdPartyCount:me,injectTCString:Ie,isConsentRequired:re,isInitialized:()=>!0,rejectService:(e,t=vo.EXPLICIT)=>ee([e],!1,t),rejectServices:(e,t=vo.EXPLICIT)=>ee(e,!1,t),restartCMP:n,restartEmbeddings:r,restoreUserSession:be,showFirstLayer:ye,showSecondLayer:e=>Ce(e),updateChoicesForTcf:Q,updateLanguage:Te}}),[_]),uc((()=>{var e;window[np]&&null!==(e=window[np])&&void 0!==e&&e.isInitialized()&&s&&(o(!1),window.dispatchEvent(new window.CustomEvent(hp.INITIALIZED)))}),[window[np],s]),{acceptAllDefault:q,acceptAllTCF:X,closeCMP:se,denyAllDefault:oe,denyAllTCF:ae,restoreUserSession:be,showSecondLayer:e=>Ce(e),updateLanguage:Te}})({initialUIValues:e,onThemeChange:n,restartCMP:t,restartEmbeddings:f,UC:r});if(s===fp.NONE)return null;const I=ya(Xg,{acceptAllDefault:h,acceptAllTCF:g,denyAllDefault:S,denyAllTCF:E,onClose:v,onLanguageChange:_,showSecondLayer:m,UC:r,isHtmlDialog:i});return ya($c,{fallback:ya(ba,null)},ya(zg,{UC:r},ya(qg,null,ya(Qg,{show:s===fp.PRIVACY_BUTTON,iconUrl:St(p)?p.iconUrl:"",onClick:async()=>{c(Nh(p,l)),a(),o(),await u(fp.SECOND_LAYER)},position:St(p)?p.position:"",isMobile:!d}),s===fp.FIRST_LAYER||s===fp.SECOND_LAYER?ya(Jg,{id:"app-focus-lock"},I):I)))},ev=Qd.div(["color:",";direction:",";font-family:",";font-size:",";font-weight:",";line-height:1.15;-webkit-text-size-adjust:100%;-webkit-overflow-scrolling:touch;*,*:after,*:before{box-sizing:border-box;}[hidden]{display:none;}a{font-weight:normal;}button,[type='button'],[type='reset'],[type='submit']{cursor:pointer;font-family:inherit;line-height:inherit;transition:background-color 0.2s ease-in-out;}img{border-style:none;}"],(({theme:e})=>e.typography.color.primary),(({theme:e})=>e.typography.rtlEnabled?"rtl":"unset"),(({theme:e})=>e.typography.font),(({theme:e})=>e.typography.size.root),(({theme:e})=>e.typography.weight.regular));var tv=Vc((({children:e})=>ya(ev,null,e)));let nv=function(e){return e.BANNER_CONTENT=".uc-banner-content",e.BUTTON_ACCEPT=".uc-accept-all-button",e.BUTTON_CUSTOMIZE=".uc-customize-button",e.BUTTON_DENY=".uc-deny-all-button",e.BUTTON_MORE=".uc-more-button",e.BUTTON_SAVE=".uc-save-button",e.BUTTONS=".uc-action-button",e.BUTTONS_CONTAINER=".uc-buttons-container",e.BUTTONS_CONTAINER_WRAPPER=".uc-buttons-container-wrapper",e.CARD_TITLE=".uc-card-title",e.CARD_DESCRIPTION=".uc-card-description",e.LANGUAGE_SWITCH=".uc-language-switch",e.LAYER=".uc-layer",e.LINKS=".uc-links",e.LOGO=".uc-logo",e.MESSAGE=".uc-message",e.PRIVACY_BUTTON=".uc-privacy-button",e.TAB_BUTTON=".uc-tab-button",e.TAB_BUTTON_ACTIVE=".uc-tab-button-active",e.TAB_CONTENT=".uc-tab-content",e.THIRD_COUNTRY_FILTER=".uc-third-country-data-transfer-filter",e.TITLE=".uc-title",e.TITLE_CONTAINER=".uc-title-container",e.CONTROLLER_ID_CARD=".uc-controller-id",e.MAX_CMP_STORAGE_DURATION=".uc-cmp-mac-storage-duration-id",e.TAB_BUTTONS_CONTAINER=".uc-tab-buttons-container",e.SECOND_LAYER=".uc-second-layer",e.POWERED_BY=".uc-powered-by",e.FOOTER_WRAPPER=".uc-footer-wrapper",e.BANNER_FOOTER_CONTENT=".uc-footer-content",e}({}),rv=function(e){return e.DEFAULT="default",e.DESKTOP="desktop",e.IE="ie",e.LANDSCAPE="landscape",e.PORTRAIT="portrait",e.MOBILE="mobile",e.MOBILE_S="mobileSmall",e.MOBILE_XS="mobileExtraSmall",e.HOVER="hover",e}({});const iv={directives:["border","height","margin","max-width","padding","width","max-height"],selector:nv.BANNER_CONTENT},sv={directives:["border","font","height","margin","padding","width","flex","display","background","color","border"],selector:nv.BUTTONS},ov={directives:["flex","width","justify-content","align-items","padding","column-gap","row-gap","margin"],selector:nv.BUTTONS_CONTAINER},av={directives:["order","flex","height","margin","padding","width","border","display","background","color","border",":hover"],selector:nv.BUTTON_ACCEPT},cv={directives:["order"],selector:nv.BUTTON_CUSTOMIZE},uv={directives:["order","flex","height","margin","padding","width","border","display","background","color","border"],selector:nv.BUTTON_DENY},lv={directives:["order","flex","height","margin","padding","width","border","display","background","color","border"],selector:nv.BUTTON_MORE},dv={directives:["order","flex","height","margin","padding","width","border","display","background","color","border"],selector:nv.BUTTON_SAVE},pv={directives:["font-size","font-weight","line-height"],selector:nv.CARD_DESCRIPTION},fv={directives:["font-size","font-weight","line-height"],selector:nv.CARD_TITLE},hv={directives:["background","color","font-size"],selector:nv.LANGUAGE_SWITCH},gv={directives:["display","border","height","margin","max-width","padding","width","min-height","flex-direction","box-shadow"],selector:nv.LAYER},vv={directives:["display","border","height","margin","max-width","padding","width","min-height","flex-direction","box-shadow"],selector:nv.SECOND_LAYER},Sv={directives:["display","border","height","margin","max-width","padding","width","min-height","flex-direction","text-decoration"],selector:nv.POWERED_BY},Ev={directives:["font","line-height","text"],selector:nv.LINKS},_v={directives:["height","width","max-width","max-height"],selector:nv.LOGO},mv={directives:["color","font","text"],selector:nv.MESSAGE},Iv={directives:["background","font","height","line-height","margin","padding","text","width"],selector:nv.TAB_BUTTON},yv={directives:["background","font","height","line-height","margin","padding","text","width","border-bottom"],selector:nv.TAB_BUTTON_ACTIVE},Cv={directives:["display","width","justify-content","align-items","margin","padding"],selector:nv.TAB_BUTTONS_CONTAINER},Tv={directives:["background","color","margin","padding"],selector:nv.TAB_CONTENT},bv={directives:["color","font","line-height","text"],selector:nv.TITLE},Av={directives:["width"],selector:nv.TITLE_CONTAINER},Ov=[iv,sv,ov,av,cv,uv,lv,dv,pv,fv,hv,gv,Ev,_v,mv,{directives:["bottom","left","right","width","height"],selector:nv.PRIVACY_BUTTON},Iv,yv,Tv,bv,Av,{directives:["color","background-color","border-color"],selector:nv.CONTROLLER_ID_CARD},{directives:["color","border-color","border-radius","background-color"],selector:nv.THIRD_COUNTRY_FILTER},Cv,vv,Sv,{directives:["flex","width","justify-content","align-items","padding","column-gap","row-gap","margin"],selector:nv.BUTTONS_CONTAINER_WRAPPER},{directives:["border","padding","flex"],selector:nv.FOOTER_WRAPPER},{directives:["flex","padding","margin","border","justify-content","align-items","width","height","display"],selector:nv.BANNER_FOOTER_CONTENT}],Nv=(e,{directives:t},n=!1)=>{const r=/\s*([a-z-]+)\s*:\s*((?:[^;]*url\(.*?\)[^;]*|[^;]*)*)\s*(?:;|$)/gi;let s,o;for(;null!==(s=r.exec(e));){const e=s[1];if("string"!=typeof e)break;t.some((t=>0===e.indexOf(t)))&&(o=i(i({},o),{},{[e.toLowerCase()]:`${s[2]} ${n?"!important":""}`}))}return o},wv=e=>{const t=Ov.find((t=>e.split("__")[0]===t.selector));return t?-1!==e.indexOf(rv.DESKTOP)?{allowedCss:t,media:rv.DESKTOP}:-1!==e.indexOf(rv.IE)?{allowedCss:t,media:rv.IE}:-1!==e.indexOf(rv.LANDSCAPE)?{allowedCss:t,media:rv.LANDSCAPE}:-1!==e.indexOf(rv.MOBILE_S)?{allowedCss:t,media:rv.MOBILE_S}:-1!==e.indexOf(rv.MOBILE_XS)?{allowedCss:t,media:rv.MOBILE_XS}:-1!==e.indexOf(rv.MOBILE)?{allowedCss:t,media:rv.MOBILE}:-1!==e.indexOf(rv.PORTRAIT)?{allowedCss:t,media:rv.PORTRAIT}:-1!==e.indexOf(rv.HOVER)?{allowedCss:t,media:rv.HOVER}:{allowedCss:t,media:rv.DEFAULT}:null},Rv=e=>e.trim().replace(/\s*,\s*/,", "),Lv=e=>{const t=/([\s\S]+?)\{([\s\S]*?)\}/gm,n=e.replace(/\/\*[\s\S]*?\*\/|([^\\:]|^)\/\/.*$/gm,"").replace(/\/\*[\s\S]*?\*\//g,"");let r,s=[];for(;null!==(r=t.exec(n));){const e=Rv(r[1]),t=wv(e),n=t&&Nv(r[2].trim(),t.allowedCss,t.media===rv.HOVER);if(n&&null!=t&&t.allowedCss){const e=s.find((e=>e.selector===t.allowedCss.selector));s=e?s.map((e=>e.selector===t.allowedCss.selector?i(i({},e),{},{[t.media]:n}):e)):[...s,{[t.media]:n,selector:t.allowedCss.selector}]}}return s},Dv=Wa({css:[]});Dv.displayName="CustomCssContext";const Pv=Vc((({children:e,UC:t})=>{const n=t.getSettingsUI(),[r,i]=ac(n&&n.customCss?Lv(n.customCss):[]);return uc((()=>{i(n&&n.customCss?Lv(n.customCss):[])}),[n]),ya(Dv.Provider,{value:{css:r}},e)})),Vv=(e,t=!1)=>{const{css:n}=gc(Dv),r=Jf(),s=e?((e,t,{isDesktop:n,isIE:r,isLandscape:s,isPortrait:o,isMobileMd:a,isMobileExtraSmall:c,isMobileSmall:u},l)=>{const d=e.find((e=>e.selector===t));let p=null;const f=e=>{e&&(p=i(p?i({},p):{},e))};return d&&l?f(d.hover):d&&(f(d.default),n&&f(d.desktop),u&&f(d.mobileSmall),c&&f(d.mobileExtraSmall),a&&f(d.mobile),s&&!n&&f(d.landscape),o&&!n&&f(d.portrait),r&&f(d.ie)),p})(n,e,r,t):null;return{rules:s}},Uv=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/\s+/g,"-").toLowerCase(),kv={blockConsumeFocus:!1,blockFocus:!1,consumeNextFocus:()=>null,langSelectorOpen:!1,nextFocus:null,setBlockConsumeFocus:()=>null,setBlockFocus:()=>null,setLangSelectorStatus:()=>null,setNextFocus:()=>null},Mv=Wa(kv);Mv.displayName="GlobalStateContext";const Fv=Vc((({children:e})=>{const{view:t}=Dg(),[n,r]=ac(kv.blockFocus),[i,s]=ac(kv.blockConsumeFocus),[o,a]=ac(kv.nextFocus),[c,u]=ac(kv.langSelectorOpen),[l,d]=ac(kv.nextFocus),p=e=>{r(e)};uc((()=>{null===o&&p(!1)}),[o]);return ya(Mv.Provider,{value:{blockConsumeFocus:i,blockFocus:n,consumeNextFocus:()=>{var e;if("string"==typeof o)try{var t;const e=document.getElementById(ep),n=(null==e||null===(t=e.shadowRoot)||void 0===t?void 0:t.querySelector(`[data-testid=${Uv(o)}]`))||null;null!==n&&n.focus()}catch(e){}else null==o||null===(e=o.current)||void 0===e||e.focus(),a(null!==l?l:null);s(!1)},langSelectorOpen:c,nextFocus:o,setBlockConsumeFocus:e=>{s(e)},setBlockFocus:p,setLangSelectorStatus:e=>{u(e)},setNextFocus:e=>{null!==o&&t===fp.SECOND_LAYER?d(o):d(null),a(e)}}},e)})),xv=()=>gc(Mv);ra.debounceRendering=setTimeout;if(window.UC_UI_IS_RENDERED||!1)throw new Error(gp.DUPLICATED_SCRIPT);window.UC_UI_IS_RENDERED=!0;let Gv=!1,Bv=!1;async function Hv(e){let t;Bv=e.options.dialogMode||!1,t=e.options.rulesetId?new ga(e.options.rulesetId,i(i({},e.options),{},{useRulesetId:!0})):new ga(e.settingsId,i({},e.options));return{initialUIValues:await t.init(),usercentrics:t}}function jv(){let e=document.getElementById(ep);var t;e&&(null===(t=e.parentNode)||void 0===t||t.removeChild(e));e=document.createElement("div"),e.style.display="none",e.id=ep,e.setAttribute("data-created-at",Date.now().toString());const n=document.createElement("div");if(n.setAttribute("data-nosnippet","1"),n.setAttribute("data-testid","uc-app-container"),"function"==typeof e.attachShadow||document.head.attachShadow||HTMLElement.prototype.attachShadow){e.attachShadow({mode:"open"}).appendChild(n)}else e.appendChild(n);return document.body.appendChild(e),Gv=!0,{appContainer:n,rootContainer:e}}async function $v(){return new Promise((e=>{"loading"!==document.readyState?((()=>{let e=null;try{e=localStorage.getItem("uc_ui_version"),e||console.info(gp.USE_LOADER)}catch(e){console.warn(gp.INACCESSIBLE_LOCAL_STORAGE)}})(),e(jv())):document.addEventListener("readystatechange",(()=>{Gv||e(jv())}))}))}const Yv=({initialUIValues:e,restartCMP:t,target:n,usercentrics:r})=>{const i=r.getSettingsCore(),s=r.getSettingsUI(),[o,a]=ac(s&&i?$f(s,Zd.includes(i.language.selected),e.variant):zf);return uc((()=>{setTimeout((()=>{const{rootContainer:e}=n;e&&"none"===e.style.display&&e.style.removeProperty("display")}),200)}),[n]),Wd.displayName="ThemeProvider",ya(bd,{target:n.appContainer,disableCSSOMInjection:!0},ya(Wd,{theme:o},ya(tv,null,ya(Pv,{UC:r},ya(Rh,{UC:r},ya(nh,{UC:r},ya(Og,null,ya(Lg,{UC:r,initialUIValues:e},ya(Fv,null,ya(Zg,{onThemeChange:a,initialUIValues:e,restartCMP:t,UC:r,isHtmlDialog:Bv}))))))))))};void 0!==e&&e.initialize({importFunctionName:"dynamicImportPolyfill",modulePath:"/dir"}),async function e(){const t=(()=>{const e=document.getElementById("usercentrics-cmp"),t=e&&e.dataset,n=t?tp.AMP_ENABLED in e.dataset:window[tp.AMP_ENABLED];let r=t?e.dataset[tp.SETTINGS_ID]:window[tp.SETTINGS_ID];const s=t?e.dataset[tp.RULESET_ID]:window[tp.RULESET_ID];if(!window.__webpack_nonce__&&e&&e.nonce&&(window.__webpack_nonce__=e.nonce),n){const e=Tp();r=(null==e?void 0:e.id)||r}let o=null;try{o=t&&e.dataset[tp.CONTROLLER_IDS]&&JSON.parse(e.dataset[tp.CONTROLLER_IDS]||"[]")}catch(e){}const a=window.UC_UI_EXCLUDE_ACCEPT_ALL_VENDORS||[],c=a.length>0&&Array.isArray(a)&&a.every((e=>"number"==typeof e));return{options:i(i({ampEnabled:n,blockDataLayerPush:t&&tp.BLOCK_DATA_LAYER_PUSH in e.dataset,controllerId:t&&e.dataset[tp.CONTROLLER_ID]},o&&{controllerIds:o}),{},{createGppStub:t?tp.GPP_ENABLED in e.dataset:window[tp.GPP_ENABLED],createTcfApiStub:t?tp.TCF_ENABLED in e.dataset:window[tp.TCF_ENABLED],dialogMode:!(!t||!(tp.DIALOG_MODE in e.dataset)),disableServerConsents:t&&tp.DISABLE_SERVER_CONSENTS in e.dataset,disableTracking:t&&tp.DISABLE_TRACKING in e.dataset,disableUet:t&&tp.DISABLE_UET in e.dataset,enableDeprecatedV1ConsentSaving:t&&tp.DEPRECATED_V1_CONSENT_SAVING_ENABLED in e.dataset,euMode:t&&tp.EU_MODE in e.dataset&&"false"!==e.dataset[tp.EU_MODE],excludeAcceptAllVendors:c?a:[],language:t?e.dataset[tp.LANGUAGE]:window[tp.LANGUAGE],prefetchServices:!t||!(tp.AVOID_PREFETCH_SERVICES in e.dataset),rulesetId:s,sandboxEnv:t&&tp.SANDBOX_ENV in e.dataset,settingsCache:t&&e.dataset[tp.SETTINGS_CACHE],storeServiceIdToNameMapping:t&&tp.STORE_SERVICE_ID_TO_NAME_MAPPING in e.dataset,suppressCmpDisplay:t&&tp.SUPPRESS_CMP_DISPLAY in e.dataset||!0===window.UC_UI_SUPPRESS_CMP_DISPLAY,userCountryData:window.UC_UI_USER_COUNTRY_DATA||null,userSessionData:window.UC_UI_USER_SESSION_DATA||null,useRulesetId:!!s,version:t&&e.dataset[tp.VERSION]}),settingsId:r}})();let n,r;try{[n,r]=await Promise.all([Hv(t),$v()])}catch(e){if(e.showErrorCmp){const{appContainer:e,rootContainer:t}=jv();t.style.removeProperty("display");ja(ya($c,{fallback:null},ya(Wc((()=>dynamicImportPolyfill("./ErrorCmp-149bf63d.js",import.meta.url))),null)),e)}else console.error(e);return}let s="";document.body&&document.body.classList.contains("overflowHidden")&&(s="overflowHidden"),n.initialUIValues.ampEnabled&&await n.usercentrics.uiReadyAmp().catch((e=>console.warn(`AMP is not initialized: ${e}`))),ja(ya(Yv,{initialUIValues:n.initialUIValues,restartCMP:async()=>{delete window[np],""!==s&&document.body.classList.add(s),await e()},target:r,usercentrics:n.usercentrics}),r.appContainer)}();export{Bo as $,R as A,Do as B,mi as C,J as D,T as E,Lo as F,No as G,ko as H,Eo as I,$o as J,W as K,Me as L,Uo as M,De as N,bo as O,Q as P,qt as Q,ep as R,ye as S,_i as T,Vn as U,Ut as V,Mo as W,ps as X,jn as Y,Go as Z,K as _,Ci as a,Sg as a$,Ho as a0,xo as a1,os as a2,ss as a3,le as a4,gn as a5,fn as a6,Sn as a7,_n as a8,vn as a9,et as aA,Vc as aB,Wc as aC,Lh as aD,Dg as aE,kg as aF,mo as aG,$c as aH,fp as aI,Rp as aJ,pp as aK,Nh as aL,Cp as aM,Qd as aN,Df as aO,Xd as aP,Vv as aQ,nv as aR,pt as aS,i as aT,ng as aU,Yh as aV,rg as aW,sg as aX,Bh as aY,Gh as aZ,$h as a_,hn as aa,En as ab,mn as ac,Ro as ad,Co as ae,jo as af,u as ag,$t as ah,nn as ai,To as aj,be as ak,Ao as al,Oo as am,Ne as an,Po as ao,wo as ap,Ue as aq,Xe as ar,qe as as,$ as at,tt as au,nt as av,ot as aw,j as ax,B as ay,Ze as az,Se as b,Jg as b$,eg as b0,fg as b1,np as b2,bp as b3,gg as b4,lg as b5,ug as b6,dg as b7,hg as b8,Ap as b9,Ta as bA,qf as bB,Jf as bC,He as bD,yp as bE,Th as bF,Ah as bG,fe as bH,dt as bI,vt as bJ,$g as bK,lc as bL,Pf as bM,xf as bN,vp as bO,Zh as bP,Ud as bQ,s as bR,H as bS,F as bT,Ya as bU,Gp as bV,cp as bW,gu as bX,xv as bY,lp as bZ,Xc as b_,Eg as ba,Cg as bb,Tg as bc,pg as bd,Ih as be,yh as bf,og as bg,tg as bh,Lp as bi,jh as bj,ih as bk,uh as bl,Hh as bm,cg as bn,lt as bo,hc as bp,fc as bq,ba as br,dc as bs,gt as bt,bh as bu,Uv as bv,Mc as bw,Ng as bx,rh as by,n as bz,Ae as c,Uh as c0,St as c1,up as c2,xp as c3,Dh as c4,Vh as c5,qd as c6,Ph as c7,ht as c8,vo as c9,Pa as ca,sa as cb,ap as cc,Zd as cd,ip as ce,op as cf,sp as cg,Mp as ch,kp as ci,Wa as cj,gc as ck,rp as cl,Sp as cm,t as cn,Mg as co,Fg as cp,Aa as cq,xg as cr,Gg as cs,Hg as ct,jg as cu,Bg as cv,ut as cw,Op as cx,ft as cy,X as d,se as e,ke as f,ya as g,ac as h,ce as i,On as j,l as k,S as l,bn as m,ae as n,p as o,fo as p,C as q,ue as r,ho as s,oe as t,Io as u,Vo as v,q as w,Bt as x,uc as y,Xt as z};
