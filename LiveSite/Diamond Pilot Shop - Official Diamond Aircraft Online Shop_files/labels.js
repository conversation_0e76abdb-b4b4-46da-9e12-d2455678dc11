/*!
 * jQuery UI Labels 1.13.2
 * http://jqueryui.com
 *
 * Copyright jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 */

//>>label: labels
//>>group: Core
//>>description: Find all the labels associated with a given input
//>>docs: http://api.jqueryui.com/labels/

( function( factory ) {
    "use strict";

    if ( typeof define === "function" && define.amd ) {

        // AMD. Register as an anonymous module.
        define( [ "jquery", "./version" ], factory );
    } else {

        // Browser globals
        factory( jQuery );
    }
} )( function( $ ) {
    "use strict";

    return $.fn.labels = function() {
        var ancestor, selector, id, labels, ancestors;

        if ( !this.length ) {
            return this.pushStack( [] );
        }

        // Check control.labels first
        if ( this[ 0 ].labels && this[ 0 ].labels.length ) {
            return this.pushStack( this[ 0 ].labels );
        }

        // Support: IE <= 11, FF <= 37, Android <= 2.3 only
        // Above browsers do not support control.labels. Everything below is to support them
        // as well as document fragments. control.labels does not work on document fragments
        labels = this.eq( 0 ).parents( "label" );

        // Look for the label based on the id
        id = this.attr( "id" );
        if ( id ) {

            // We don't search against the document in case the element
            // is disconnected from the DOM
            ancestor = this.eq( 0 ).parents().last();

            // Get a full set of top level ancestors
            ancestors = ancestor.add( ancestor.length ? ancestor.siblings() : this.siblings() );

            // Create a selector for the label based on the id
            selector = "label[for='" + $.escapeSelector( id ) + "']";

            labels = labels.add( ancestors.find( selector ).addBack( selector ) );

        }

        // Return whatever we have found for labels
        return this.pushStack( labels );
    };

} );
