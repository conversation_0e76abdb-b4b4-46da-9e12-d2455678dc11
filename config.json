{"client": "Diamondair", "themeName": "blank", "src": {"root": "app/design/frontend/Diamondair/blank/web", "sassFolder": false, "lessFolder": "app/design/frontend/Diamondair/blank/web/css", "sass": false, "less": ["app/design/frontend/Diamondair/blank/web/css/styles.less", "app/design/frontend/Diamondair/blank/web/css/heidelpay.less", "pub/static/frontend/Diamondair/blank/de_DE/css/styles-m.less", "pub/static/frontend/Diamondair/blank/de_DE/css/styles-l.less"], "includePaths": [], "js": "app/design/frontend/Diamondair/blank/web/Resources/JavaScript/", "chunks": {"main": "app/design/frontend/Diamondair/blank/web/Resources/JavaScript/main.js"}, "img": "app/design/frontend/Diamondair/blank/web/Resources/Images", "externalLibrary": false, "externals": {"window": "window"}, "providePlugin": false, "providePlugins": {"$": "j<PERSON>y", "jQuery": "j<PERSON>y", "window.jQuery": "j<PERSON>y"}, "sprite": [{"name": "sprites", "input": "sprites", "outputImg": "sprite", "outputScss": false, "outputLess": "/modules"}], "handlebars": {}}, "dist": {"root": "app/design/frontend/Diamondair/blank", "css": "app/design/frontend/Diamondair/blank/web/css", "js": "app/design/frontend/Diamondair/blank/web/js", "publicPath": "/web/js/", "img": "app/design/frontend/Diamondair/blank/web/images", "sprite": [{"output": "sprite"}]}, "clean": {"css": ["app/design/frontend/Diamondair/blank/web/css/**/*.css", "!app/design/frontend/Diamondair/blank/web/css", "!app/design/frontend/Diamondair/blank/web/css/.gitignore", "pub/static/frontend/Diamondair/blank/de_DE/css/*.css", "!pub/static/frontend/Diamondair/blank/de_DE/css", "var/view_preprocessed/pub/static/frontend/Diamondair/blank/de_DE/css/*.css", "!var/view_preprocessed/pub/static/frontend/Diamondair/blank/de_DE/css"], "js": ["**/*", "!.giti<PERSON>re"], "img": ["app/design/frontend/Diamondair/blank/web/images/**", "!app/design/frontend/Diamondair/blank/web/images", "!app/design/frontend/Diamondair/blank/web/images/.gitignore"]}, "linter": {"sass": false, "less": ["app/design/frontend/Diamondair/blank/web/Resources/Css/**/*.less"], "css": ["app/design/frontend/Diamondair/blank/**/*.css"]}, "watch": {"sass": false, "less": ["app/design/frontend/Diamondair/blank/**/*.less", "app/code/**/*.less"], "js": ["app/design/frontend/Diamondair/blank/web/Resources/JavaScript/**/*.js"], "html": ["app/design/frontend/Diamondair/blank/**/*.html"], "hbspages": [""], "php": ["app/design/frontend/Diamondair/blank/web/Resources/**/*.php"], "singleSass": false, "singleLess": "styles.less", "css": ["app/design/frontend/Diamondair/blank/web/css/main.css"]}, "jest": ["app/design/frontend/Diamondair/blank/web/Resources", "frontend/test"], "purge": {"purgeList": ["frontend/purgeHtml/*.html", "app/design/frontend/Diamondair/blank/web/Resources/**/*.phtml", "app/design/frontend/Diamondair/blank/web/Resources/**/*.xml", "app/design/frontend/Diamondair/blank/web/Resources/JavaScript/**/*.js"], "purgeWhiteList": ["random"], "purgePattern": ["js--", "ls--", "is--"]}, "usepolling": true, "publicJavascript": "/web/js/", "disableJavascriptCompiling": false, "includePath": false, "enablePurgecss": false, "enable": {"sasshash": false, "vue": false, "react": true, "watch": {"css": true, "js": false, "html": false, "php": false}, "clean": {"images": true, "css": true}, "webpack": {"html": false, "chunkhash": false, "cssExtract": true}, "server": {"historyApi": false}}}