{"name": "Diamondair", "version": "1.0.0", "engines": {"node": ">=12"}, "description": "Project Diamondair", "jest": {"verbose": true}, "scripts": {"sprites": "frontend run sprites --", "sasslint": "frontend run stylelint -- --scss", "stylelint": "frontend run stylelint --", "dev:css": "frontend run sass --", "build:css": "cross-env NODE_ENV=production frontend run sass --", "dev:js": "cross-env NODE_ENV=development frontend run webpack -- --serve", "build:js": "cross-env NODE_ENV=production frontend run webpack --", "images": "frontend run images --", "handlebars": "frontend run handlebars --", "serve": "cross-env NODE_ENV=development frontend run index -- --serve", "serve:reload": "cross-env NODE_ENV=development frontend run index -- --serve --reload", "serve:build": "cross-env NODE_ENV=production frontend run index -- --serve --reload", "watch": "cross-env NODE_ENV=development frontend run index -- --watch", "http:server": "http-server  -d false -p 7777 --proxy http://na.kartenstelle-oegb-at.junge.vm-na.limesoda.com  --ext php -c-1", "watch:server": "concurrently --kill-others \"yarn run watch\" \"yarn run http-server  -d false -p 7777 --proxy http://na.kartenstelle-oegb-at.junge.vm-na.limesoda.com --ext php -c-1\"", "build:dev": "cross-env NODE_ENV=development frontend run index -- --dev", "build": "cross-env NODE_ENV=production frontend run index --", "test": "frontend run jest -- --verbose", "test:coverage": "frontend run jest -- --coverage --verbose", "test:watch": "frontend run jest -- --verbose --watchAll", "build:project": "frontend run inheritance --", "watch:all": "yarn run images && yarn run watch", "serve:all": "yarn run images && yarn run serve", "build:all": "yarn run images && yarn run build", "deploy": "frontend run deploy --", "git:precommit": "lint-staged"}, "author": "<PERSON><PERSON>", "license": "ISC", "browserslist": ["Chrome >= 72", "ChromeAndroid >= 72", "Firefox >= 65", "iOS >=12", "IE >= 11"], "lint-staged": {"app/design/frontend/Diamondair/blank/web/Resources/**/*.js": ["./node_modules/.bin/eslint  --fix", "yarn run test"], "app/design/frontend/Diamondair/blank/web/Resources/**/*.{scss,less,sass}": ["./node_modules/.bin/stylelint  --fix"]}, "husky": {"hooks": {"pre-commit": "yarn git:precommit"}}, "dependencies": {"axios": "^0.21.1", "es6-promise": "^4.2.8", "frontend": "git+ssh://***********************/limesoda/frontend/workflow.git#2.5.19", "lazysizes": "^5.1.0", "tiny-slider": "^2.9.2"}}