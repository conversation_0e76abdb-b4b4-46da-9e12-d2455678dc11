version: "2.4"

volumes:
    composer-cache:
        external: true

services:
    app:
        image: copex/nginx-php-fpm:dev
#        ports:
            #            - "80:80"
            #            - "443:443"
        extra_hosts:
            - "host.docker.internal:host-gateway"
        environment:
            DOMAIN: ${DEV_DOMAIN}
            MAGENTO_ROOT: ${DEV_MAGENTO_ROOT}
            XDEBUG_MODE: debug
            PHP_IDE_CONFIG: "serverName=${DEV_DOMAIN}"
            SSL_ON: 1
        volumes:
            - ../:${DEV_MAGENTO_ROOT}
            - composer-cache:/var/www/.composer/cache
    mysql:
        environment:
            MYSQL_ROOT_PASSWORD: "r00t"
            MYSQL_USER: "magento"
            MYSQL_PASSWORD: "magento"
            MYSQL_DATABASE: "magento"
        ports:
            - "3306:3306"
        command:
            - mysqld
            - --explicit_defaults_for_timestamp=on
            - --innodb_flush_log_at_trx_commit=2
    cron:
        image: tianon/true
        ports:
            - "8282:8282"
        restart: "no"
        environment:
            MAGENTO_ROOT: ${DEV_MAGENTO_ROOT}
        volumes:
            - ../:${DEV_MAGENTO_ROOT}
    search:
        ports:
            - "9200:9200"
            - "9300:9300"
        environment:
            - http.cors.enabled=true
            - http.cors.allow-origin=/.*/
    elastic:
        image: cars10/elasticvue
        networks:
            - backend
        ports:
            - 5000:8080
        environment:
            HQ_DEFAULT_URL: 'http://localhost:9200'
    mailcatcher:
        image: sj26/mailcatcher
        ports:
            - "1080:1080"
        networks:
            - backend
