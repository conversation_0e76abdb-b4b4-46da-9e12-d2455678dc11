<?php

namespace Deployer;

use Magento\Framework\App\DeploymentConfig\Writer\PhpFormatter;

require 'recipe/common.php';
require 'recipe/magento2.php';
require 'src/vendor/limesoda/deployer_recipe_shared_database_m2/recipe/shared_database.php';
require 'src/vendor/limesoda/deployer_recipe_deploy_m2/recipe/magento2.php';
require 'src/vendor/limesoda/deployer_recipe_shared_files_backup_m2/recipe/shared_files.php';

// [Optional] Allocate tty for git clone. Default value is false.
set('git_tty', true);

// Shared files/dirs between deploys
// Will symlink {{deployment_dir}}/{{shared_dir}} to shared/{{shared_dir}}
// Same thing applies to files
set('shared_files', [
    'src/app/etc/env.php',
    'src/pub/robots.txt',
    'src/pub/.htaccess'
]);
set('shared_dirs', [
    'src/pub/media',
    'src/pub/sitemaps',
    'src/var',
]);

// Writable dirs by web server
// depends on the target OS if this command works
set('writable_dirs', [
    'src/var',
    'src/pub/media',
    'src/pub/sitemaps',
]);

// import host definition and set some global properties
inventory('deploy_hosts.yml')
    ->identityFile('~/.ssh/id_rsa')
    ->forwardAgent(true)
    ->multiplexing(true)
    ->addSshOption('UserKnownHostsFile', '/dev/null')
    ->addSshOption('StrictHostKeyChecking', 'no');

before('ls:ci_deploy_static_content', 'ls:frontend_workflow');

task('ls:frontend_workflow', function () {
    runLocally('cd {{magento_root_dir}} && yarn install --frozen-lockfile', ['timeout' => 3000000]);
    runLocally('cd {{magento_root_dir}} && yarn images', ['timeout' => 3000000]);
    runLocally('cd {{magento_root_dir}} && yarn build:js', ['timeout' => 3000000]);
})->desc('Frontend Workflow.');


before('ls:ci_di_compile', 'ls:ci_create_env_file');

task('ls:ci_create_env_file', function () {
    runLocally('rm ./src/app/etc/env.php');

    $formatter = new PhpFormatter();
    $content =  $formatter->format([
        'MAGE_MODE' => 'production'
    ]);

    runLocally('echo "'.$content.'" > ./src/app/etc/env.php');
});

// Disable all ci database tasks
task('ls:ci_configure_database', function () {
    echo('Skip ci database tasks');
});

task('ls:ci_drop_database', function () {
    echo('Skip ci database tasks');
});

task('ls:ci_create_database', function () {
    echo('Skip ci database tasks');
});

task('ls:ci_import_database', function () {
    echo('Skip ci database tasks');
});

task('ls:ci_upgrade_database', function () {
    echo('Skip ci database tasks');
});

task('ls:ci_apply_magento_config_settings', function () {
    echo('Skip - everything is set in app/etc/config.php');
});

task('ls:ci_cache_flush', function () {
    echo('Skip during ci process');
});

task('ls:ci_cache_clean', function () {
    echo('Skip during ci process');
});

task('ls:apply_magento_config_settings', function () {
    echo('Skip - everything is set in app/etc/config.php');
});

task('deploy:writable', function () {
    echo('Skip temp');
});
