version: "2.4"

networks:
    backend:
        external: false

services:
    app:
        environment:
            DOMAIN: ${DOMAIN}
        volumes:
            - ${DEPLOYMENT_ROOT}:${DEPLOYMENT_ROOT}
            - /var/backups:/backups
            - ${DEPLOYMENT_ROOT}/config/nginx/.htpasswd:/etc/nginx/.htpasswd
        labels:
            - "traefik.enable=true"
            - "traefik.http.routers.${PROJECT}.entrypoints=websecure"
            - "traefik.http.routers.${PROJECT}.rule=Host(`${TRAEFIK_DOMAIN}`)"
            - "traefik.http.routers.${PROJECT}.tls=true"
            - "traefik.http.routers.${PROJECT}.tls.certresolver=letsencrypt"
            - "traefik.http.routers.${PROJECT}.middlewares=${PROJECT}_auth"
            - "traefik.http.middlewares.${PROJECT}_auth.basicauth.users=copex:$$2y$$05$$pW5Np1LlOGEskvKNqrRNluArXPjeNMu93VWjvN9VvKzVB5fYeXFCe"

    mysql:
        volumes:
            - /var/backups:/backups
        ports:
            - "8306:3306"
        command: "--transaction-isolation=READ-COMMITTED
            --innodb_buffer_pool_size=${MYSQL_INNODB_MEMORY}
            --innodb-flush-log-at-trx-commit=2
            --innodb-autoextend-increment=512
            --innodb-log-file-size=256M
            --innodb-log-buffer-size=32M
            --thread-cache-size=32
            --table-cache=1024
            --join-buffer-size=8M
            --tmp-table-size=512M
            --key-buffer-size=64M
            --max-allowed-packet=2048M
            --max-heap-table-size=512M
            --read-buffer-size=4M
            --read-rnd-buffer-size=16M
            --bulk-insert-buffer-size=64M
            --sort-buffer-size=4M
            --max-connections=300
            --net-buffer-length=32704
            --table-definition-cache=4096"

    cron:
        volumes:
            - ${DEPLOYMENT_ROOT}:${DEPLOYMENT_ROOT}
    logrotate:
        image: blacklabelops/logrotate
        restart: unless-stopped
        volumes:
            - ${DEPLOYMENT_ROOT}:${DEPLOYMENT_ROOT}
        environment:
            LOG_DIRECTORIES: ${DEPLOYMENT_ROOT}/shared/var/log
            LOGROTATE_MODE: "nocreate"
    redis:
       command: redis-server --maxmemory ${REDIS_PRODUCTION_MEMORY} --maxmemory-policy volatile-lru --save "3600 1"
