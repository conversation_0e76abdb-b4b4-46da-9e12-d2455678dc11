<?php

namespace Deployer;

set('clear_misc_paths', [
    "Capfile",
    "README.md",
    ".gitlab-ci.yml",
    "deploy.php",
    "composer.phar",
    ".htaccess.sample",
    ".gitignore",
    ".php_cs",
    ".travis.yml",
    ".user.ini",
    "auth.json",
    "CONTRIBUTING.md",
    "COPYING.txt",
    "*.sample",
    "ISSUE_TEMPLATE.md",
    "LICENSE.txt",
    "LICENSE_AFL.txt",
    "php.ini",
    "config/*",
    "docker-compose*",
    ".env",
]);
task('clear_misc', function () {
    $paths = get('clear_misc_paths');
    $sudo = get('clear_use_sudo') ? 'sudo' : '';
    $batch = 100;

    $commands = [];
    foreach ($paths as $path) {
        $commands[] = "$sudo rm -rf {{release_path}}/$path";
    }
    $chunks = array_chunk($commands, $batch);
    foreach ($chunks as $chunk) {
        run(implode('; ', $chunk));
    }
});
after('deploy', 'clear_misc');
after('deploy:failed', 'deploy:unlock');