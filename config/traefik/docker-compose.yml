version: '3'

services:
  traefik:
    image: traefik:v2.9
    restart: unless-stopped
    container_name: traefik_v2
    command:
      - "--log.level=DEBUG"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.storage=/acme.json"
    ports:
      - 80:80
      - 443:443
    networks:
      - web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${CONTAINERPATH}/traefik.yaml:/traefik.yaml
      - ${CONTAINERPATH}/acme.json:/acme.json
      - ${CONTAINERPATH}/credentials:/credentials
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.admin.entrypoints=web"
      - "traefik.http.routers.admin.rule=Host(`traefik.copex.io`)"
      - "traefik.http.routers.admin.middlewares=adminAuth"
      - "traefik.http.routers.admin.service=api@internal"

networks:
  web:
    external: true
