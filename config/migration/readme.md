M2 Data Migration
=============
- Symlink config/migration/config.xml to vendor/magento/data-migration-tool/etc/config.xml
- Symlink config/migration/class-map.xml to vendor/magento/data-migration-tool/etc/class-map.xml
- bin/magento migrate:settings /var/www/htdocs/vendor/magento/data-migration-tool/etc/config.xml
- magento m migrate:data --auto /var/www/htdocs/vendor/magento/data-migration-tool/etc/config.xml

--
- Edit file src/Migration/Step/PostProcessing/Model/ProductsInRootCatalog.php and change line 59 to
                ['product_id']

--

- New data gets moved to the m2 system by: % bin/magento migrate:delta /var/www/htdocs/vendor/magento/data-migration-tool/etc/config.xml
  <br/>This data runs periodically
